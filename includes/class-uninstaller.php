<?php

namespace EFFE_Plugin;

/**
 * Fired during plugin uninstall.
 *
 * @since      1.0.0
 * @package    Event_Feed_For_Eventbrite
 * @subpackage Event_Feed_For_Eventbrite/includes
 * <AUTHOR> Plugins <https://www.bohemiaplugins.com>
 */
class Event_Feed_For_Eventbrite_Uninstaller {

	/**
	 * Uninstall.
	 *
	 * @since    1.0.0
     * @access   public
	 */
	public static function uninstall() {

        if ( ! current_user_can( 'activate_plugins' ) ) {
			return;
		}
		
        $advanced_settings = get_option( EFFE_PLUGIN_NAME . '-advanced' );
        $delete_data = $advanced_settings['delete_data'];

        // If user specified in the settings, delete all plugin's data
        if( $delete_data == true ) {

            // Delete plugin options
            delete_option( EFFE_PLUGIN_NAME );
            delete_option( EFFE_PLUGIN_NAME  . '-appearance' );
            delete_option( EFFE_PLUGIN_NAME  . '-formats' );
            delete_option( EFFE_PLUGIN_NAME  . '-advanced' );

            // Get all event feeds 
            $feeds = get_posts( 'numberposts=-1&post_type=event_feed&post_status=any' );

            foreach( $feeds as $feed ) {

                // Delete posts
                wp_delete_post( $feed->ID, true );

                // Delete post meta
                $feed_meta = get_post_meta( $feed->ID );
                foreach( $feed_meta as $meta )  {
                    delete_post_meta( $feed->ID, $meta );
                }

            }

        }

    }

}
