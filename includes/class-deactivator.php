<?php

namespace EFFE_Plugin;

/**
 * Fired during plugin deactivation.
 *
 * @since      1.0.0
 * @package    Event_Feed_For_Eventbrite
 * @subpackage Event_Feed_For_Eventbrite/includes
 * <AUTHOR> Plugins <https://www.bohemiaplugins.com>
 */
class Event_Feed_For_Eventbrite_Deactivator {

	/**
	 * Deactivation.
	 *
	 * @since    1.0.0
	 * @access	 public
	 */
	public static function deactivate() {

		if ( ! current_user_can( 'activate_plugins' ) ) {
			return;
		}
   
		// Delete all plugin related transients.
		$transients = get_option( EFFE_PLUGIN_NAME . '-transients', array() );
		if ( $transients ) {
			foreach ( $transients as $transient ) {
				delete_transient( $transient );
			}
			delete_option( EFFE_PLUGIN_NAME . '-transients' );
		}
		
		// Unregister Event Feed post type, so the rules are no longer in memory.
		$feed = new Event_Feed_For_Eventbrite_Feed();
		$feed->unregister_event_feed_post_type();

	}

}
