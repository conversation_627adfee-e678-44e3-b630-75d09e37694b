<?php

namespace EFFE_Plugin;

/**
 * The class responsible for registering custom post type, post meta and getting information 
 * about Event Feed
 *
 * @since      1.0.0
 * @package    Event_Feed_For_Eventbrite
 * @subpackage Event_Feed_For_Eventbrite/includes
 * <AUTHOR> Plugins <https://www.bohemiaplugins.com>
 */
class Event_Feed_For_Eventbrite_Feed {

    /**
    * The unique identifier of this plugin.
    *
    * @since    1.0.0
    * @access   protected
    * @var      string    $plugin_name    The string used to uniquely identify this plugin.
    */
    protected $plugin_name;

    /**
    * The slug of the created post type.
    *
    * @since    1.0.0
    * @access   protected
    * @var      string    $post_type_slug    The slug of the created post type.
    */
    protected $post_type_slug;

    /**
	 * Current Event Feed ID.
	 * 
	 * @since    1.0.0
	 * @access   protected
	 * @var      string     $feed_id     The ID of the Event Feed.
	 */
	protected $feed_id;

   /**
    * Define the core functionality of the plugin.
    *
    * @since    1.0.0
    * @access   public
    */
    public function __construct( $feed_id = '' ) {

        $this->plugin_name = EFFE_PLUGIN_NAME;
        $this->post_type_slug = 'event_feed';
        $this->feed_id = $feed_id;

    }

    /**
	 * Register event feed post type
	 *
	 * @since    1.0.0
     * @access   public
	 */
    public function register_event_feed_post_type() {

        $labels = array(
            'name'                  => esc_html__( 'Event Feeds', 'event-feed-for-eventbrite' ),
            'singular_name'         => esc_html__( 'Event Feed', 'event-feed-for-eventbrite' ),
            'menu_name'             => esc_html__( 'Eventbrite Events', 'event-feed-for-eventbrite' ),
            'name_admin_bar'        => esc_html__( 'Event Feed', 'event-feed-for-eventbrite' ),
            'add_new'               => esc_html__( 'Add New', 'event-feed-for-eventbrite' ),
            'add_new_item'          => esc_html__( 'Add New Event Feed', 'event-feed-for-eventbrite' ),
            'new_item'              => esc_html__( 'New Event Feed', 'event-feed-for-eventbrite' ),
            'edit_item'             => esc_html__( 'Edit Event Feed', 'event-feed-for-eventbrite' ),
            'view_item'             => esc_html__( 'View Event Feed', 'event-feed-for-eventbrite' ),
            'all_items'             => esc_html__( 'Event Feeds', 'event-feed-for-eventbrite' ),
            'search_items'          => esc_html__( 'Search Event Feeds', 'event-feed-for-eventbrite' ),
            'parent_item_colon'     => esc_html__( 'Parent Event Feeds:', 'event-feed-for-eventbrite' ),
            'not_found'             => esc_html__( 'No Event Feeds found.', 'event-feed-for-eventbrite' ),
            'not_found_in_trash'    => esc_html__( 'No Event Feeds found in Trash.', 'event-feed-for-eventbrite' ),
            'archives'              => esc_html__( 'Event Feed archives', 'event-feed-for-eventbrite' ),
            'insert_into_item'      => esc_html__( 'Insert into Event Feed', 'event-feed-for-eventbrite' ),
            'uploaded_to_this_item' => esc_html__( 'Uploaded to this Event Feed', 'event-feed-for-eventbrite' ),
            'filter_items_list'     => esc_html__( 'Filter Event Feeds list', 'event-feed-for-eventbrite' ),
            'items_list_navigation' => esc_html__( 'Event Feeds list navigation', 'event-feed-for-eventbrite' ),
            'items_list'            => esc_html__( 'Event Feeds list', 'event-feed-for-eventbrite' )
        );
     
        $args = array(
            'labels'             => $labels,
            'public'             => false,
            'publicly_queryable' => true,
            'show_ui'            => true,
            'show_in_menu'       => true,
            'show_in_nav_menus'  => true,
            'show_in_rest'       => true,
            'query_var'          => true,
            'rewrite'            => false,
            'capability_type'    => 'post',
            'has_archive'        => false,
            'hierarchical'       => false,
            'menu_position'      => 21,
            'menu_icon'          => 'dashicons-calendar-alt',
            'supports'           => array( 'title' ),
        );
     
        register_post_type( $this->post_type_slug, $args );
        
    }

    /**
	 * Unregister event feed post type
	 *
	 * @since    1.0.0
     * @access   public
	 */
    public function unregister_event_feed_post_type() {
        
        unregister_post_type( $this->post_type_slug);

    }

    /**
	 * Change default title text in new post admin screen
	 *
	 * @since    1.0.0
     * @access   public
	 */
    public function event_feed_change_title_text( $title ) {
        
        $screen = get_current_screen();
     
        if  ( $this->post_type_slug == $screen->post_type ) {
            $title = esc_html__( 'Name of your event feed', 'event-feed-for-eventbrite' );
        }
     
        return $title;
    }

    /**
	 * Register event feed post meta
	 *
	 * @since    1.0.0
     * @access   public
	 */
    public function register_event_feed_post_meta() {

        // Layout type
        register_post_meta( $this->post_type_slug, 'layout_type', array(
            'type' => 'string',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );
        
        // Number of rows in grid layout - desktop
        register_post_meta( $this->post_type_slug, 'rows', array(
            'type' => 'number',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Number of rows in grid layout - large tablet
        register_post_meta( $this->post_type_slug, 'rows_large_tablet', array(
            'type' => 'number',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Number of rows in grid layout - small tablet
        register_post_meta( $this->post_type_slug, 'rows_small_tablet', array(
            'type' => 'number',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Number of rows in grid layout - mobile
        register_post_meta( $this->post_type_slug, 'rows_mobile', array(
            'type' => 'number',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Number of events displayed - desktop
        register_post_meta( $this->post_type_slug, 'events_limit', array(
            'type' => 'number',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // No limit of events displayed - desktop
        register_post_meta( $this->post_type_slug, 'events_limit_no', array(
            'type' => 'boolean',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Number of events displayed - large tablet
        register_post_meta( $this->post_type_slug, 'events_limit_large_tablet', array(
            'type' => 'number',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // No limit of events displayed - large tablet
        register_post_meta( $this->post_type_slug, 'events_limit_no_large_tablet', array(
            'type' => 'boolean',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Number of events displayed - small tablet
        register_post_meta( $this->post_type_slug, 'events_limit_small_tablet', array(
            'type' => 'number',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // No limit of events displayed - small tablet
        register_post_meta( $this->post_type_slug, 'events_limit_no_small_tablet', array(
            'type' => 'boolean',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Number of events displayed - mobile
        register_post_meta( $this->post_type_slug, 'events_limit_mobile', array(
            'type' => 'number',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // No limit of events displayed - mobile
        register_post_meta( $this->post_type_slug, 'events_limit_no_mobile', array(
            'type' => 'boolean',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Display image
        register_post_meta( $this->post_type_slug, 'display_image', array(
            'type' => 'boolean',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Display price
        register_post_meta( $this->post_type_slug, 'display_price', array(
            'type' => 'boolean',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Display title
        register_post_meta( $this->post_type_slug, 'display_title', array(
            'type' => 'boolean',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Display short date and time
        register_post_meta( $this->post_type_slug, 'display_short_datetime', array(
            'type' => 'boolean',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Display full date and time
        register_post_meta( $this->post_type_slug, 'display_datetime', array(
            'type' => 'boolean',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Display location
        register_post_meta( $this->post_type_slug, 'display_location', array(
            'type' => 'boolean',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Display description
        register_post_meta( $this->post_type_slug, 'display_description', array(
            'type' => 'boolean',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Display sign up button
        register_post_meta( $this->post_type_slug, 'display_signup_button', array(
            'type' => 'boolean',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Display read more button
        register_post_meta( $this->post_type_slug, 'display_more_button', array(
            'type' => 'boolean',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Theme color
        register_post_meta( $this->post_type_slug, 'theme_color', array(
            'type' => 'string',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Title length
        register_post_meta( $this->post_type_slug, 'title_length', array(
            'type' => 'number',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Title length full
        register_post_meta( $this->post_type_slug, 'title_length_full', array(
            'type' => 'boolean',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Excerpt length
        register_post_meta( $this->post_type_slug, 'excerpt_length', array(
            'type' => 'number',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Excerpt length full
        register_post_meta( $this->post_type_slug, 'excerpt_length_full', array(
            'type' => 'boolean',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Display price as image overlay
        register_post_meta( $this->post_type_slug, 'price_overlay', array(
            'type' => 'boolean',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Buy tickets button text
        register_post_meta( $this->post_type_slug, 'tickets_button_text', array(
            'type' => 'string',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Sign up button text
        register_post_meta( $this->post_type_slug, 'signup_button_text', array(
            'type' => 'string',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );
        
        // Sign up button background color
        register_post_meta( $this->post_type_slug, 'signup_button_bg_color', array(
            'type' => 'string',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );
            
        // Sign up button text color
        register_post_meta( $this->post_type_slug, 'signup_button_text_color', array(
            'type' => 'string',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );
                
        // Read more button text
        register_post_meta( $this->post_type_slug, 'more_button_text', array(
            'type' => 'string',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Organization
        register_post_meta( $this->post_type_slug, 'organization', array(
            'type' => 'string',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Time filter
        register_post_meta( $this->post_type_slug, 'time_filter', array(
            'type' => 'string',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Display public events
        register_post_meta( $this->post_type_slug, 'display_public', array(
            'type' => 'boolean',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Display private events
        register_post_meta( $this->post_type_slug, 'display_private', array(
            'type' => 'boolean',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Display events with venue
        register_post_meta( $this->post_type_slug, 'display_venue', array(
            'type' => 'boolean',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Display online events
        register_post_meta( $this->post_type_slug, 'display_online', array(
            'type' => 'boolean',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Name filter
        register_post_meta( $this->post_type_slug, 'name_filter', array(
            'type' => 'string',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Location filter
        register_post_meta( $this->post_type_slug, 'location_filter', array(
            'type' => 'string',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Description filter
        register_post_meta( $this->post_type_slug, 'description_filter', array(
            'type' => 'string',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );
        
        // Organizer filter
        register_post_meta( $this->post_type_slug, 'organizer_filter', array(
            'type' => 'string',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Sign up behaviour
        register_post_meta( $this->post_type_slug, 'link_to', array(
            'type' => 'string',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Open links in
        register_post_meta( $this->post_type_slug, 'link_target_blank', array(
            'type' => 'boolean',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // Show popup
        register_post_meta( $this->post_type_slug, 'popup', array(
            'type' => 'boolean',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

		// Show add to calendar button
        register_post_meta( $this->post_type_slug, 'calendar_button', array(
            'type' => 'boolean',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

		// Show map
        register_post_meta( $this->post_type_slug, 'google_map', array(
            'type' => 'boolean',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

		// Show organizer
        register_post_meta( $this->post_type_slug, 'organizer_info', array(
            'type' => 'boolean',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

		// Show link to Eventbrite
        register_post_meta( $this->post_type_slug, 'eventbrite_link', array(
            'type' => 'boolean',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // CSS ID
        register_post_meta( $this->post_type_slug, 'css_id', array(
            'type' => 'string',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

        // CSS classes
        register_post_meta( $this->post_type_slug, 'css_classes', array(
            'type' => 'string',
            'single' => true,
            'show_in_rest' => true,
            'sanitize_callback' => function( $value ) {
                return wp_strip_all_tags( $value );
            }
        ) );

    }

    /**
	 * Get event feed object
	 *
	 * @since    1.0.0
	 * @access   public
	 * @param    integer       $feed_id     Post ID.
	 * @return   object                     Event Feed with specified keys.
	 */
	public function get_event_feed_data() {

		if ( isset( $this->feed_id ) && ! empty( $this->feed_id ) ) {
			$feed = $this->map_feed_keys( $this->feed_id );
            return (object) $feed;
		} else {
            return false;
        }

	}

    /**
	 * Convert post meta to Event Feed object.
	 * 
	 * @since    1.0.0
	 * @access   protected     
	 * @param    integer       $feed_id     Post ID.
	 * @return   object                     Event Feed with specified keys.
	 */
	protected function map_feed_keys( $feed_id ) {

		$feed = array();

		$feed['ID']                             = $feed_id;
		$feed['layout']                         = get_post_meta( $feed_id, 'layout_type', true );
		$feed['rows']                           = get_post_meta( $feed_id, 'rows', true );
		$feed['rows_large_tablet']              = get_post_meta( $feed_id, 'rows_large_tablet', true );
		$feed['rows_small_tablet']              = get_post_meta( $feed_id, 'rows_small_tablet', true );
		$feed['rows_mobile']                    = get_post_meta( $feed_id, 'rows_mobile', true );
		$feed['events_limit']                   = get_post_meta( $feed_id, 'events_limit', true );
		$feed['events_limit_no']                = get_post_meta( $feed_id, 'events_limit_no', true );
		$feed['events_limit_large_tablet']      = get_post_meta( $feed_id, 'events_limit_large_tablet', true );
		$feed['events_limit_no_large_tablet']   = get_post_meta( $feed_id, 'events_limit_no_large_tablet', true );
		$feed['events_limit_small_tablet']      = get_post_meta( $feed_id, 'events_limit_small_tablet', true );
		$feed['events_limit_no_small_tablet']   = get_post_meta( $feed_id, 'events_limit_no_small_tablet', true );
		$feed['events_limit_mobile']            = get_post_meta( $feed_id, 'events_limit_mobile', true );
		$feed['events_limit_no_mobile']         = get_post_meta( $feed_id, 'events_limit_no_mobile', true );
		$feed['display_image']                  = get_post_meta( $feed_id, 'display_image', true );
		$feed['display_price']                  = get_post_meta( $feed_id, 'display_price', true );
		$feed['display_tickets']                = get_post_meta( $feed_id, 'display_tickets', true );
		$feed['display_title']                  = get_post_meta( $feed_id, 'display_title', true );
		$feed['display_short_datetime']         = get_post_meta( $feed_id, 'display_short_datetime', true );
		$feed['display_datetime']               = get_post_meta( $feed_id, 'display_datetime', true );
		$feed['display_location']               = get_post_meta( $feed_id, 'display_location', true );
		$feed['display_description']            = get_post_meta( $feed_id, 'display_description', true );
		$feed['display_signup_button']          = get_post_meta( $feed_id, 'display_signup_button', true );
		$feed['display_more_button']            = get_post_meta( $feed_id, 'display_more_button', true );
		$feed['theme_color']                    = get_post_meta( $feed_id, 'theme_color', true );
		$feed['title_length']                   = get_post_meta( $feed_id, 'title_length', true );
		$feed['title_length_full']              = get_post_meta( $feed_id, 'title_length_full', true );
		$feed['excerpt_length']                 = get_post_meta( $feed_id, 'excerpt_length', true );
		$feed['excerpt_length_full']            = get_post_meta( $feed_id, 'excerpt_length_full', true );
		$feed['price_overlay']                  = get_post_meta( $feed_id, 'price_overlay', true );
		$feed['tickets_text']                   = get_post_meta( $feed_id, 'tickets_text', true );
		$feed['signup_button_text']             = get_post_meta( $feed_id, 'signup_button_text', true );
		$feed['tickets_button_text']            = get_post_meta( $feed_id, 'tickets_button_text', true );
		$feed['more_button_text']               = get_post_meta( $feed_id, 'more_button_text', true );
		$feed['organization']                   = get_post_meta( $feed_id, 'organization', true );
		$feed['time_filter']                    = get_post_meta( $feed_id, 'time_filter', true );
		$feed['display_public']                 = get_post_meta( $feed_id, 'display_public', true );
		$feed['display_private']                = get_post_meta( $feed_id, 'display_private', true );
		$feed['display_venue']                  = get_post_meta( $feed_id, 'display_venue', true );
		$feed['display_online']                 = get_post_meta( $feed_id, 'display_online', true );
		$feed['name_filter']                    = get_post_meta( $feed_id, 'name_filter', true );
		$feed['location_filter']                = get_post_meta( $feed_id, 'location_filter', true );
		$feed['description_filter']             = get_post_meta( $feed_id, 'description_filter', true );
		$feed['organizer_filter']               = get_post_meta( $feed_id, 'organizer_filter', true );
		$feed['link_to']                        = get_post_meta( $feed_id, 'link_to', true );
		$feed['link_target_blank']              = get_post_meta( $feed_id, 'link_target_blank', true );
		$feed['popup']                          = get_post_meta( $feed_id, 'popup', true );
		$feed['calendar_button']                = get_post_meta( $feed_id, 'calendar_button', true );
		$feed['google_map']                     = get_post_meta( $feed_id, 'google_map', true );
		$feed['organizer_info']                 = get_post_meta( $feed_id, 'organizer_info', true );
		$feed['eventbrite_link']                = get_post_meta( $feed_id, 'eventbrite_link', true );
		$feed['css_id']                         = get_post_meta( $feed_id, 'css_id', true );
		$feed['css_classes']                    = get_post_meta( $feed_id, 'css_classes', true );

		return (object) $feed;

	}

    
	/**
	 * Get list of published event feeds
	 *
	 * @since    1.0.0
	 * @access   public
	 */
	public function get_event_feeds() {
		
        $feeds = get_posts( array( 'numberposts' => -1, 'post_type' => 'event_feed' ) );
        $feeds_array = array();

        foreach( $feeds as $feed ) {
            array_push( $feeds_array, array( 'ID' => $feed->ID, 'name' => $feed->post_title ) );
        }

        return (object) array_values( $feeds_array );

	}

}