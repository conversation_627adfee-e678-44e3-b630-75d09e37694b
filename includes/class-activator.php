<?php

namespace EFFE_Plugin;

/**
 * Fired during plugin activation.
 *
 * @since      1.0.0
 * @package    Event_Feed_For_Eventbrite
 * @subpackage Event_Feed_For_Eventbrite/includes
 * <AUTHOR> Plugins <https://www.bohemiaplugins.com>
 */
class Event_Feed_For_Eventbrite_Activator {

	/**
	 * Activation.
	 *
	 * @since    1.0.0
	 * @access	 public
	 */
	public static function activate() {

		if( ! current_user_can( 'activate_plugins' ) ) {
			return;
		}

		// Add default option to respect Eventbrite timezone
		if( ! get_option( 'event-feed-for-eventbrite' ) && ! get_option( 'event-feed-for-eventbrite-formats' ) ) {
			$default_format_option = array(
				'time_format' => false,
				'date_format_custom' => 'F j, Y',
				'address_format' => '[localized_address_display]',
				'eventbrite_timezone' => true,
				'shortdate_months' => false,
			);
			update_option( 'event-feed-for-eventbrite-formats', $default_format_option, true );
		}
		
    }

}
