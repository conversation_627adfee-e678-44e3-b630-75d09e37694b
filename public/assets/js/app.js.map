{"version": 3, "file": "app.js", "mappings": "gBAAIA,EACAC,ECAEC,EAIAC,EAKAC,EAIAC,EAOAC,E,gCCrBNC,EAAOC,QAAUC,MCCbC,EAA2B,GAG/B,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAaL,QAGrB,IAAID,EAASG,EAAyBE,GAAY,CAGjDJ,QAAS,IAOV,OAHAO,EAAoBH,GAAUL,EAAQA,EAAOC,QAASG,GAG/CJ,EAAOC,QAIfG,EAAoBK,EAAID,ECxBxBJ,EAAoBM,EAAI,SAASV,GAChC,IAAIW,EAASX,GAAUA,EAAOY,WAC7B,WAAa,OAAOZ,EAAgB,SACpC,WAAa,OAAOA,GAErB,OADAI,EAAoBS,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,GCLRP,EAAoBS,EAAI,SAASZ,EAASc,GACzC,IAAI,IAAIC,KAAOD,EACXX,EAAoBa,EAAEF,EAAYC,KAASZ,EAAoBa,EAAEhB,EAASe,IAC5EE,OAAOC,eAAelB,EAASe,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,MCJ3EZ,EAAoBkB,EAAI,GAGxBlB,EAAoBmB,EAAI,SAASC,GAChC,OAAOC,QAAQC,IAAIR,OAAOS,KAAKvB,EAAoBkB,GAAGM,QAAO,SAASC,EAAUb,GAE/E,OADAZ,EAAoBkB,EAAEN,GAAKQ,EAASK,GAC7BA,IACL,MCNJzB,EAAoB0B,EAAI,SAASN,GAEhC,OAAa,CAAC,GAAK,QAAQ,GAAK,OAAO,IAAM,SAAS,IAAM,QAAQA,IAAYA,GAAW,OCH5FpB,EAAoB2B,EAAI,WACvB,GAA0B,iBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAOC,MAAQ,IAAIC,SAAS,cAAb,GACd,MAAOX,GACR,GAAsB,iBAAXY,OAAqB,OAAOA,QALjB,GCAxB/B,EAAoBa,EAAI,SAASmB,EAAKC,GAAQ,OAAOnB,OAAOoB,UAAUC,eAAeC,KAAKJ,EAAKC,ITA3F5C,EAAa,GACbC,EAAoB,6BAExBU,EAAoBqC,EAAI,SAASC,EAAKC,EAAM3B,EAAKQ,GAChD,GAAG/B,EAAWiD,GAAQjD,EAAWiD,GAAKE,KAAKD,OAA3C,CACA,IAAIE,EAAQC,EACZ,QAAWvC,IAARS,EAEF,IADA,IAAI+B,EAAUC,SAASC,qBAAqB,UACpCC,EAAI,EAAGA,EAAIH,EAAQI,OAAQD,IAAK,CACvC,IAAIE,EAAIL,EAAQG,GAChB,GAAGE,EAAEC,aAAa,QAAUX,GAAOU,EAAEC,aAAa,iBAAmB3D,EAAoBsB,EAAK,CAAE6B,EAASO,EAAG,OAG1GP,IACHC,GAAa,GACbD,EAASG,SAASM,cAAc,WAEzBC,QAAU,QACjBV,EAAOW,QAAU,IACbpD,EAAoBqD,IACvBZ,EAAOa,aAAa,QAAStD,EAAoBqD,IAElDZ,EAAOa,aAAa,eAAgBhE,EAAoBsB,GACxD6B,EAAOc,IAAMjB,GAEdjD,EAAWiD,GAAO,CAACC,GACnB,IAAIiB,EAAmB,SAASC,EAAMC,GAErCjB,EAAOkB,QAAUlB,EAAOmB,OAAS,KACjCC,aAAaT,GACb,IAAIU,EAAUzE,EAAWiD,GAIzB,UAHOjD,EAAWiD,GAClBG,EAAOsB,YAActB,EAAOsB,WAAWC,YAAYvB,GACnDqB,GAAWA,EAAQG,SAAQ,SAASC,GAAM,OAAOA,EAAGR,MACjDD,EAAM,OAAOA,EAAKC,IAGlBN,EAAUe,WAAWX,EAAiBY,KAAK,UAAMjE,EAAW,CAAEkE,KAAM,UAAWC,OAAQ7B,IAAW,MACtGA,EAAOkB,QAAUH,EAAiBY,KAAK,KAAM3B,EAAOkB,SACpDlB,EAAOmB,OAASJ,EAAiBY,KAAK,KAAM3B,EAAOmB,QACnDlB,GAAcE,SAAS2B,KAAKC,YAAY/B,KUvCzCzC,EAAoByE,EAAI,SAAS5E,GACX,oBAAX6E,QAA0BA,OAAOC,aAC1C7D,OAAOC,eAAelB,EAAS6E,OAAOC,YAAa,CAAEC,MAAO,WAE7D9D,OAAOC,eAAelB,EAAS,aAAc,CAAE+E,OAAO,K,WCLvD,IAAIC,EACA7E,EAAoB2B,EAAEmD,gBAAeD,EAAY7E,EAAoB2B,EAAEoD,SAAW,IACtF,IAAInC,EAAW5C,EAAoB2B,EAAEiB,SACrC,IAAKiC,GAAajC,IACbA,EAASoC,gBACZH,EAAYjC,EAASoC,cAAczB,MAC/BsB,GAAW,CACf,IAAIlC,EAAUC,EAASC,qBAAqB,UACzCF,EAAQI,SAAQ8B,EAAYlC,EAAQA,EAAQI,OAAS,GAAGQ,KAK7D,IAAKsB,EAAW,MAAM,IAAII,MAAM,yDAChCJ,EAAYA,EAAUK,QAAQ,OAAQ,IAAIA,QAAQ,QAAS,IAAIA,QAAQ,YAAa,KACpFlF,EAAoBmF,EAAIN,E,cCVxB,IAAIO,EAAkB,CACrB,IAAK,GAGNpF,EAAoBkB,EAAEmE,EAAI,SAASjE,EAASK,GAE1C,IAAI6D,EAAqBtF,EAAoBa,EAAEuE,EAAiBhE,GAAWgE,EAAgBhE,QAAWjB,EACtG,GAA0B,IAAvBmF,EAGF,GAAGA,EACF7D,EAASe,KAAK8C,EAAmB,QAC3B,CAGL,IAAIC,EAAU,IAAIlE,SAAQ,SAASmE,EAASC,GAAUH,EAAqBF,EAAgBhE,GAAW,CAACoE,EAASC,MAChHhE,EAASe,KAAK8C,EAAmB,GAAKC,GAGtC,IAAIjD,EAAMtC,EAAoBmF,EAAInF,EAAoB0B,EAAEN,GAEpDsE,EAAQ,IAAIT,MAgBhBjF,EAAoBqC,EAAEC,GAfH,SAASoB,GAC3B,GAAG1D,EAAoBa,EAAEuE,EAAiBhE,KAEf,KAD1BkE,EAAqBF,EAAgBhE,MACRgE,EAAgBhE,QAAWjB,GACrDmF,GAAoB,CACtB,IAAIK,EAAYjC,IAAyB,SAAfA,EAAMW,KAAkB,UAAYX,EAAMW,MAChEuB,EAAUlC,GAASA,EAAMY,QAAUZ,EAAMY,OAAOf,IACpDmC,EAAMG,QAAU,iBAAmBzE,EAAU,cAAgBuE,EAAY,KAAOC,EAAU,IAC1FF,EAAMI,KAAO,iBACbJ,EAAMrB,KAAOsB,EACbD,EAAMK,QAAUH,EAChBN,EAAmB,GAAGI,MAIgB,SAAWtE,EAASA,KAiBlE,IAAI4E,EAAuB,SAASC,EAA4BC,GAC/D,IAKIjG,EAAUmB,EALV+E,EAAWD,EAAK,GAChBE,EAAcF,EAAK,GACnBG,EAAUH,EAAK,GAGIpD,EAAI,EAC3B,GAAGqD,EAASG,MAAK,SAASC,GAAM,OAA+B,IAAxBnB,EAAgBmB,MAAe,CACrE,IAAItG,KAAYmG,EACZpG,EAAoBa,EAAEuF,EAAanG,KACrCD,EAAoBK,EAAEJ,GAAYmG,EAAYnG,IAGhD,GAAGoG,EAAsBA,EAAQrG,GAGlC,IADGiG,GAA4BA,EAA2BC,GACrDpD,EAAIqD,EAASpD,OAAQD,IACzB1B,EAAU+E,EAASrD,GAChB9C,EAAoBa,EAAEuE,EAAiBhE,IAAYgE,EAAgBhE,IACrEgE,EAAgBhE,GAAS,KAE1BgE,EAAgBe,EAASrD,IAAM,GAK7B0D,EAAqBC,KAA4C,sCAAIA,KAA4C,uCAAK,GAC1HD,EAAmBvC,QAAQ+B,EAAqB5B,KAAK,KAAM,IAC3DoC,EAAmBhE,KAAOwD,EAAqB5B,KAAK,KAAMoC,EAAmBhE,KAAK4B,KAAKoC,I,GXtFjFjH,EAAO,kBAAM,oDAIbC,EAAS,kBAAM,sDAKfC,EAAO,kBAAM,qDAIbC,EAAQ,kBAAM,qDAOdC,EAAQiD,SAAS8D,uBAAuB,iCAC9C,GAAGzC,QAAQ7B,KAAMzC,GAAO,SAAWgH,GAGlC7G,IAAIoC,UAAU0E,WAAa9E,SAAS,gCAAkC6E,EAAQE,QAAQC,IAA3DhF,GAG3BhC,IAAIoC,UAAU6E,WAAaxH,EAGiB,QAAxCO,IAAIoC,UAAU0E,WAAWI,KAAKC,SACjCnH,IAAIoC,UAAU6E,WAAaxH,GAEgB,UAAxCO,IAAIoC,UAAU0E,WAAWI,KAAKC,SACjCnH,IAAIoC,UAAU6E,WAAavH;AAG5B,IAAI0H,EAA8C,SAAnCC,uBAAuBD,QAClCE,EAAwC,SAAhCD,uBAAuBC,KACpB,GAAXF,IAA4B,IAATE,IACsB,QAAxCtH,IAAIoC,UAAU0E,WAAWI,KAAKC,SACjCnH,IAAIoC,UAAU6E,WAAatH,GAEgB,SAAxCK,IAAIoC,UAAU0E,WAAWI,KAAKC,SACjCnH,IAAIoC,UAAU6E,WAAarH;AAM7B,IAAII,IAAK,CACFuH,GAAI,IAAMV,EAAQJ,GACxBe,WAAY,CACX,OAAUxH,IAAIoC,UAAU6E,YAEzBb,KAAM,WACI,MAAO,CAGfqB,WAAY1F,KAAK+E,WAAWE,IAChBU,YAAa3F,KAAK+E,WAAWI,KAC7BS,gBAAiB5F,KAAK+E,WAAWc,SACjCC,WAAY9F,KAAK+E,WAAWgB,W", "sources": ["webpack://event-feed-for-eventbrite/webpack/runtime/load script", "webpack://event-feed-for-eventbrite/./public/src/js/app.js", "webpack://event-feed-for-eventbrite/external var \"Vue\"", "webpack://event-feed-for-eventbrite/webpack/bootstrap", "webpack://event-feed-for-eventbrite/webpack/runtime/compat get default export", "webpack://event-feed-for-eventbrite/webpack/runtime/define property getters", "webpack://event-feed-for-eventbrite/webpack/runtime/ensure chunk", "webpack://event-feed-for-eventbrite/webpack/runtime/get javascript chunk filename", "webpack://event-feed-for-eventbrite/webpack/runtime/global", "webpack://event-feed-for-eventbrite/webpack/runtime/hasOwnProperty shorthand", "webpack://event-feed-for-eventbrite/webpack/runtime/make namespace object", "webpack://event-feed-for-eventbrite/webpack/runtime/publicPath", "webpack://event-feed-for-eventbrite/webpack/runtime/jsonp chunk loading"], "sourcesContent": ["var inProgress = {};\nvar dataWebpackPrefix = \"event-feed-for-eventbrite:\";\n// loadScript function to load a script via script tag\n__webpack_require__.l = function(url, done, key, chunkId) {\n\tif(inProgress[url]) { inProgress[url].push(done); return; }\n\tvar script, needAttach;\n\tif(key !== undefined) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tfor(var i = 0; i < scripts.length; i++) {\n\t\t\tvar s = scripts[i];\n\t\t\tif(s.getAttribute(\"src\") == url || s.getAttribute(\"data-webpack\") == dataWebpackPrefix + key) { script = s; break; }\n\t\t}\n\t}\n\tif(!script) {\n\t\tneedAttach = true;\n\t\tscript = document.createElement('script');\n\n\t\tscript.charset = 'utf-8';\n\t\tscript.timeout = 120;\n\t\tif (__webpack_require__.nc) {\n\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n\t\t}\n\t\tscript.setAttribute(\"data-webpack\", dataWebpackPrefix + key);\n\t\tscript.src = url;\n\t}\n\tinProgress[url] = [done];\n\tvar onScriptComplete = function(prev, event) {\n\t\t// avoid mem leaks in IE.\n\t\tscript.onerror = script.onload = null;\n\t\tclearTimeout(timeout);\n\t\tvar doneFns = inProgress[url];\n\t\tdelete inProgress[url];\n\t\tscript.parentNode && script.parentNode.removeChild(script);\n\t\tdoneFns && doneFns.forEach(function(fn) { return fn(event); });\n\t\tif(prev) return prev(event);\n\t}\n\t;\n\tvar timeout = setTimeout(onScriptComplete.bind(null, undefined, { type: 'timeout', target: script }), 120000);\n\tscript.onerror = onScriptComplete.bind(null, script.onerror);\n\tscript.onload = onScriptComplete.bind(null, script.onload);\n\tneedAttach && document.head.appendChild(script);\n};", "// Import Event Feed components\nconst List = () => import(\n\t/* webpackChunkName: \"list\" */\n\t'./components/List.vue'\n);\nconst Widget = () => import(\n\t/* webpackChunkName: \"widget\" */\n\t'./components/Widget.vue'\n);\n//removeIf(!premium)\nconst Grid = () => import(\n\t/* webpackChunkName: \"grid\" */\n\t'./components/Grid.vue'\n);\nconst Cards = () => import(\n\t/* webpackChunkName: \"cards\" */\n\t'./components/Cards.vue'\n);\n//endRemoveIf(!premium)\n\n// Load Vue module for every feed on the page\nconst feeds = document.getElementsByClassName('event-feed-for-eventbrite-app');\n[].forEach.call( feeds, function ( element ) {\n\n\t// Get data from wp_localize_script by feed ID\n\tVue.prototype.scriptName = Function('return EventFeedForEventbrite' + element.dataset.uid)();\n\n\t// Set default layout\n\tVue.prototype.layoutType = List;\n\n\t// Get layout type from feed settings\n\tif( Vue.prototype.scriptName.feed.layout == 'list' ) { \n\t\tVue.prototype.layoutType = List\n\t}\n\tif( Vue.prototype.scriptName.feed.layout == 'widget' ) {\n\t\tVue.prototype.layoutType = Widget\n\t}\n\t//removeIf(!premium)\n\tvar premium = (EventFeedForEventbrite.premium === 'true');\n\tvar free = (EventFeedForEventbrite.free === 'true');\n\tif( premium == true && free !== true ) {\n\t\tif( Vue.prototype.scriptName.feed.layout == 'grid' ) {\n\t\t\tVue.prototype.layoutType = Grid\n\t\t}\n\t\tif( Vue.prototype.scriptName.feed.layout == 'cards' ) {\n\t\t\tVue.prototype.layoutType = Cards\n\t\t}\n\t}\n\t//endRemoveIf(!premium)\n\n\t// Vue instance\n\tnew Vue( {\n        el: '#' + element.id,\n\t\tcomponents: {\n\t\t\t'layout': Vue.prototype.layoutType,\n\t\t},\n\t\tdata: function() {\n            return {\n\n\t\t\t\t// Get user data from WP admin\n\t\t\t\tinitialUid: this.scriptName.uid, \n                initialFeed: this.scriptName.feed,\n                initialSettings: this.scriptName.settings,\n                initialApi: this.scriptName.api,\n\n\t\t\t}\n\t\t}\n \t})\n\t \n}); ", "module.exports = Vue;", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.f = {};\n// This file contains only the entry chunk.\n// The chunk loading function for additional chunks\n__webpack_require__.e = function(chunkId) {\n\treturn Promise.all(Object.keys(__webpack_require__.f).reduce(function(promises, key) {\n\t\t__webpack_require__.f[key](chunkId, promises);\n\t\treturn promises;\n\t}, []));\n};", "// This function allow to reference async chunks\n__webpack_require__.u = function(chunkId) {\n\t// return url for filenames based on template\n\treturn \"\" + ({\"12\":\"cards\",\"93\":\"list\",\"263\":\"widget\",\"276\":\"grid\"}[chunkId] || chunkId) + \".js\";\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "var scriptUrl;\nif (__webpack_require__.g.importScripts) scriptUrl = __webpack_require__.g.location + \"\";\nvar document = __webpack_require__.g.document;\nif (!scriptUrl && document) {\n\tif (document.currentScript)\n\t\tscriptUrl = document.currentScript.src\n\tif (!scriptUrl) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tif(scripts.length) scriptUrl = scripts[scripts.length - 1].src\n\t}\n}\n// When supporting browsers where an automatic publicPath is not supported you must specify an output.publicPath manually via configuration\n// or pass an empty string (\"\") and set the __webpack_public_path__ variable from your code to use your own logic.\nif (!scriptUrl) throw new Error(\"Automatic publicPath is not supported in this browser\");\nscriptUrl = scriptUrl.replace(/#.*$/, \"\").replace(/\\?.*$/, \"\").replace(/\\/[^\\/]+$/, \"/\");\n__webpack_require__.p = scriptUrl;", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t143: 0\n};\n\n__webpack_require__.f.j = function(chunkId, promises) {\n\t\t// JSONP chunk loading for javascript\n\t\tvar installedChunkData = __webpack_require__.o(installedChunks, chunkId) ? installedChunks[chunkId] : undefined;\n\t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n\t\t\t// a Promise means \"currently loading\".\n\t\t\tif(installedChunkData) {\n\t\t\t\tpromises.push(installedChunkData[2]);\n\t\t\t} else {\n\t\t\t\tif(true) { // all chunks have JS\n\t\t\t\t\t// setup Promise in chunk cache\n\t\t\t\t\tvar promise = new Promise(function(resolve, reject) { installedChunkData = installedChunks[chunkId] = [resolve, reject]; });\n\t\t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n\t\t\t\t\t// start chunk loading\n\t\t\t\t\tvar url = __webpack_require__.p + __webpack_require__.u(chunkId);\n\t\t\t\t\t// create error before stack unwound to get useful stacktrace later\n\t\t\t\t\tvar error = new Error();\n\t\t\t\t\tvar loadingEnded = function(event) {\n\t\t\t\t\t\tif(__webpack_require__.o(installedChunks, chunkId)) {\n\t\t\t\t\t\t\tinstalledChunkData = installedChunks[chunkId];\n\t\t\t\t\t\t\tif(installedChunkData !== 0) installedChunks[chunkId] = undefined;\n\t\t\t\t\t\t\tif(installedChunkData) {\n\t\t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n\t\t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n\t\t\t\t\t\t\t\terror.message = 'Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n\t\t\t\t\t\t\t\terror.name = 'ChunkLoadError';\n\t\t\t\t\t\t\t\terror.type = errorType;\n\t\t\t\t\t\t\t\terror.request = realSrc;\n\t\t\t\t\t\t\t\tinstalledChunkData[1](error);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t\t__webpack_require__.l(url, loadingEnded, \"chunk-\" + chunkId, chunkId);\n\t\t\t\t} else installedChunks[chunkId] = 0;\n\t\t\t}\n\t\t}\n};\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n// no on chunks loaded\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = function(parentChunkLoadingFunction, data) {\n\tvar chunkIds = data[0];\n\tvar moreModules = data[1];\n\tvar runtime = data[2];\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some(function(id) { return installedChunks[id] !== 0; })) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkIds[i]] = 0;\n\t}\n\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkevent_feed_for_eventbrite\"] = self[\"webpackChunkevent_feed_for_eventbrite\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));"], "names": ["inProgress", "dataWebpackPrefix", "List", "Widget", "Grid", "Cards", "feeds", "module", "exports", "<PERSON><PERSON>", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "__webpack_modules__", "m", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "f", "e", "chunkId", "Promise", "all", "keys", "reduce", "promises", "u", "g", "globalThis", "this", "Function", "window", "obj", "prop", "prototype", "hasOwnProperty", "call", "l", "url", "done", "push", "script", "<PERSON><PERSON><PERSON><PERSON>", "scripts", "document", "getElementsByTagName", "i", "length", "s", "getAttribute", "createElement", "charset", "timeout", "nc", "setAttribute", "src", "onScriptComplete", "prev", "event", "onerror", "onload", "clearTimeout", "doneFns", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "fn", "setTimeout", "bind", "type", "target", "head", "append<PERSON><PERSON><PERSON>", "r", "Symbol", "toStringTag", "value", "scriptUrl", "importScripts", "location", "currentScript", "Error", "replace", "p", "installedChunks", "j", "installedChunkData", "promise", "resolve", "reject", "error", "errorType", "realSrc", "message", "name", "request", "webpackJsonpCallback", "parentChunkLoadingFunction", "data", "chunkIds", "moreModules", "runtime", "some", "id", "chunkLoadingGlobal", "self", "getElementsByClassName", "element", "scriptName", "dataset", "uid", "layoutType", "feed", "layout", "premium", "EventFeedForEventbrite", "free", "el", "components", "initialUid", "initialFeed", "initialSettings", "settings", "initialApi", "api"], "sourceRoot": ""}