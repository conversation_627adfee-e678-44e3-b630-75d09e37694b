!function(){var e,t,r,n,o,i,u,a={311:function(e){"use strict";e.exports=Vue}},c={};function p(e){var t=c[e];if(void 0!==t)return t.exports;var r=c[e]={exports:{}};return a[e](r,r.exports,p),r.exports}p.m=a,p.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return p.d(t,{a:t}),t},p.d=function(e,t){for(var r in t)p.o(t,r)&&!p.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},p.f={},p.e=function(e){return Promise.all(Object.keys(p.f).reduce((function(t,r){return p.f[r](e,t),t}),[]))},p.u=function(e){return({12:"cards",93:"list",263:"widget",276:"grid"}[e]||e)+".js"},p.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),p.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},e={},t="event-feed-for-eventbrite:",p.l=function(r,n,o,i){if(e[r])e[r].push(n);else{var u,a;if(void 0!==o)for(var c=document.getElementsByTagName("script"),l=0;l<c.length;l++){var s=c[l];if(s.getAttribute("src")==r||s.getAttribute("data-webpack")==t+o){u=s;break}}u||(a=!0,(u=document.createElement("script")).charset="utf-8",u.timeout=120,p.nc&&u.setAttribute("nonce",p.nc),u.setAttribute("data-webpack",t+o),u.src=r),e[r]=[n];var f=function(t,n){u.onerror=u.onload=null,clearTimeout(d);var o=e[r];if(delete e[r],u.parentNode&&u.parentNode.removeChild(u),o&&o.forEach((function(e){return e(n)})),t)return t(n)},d=setTimeout(f.bind(null,void 0,{type:"timeout",target:u}),12e4);u.onerror=f.bind(null,u.onerror),u.onload=f.bind(null,u.onload),a&&document.head.appendChild(u)}},p.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},function(){var e;p.g.importScripts&&(e=p.g.location+"");var t=p.g.document;if(!e&&t&&(t.currentScript&&(e=t.currentScript.src),!e)){var r=t.getElementsByTagName("script");r.length&&(e=r[r.length-1].src)}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),p.p=e}(),function(){var e={143:0};p.f.j=function(t,r){var n=p.o(e,t)?e[t]:void 0;if(0!==n)if(n)r.push(n[2]);else{var o=new Promise((function(r,o){n=e[t]=[r,o]}));r.push(n[2]=o);var i=p.p+p.u(t),u=new Error;p.l(i,(function(r){if(p.o(e,t)&&(0!==(n=e[t])&&(e[t]=void 0),n)){var o=r&&("load"===r.type?"missing":r.type),i=r&&r.target&&r.target.src;u.message="Loading chunk "+t+" failed.\n("+o+": "+i+")",u.name="ChunkLoadError",u.type=o,u.request=i,n[1](u)}}),"chunk-"+t,t)}};var t=function(t,r){var n,o,i=r[0],u=r[1],a=r[2],c=0;if(i.some((function(t){return 0!==e[t]}))){for(n in u)p.o(u,n)&&(p.m[n]=u[n]);if(a)a(p)}for(t&&t(r);c<i.length;c++)o=i[c],p.o(e,o)&&e[o]&&e[o][0](),e[i[c]]=0},r=self.webpackChunkevent_feed_for_eventbrite=self.webpackChunkevent_feed_for_eventbrite||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))}(),r=function(){return Promise.all([p.e(456),p.e(93)]).then(p.bind(p,12))},n=function(){return Promise.all([p.e(456),p.e(263)]).then(p.bind(p,354))},o=function(){return Promise.all([p.e(456),p.e(276)]).then(p.bind(p,79))},i=function(){return Promise.all([p.e(456),p.e(12)]).then(p.bind(p,243))},u=document.getElementsByClassName("event-feed-for-eventbrite-app"),[].forEach.call(u,(function(e){Vue.prototype.scriptName=Function("return EventFeedForEventbrite"+e.dataset.uid)(),Vue.prototype.layoutType=r,"list"==Vue.prototype.scriptName.feed.layout&&(Vue.prototype.layoutType=r),"widget"==Vue.prototype.scriptName.feed.layout&&(Vue.prototype.layoutType=n);//removeIf(!premium)
var t="true"===EventFeedForEventbrite.premium,u="true"===EventFeedForEventbrite.free;1==t&&!0!==u&&("grid"==Vue.prototype.scriptName.feed.layout&&(Vue.prototype.layoutType=o),"cards"==Vue.prototype.scriptName.feed.layout&&(Vue.prototype.layoutType=i)),//endRemoveIf(!premium)
new Vue({el:"#"+e.id,components:{layout:Vue.prototype.layoutType},data:function(){return{initialUid:this.scriptName.uid,initialFeed:this.scriptName.feed,initialSettings:this.scriptName.settings,initialApi:this.scriptName.api}}})}))}();
//# sourceMappingURL=app.js.map