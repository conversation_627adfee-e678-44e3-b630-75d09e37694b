{"version": 3, "file": "widget.js", "mappings": "uLAAA,IAAIA,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eACTC,EAAKJ,EAAIK,MAAMD,IAAMF,EACzB,OAAOE,EACL,MACA,CACEA,EACE,MACA,CACEE,WAAY,CACV,CACEC,KAAM,OACNC,QAAS,SACTC,MAAOT,EAAIU,SAASC,UAAYX,EAAIY,YAAcZ,EAAIa,WACtDC,WAAY,oDAGhBC,MACE,+DACAf,EAAIgB,KAER,CACEhB,EAAIiB,GAAG,GACPjB,EAAIkB,GAAG,KACPd,EAAG,MAAO,CAAEe,YAAa,iCAAmC,CAC1Df,EAAG,IAAK,CAACJ,EAAIkB,GAAGlB,EAAIoB,GAAGpB,EAAIqB,aAAaC,qBAI9CtB,EAAIkB,GAAG,KACPlB,EAAIU,SAASC,SAAWX,EAAIuB,QAAUvB,EAAIuB,OAAOC,OAAS,EACtDpB,EACE,MACA,CACEE,WAAY,CACV,CACEC,KAAM,OACNC,QAAS,SACTC,MAAOT,EAAIa,WACXC,WAAY,eAGhBC,MACE,sCACAf,EAAIyB,KAAKC,GACT,2BACA1B,EAAIyB,KAAKE,YACXC,MAAO,CAAEC,GAAI7B,EAAIyB,KAAKK,SAExB,CACE9B,EAAI+B,OAAS/B,EAAIgC,SACb5B,EACE,IACA,CACEe,YAAa,4BACbS,MAAO,CAAEK,OAAQ,SAAUC,KAAMlC,EAAIgC,WAEvC,CACE5B,EACE,MACA,CACE+B,YAAa,CAAE,oBAAqB,iBACpCP,MAAO,CACLQ,MAAO,6BACP,cAAe,+BACfC,KAAM,UACNC,MAAO,KACPC,OAAQ,KACRC,QAAS,MACTX,GAAI,qBACJY,EAAG,MACHC,EAAG,MACHC,QAAS,YACT,YAAa,aAGjB,CACEvC,EAAG,OAAQ,CACTwB,MAAO,CACLgB,EACE,8qBAKV5C,EAAIkB,GAAG,KACPd,EAAG,OAAQ,CACTJ,EAAIkB,GAAGlB,EAAIoB,GAAGpB,EAAIqB,aAAawB,qBAIrC7C,EAAI8C,KACR9C,EAAIkB,GAAG,KACPlB,EAAI+C,GAAG/C,EAAIuB,QAAQ,SAASyB,EAAOC,GACjC,OAAO7C,EACL,MACA,CACE8C,IAAKF,EAAMtB,GACXX,MAAO,CACL,kBACA,CACE,UACA,kBACA,cACA,WACA,eACAoC,SAASH,EAAMI,mBAAmBC,cAChCL,EAAMI,mBAAmBC,aACzB,IAENzB,MAAO,CACL,gBAAiBoB,EAAMtB,GACvB,mBAAoBuB,IAGxB,CACEjD,EAAIyB,KAAK6B,eAAiBN,EAAMO,IAC5BnD,EAAG,MAAO,CAAEe,YAAa,yBAA2B,CAClDf,EACE,MACA,CACEe,YAAa,8BACbqC,GAAI,CACFC,MAAO,SAASC,GACd,OAAO1D,EAAI2D,gBAAgBX,EAAOC,MAIxC,CACED,EAAMY,IAAIC,MACNzD,EAAG,MAAO,CACRwB,MAAO,CACLkC,IAAKd,EAAMY,IAAIC,MACfE,IAAKf,EAAMgB,WACX1B,MAAO,MACPC,OAAQ,MACR0B,QAAS,UAGbjE,EAAI8C,KACR9C,EAAIkB,GAAG,MACNlB,EAAIyB,KAAKyC,eACRlE,EAAIyB,KAAK0C,kBACXnE,EAAIyB,KAAK2C,cACLhE,EACE,OACA,CACEe,YAAa,8BAEf,CACEnB,EAAIyB,KAAKyC,eAAiBlB,EAAMY,IAAIS,MAChCjE,EAAG,OAAQ,CACTe,YACE,8BACFmD,SAAU,CACRC,UAAWvE,EAAIoB,GAAG4B,EAAMY,IAAIS,UAGhCrE,EAAI8C,KACR9C,EAAIkB,GAAG,KACPlB,EAAIyB,KAAK0C,iBACTnB,EAAMwB,mBACNxB,EAAMwB,kBAAoB,EACtBpE,EACE,OACA,CACEe,YACE,2CAEJ,CACEnB,EAAIkB,GACFlB,EAAIoB,GACF4B,EAAMwB,mBAENxE,EAAIoB,GACFpB,EAAIyE,YACFzE,EAAIyB,KAAKiD,kBAMrB1E,EAAI8C,OAGZ9C,EAAI8C,SAId9C,EAAI8C,KACR9C,EAAIkB,GAAG,KACPd,EAAG,MAAO,CAAEe,YAAa,2BAA6B,CACpDf,EAAG,MAAO,CAAEe,YAAa,2BAA6B,CACpDf,EACE,MACA,CAAEe,YAAa,6BACf,CACEnB,EAAIyB,KAAKkD,kBAAoB3B,EAAMY,IAAIgB,MACnCxE,EACE,OACA,CAAEe,YAAa,4BACf,CACEnB,EAAIkB,GACF,iCACElB,EAAIoB,GAAG4B,EAAMY,IAAIgB,OACjB,MACA5E,EAAIoB,GAAG4B,EAAMY,IAAIiB,KACjB,gCAIR7E,EAAI8C,KACR9C,EAAIkB,GAAG,KACP8B,EAAMY,IAAIkB,MACN1E,EACE,KACA,CACEe,YAAa,wBACbqC,GAAI,CACFC,MAAO,SAASC,GACd,OAAO1D,EAAI2D,gBACTX,EACAC,MAKR,CACEjD,EAAIkB,GACF,iCACElB,EAAIoB,GAAG4B,EAAMY,IAAIkB,OACjB,gCAIR9E,EAAI8C,SAId9C,EAAIkB,GAAG,KACPlB,EAAIyB,KAAKsD,kBACT/E,EAAIyB,KAAKuD,qBACRhF,EAAIyB,KAAKyC,gBACPlE,EAAIyB,KAAK6B,eACVN,EAAMY,IAAIS,OACZrE,EAAIyB,KAAKwD,uBACTjF,EAAIyB,KAAKyD,oBACL9E,EACE,MACA,CAAEe,YAAa,8BACf,CACEnB,EAAIyB,KAAKsD,iBACL3E,EACE,IACA,CAAEe,YAAa,4BACf,CACEf,EACE,OACA,CACEe,YACE,yCAEJ,CACyB,WAAvB6B,EAAMY,IAAIuB,SACN/E,EACE,MACA,CACEe,YACE,gCACFgB,YAAa,CACX,oBACE,iBAEJP,MAAO,CACLY,QAAS,MACTJ,MACE,6BACF,cACE,+BACFK,EAAG,MACHC,EAAG,MACHC,QAAS,YACT,YAAa,aAGjB,CACEvC,EAAG,IAAK,CACNA,EAAG,OAAQ,CACTwB,MAAO,CACLgB,EACE,iQAGN5C,EAAIkB,GAAG,KACPd,EAAG,OAAQ,CACTwB,MAAO,CACLgB,EACE,uLAMZ5C,EAAI8C,KACR9C,EAAIkB,GAAG,KACe,UAAtB8B,EAAMY,IAAIuB,SACN/E,EACE,MACA,CACEe,YACE,uCACFgB,YAAa,CACX,oBACE,mBAEJP,MAAO,CACLY,QAAS,MACTJ,MACE,6BACF,cACE,+BACFK,EAAG,MACHC,EAAG,MACHC,QAAS,cACT,YAAa,aAGjB,CACEvC,EAAG,IAAK,CACNA,EAAG,OAAQ,CACTwB,MAAO,CACLgB,EACE,4PAGN5C,EAAIkB,GAAG,KACPd,EAAG,OAAQ,CACTwB,MAAO,CACLgB,EACE,2NAGN5C,EAAIkB,GAAG,KACPd,EAAG,OAAQ,CACTwB,MAAO,CACLgB,EACE,oOAGN5C,EAAIkB,GAAG,KACPd,EAAG,OAAQ,CACTwB,MAAO,CACLgB,EACE,0NAGN5C,EAAIkB,GAAG,KACPd,EAAG,OAAQ,CACTwB,MAAO,CACLgB,EACE,oNAGN5C,EAAIkB,GAAG,KACPd,EAAG,OAAQ,CACTwB,MAAO,CACLgB,EACE,oNAGN5C,EAAIkB,GAAG,KACPd,EAAG,OAAQ,CACTwB,MAAO,CACLgB,EACE,oNAMZ5C,EAAI8C,OAGZ9C,EAAIkB,GAAG,KACPd,EAAG,OAAQ,CACTJ,EAAIkB,GAAGlB,EAAIoB,GAAG4B,EAAMY,IAAIuB,eAI9BnF,EAAI8C,KACR9C,EAAIkB,GAAG,KACPlB,EAAIyB,KAAKuD,oBACL5E,EACE,IACA,CACEe,YAAa,+BAEf,CAACnB,EAAIkB,GAAGlB,EAAIoB,GAAG4B,EAAMY,IAAIwB,gBAE3BpF,EAAI8C,KACR9C,EAAIkB,GAAG,MACNlB,EAAIyB,KAAKyC,gBACRlE,EAAIyB,KAAK0C,iBACVnE,EAAIyB,KAAK2C,cAwCNpE,EAAI8C,KAvCJ1C,EACE,MACA,CAAEe,YAAa,wBACf,CACEnB,EAAIyB,KAAKyC,eAAiBlB,EAAMY,IAAIS,MAChCjE,EAAG,MAAO,CACRe,YACE,wBACFmD,SAAU,CACRC,UAAWvE,EAAIoB,GAAG4B,EAAMY,IAAIS,UAGhCrE,EAAI8C,KACR9C,EAAIkB,GAAG,KACPlB,EAAIyB,KAAK0C,iBACTnB,EAAMwB,mBACNxB,EAAMwB,kBAAoB,EACtBpE,EACE,MACA,CACEe,YACE,qCAEJ,CACEnB,EAAIkB,GACFlB,EAAIoB,GACF4B,EAAMwB,mBAENxE,EAAIoB,GACFpB,EAAIyE,YACFzE,EAAIyB,KAAKiD,kBAMrB1E,EAAI8C,OAIhB9C,EAAIkB,GAAG,KACPlB,EAAIyB,KAAKwD,uBACTjF,EAAIyB,KAAKyD,oBACL9E,EACE,MACA,CAAEe,YAAa,2BACf,CAEI,WADF6B,EAAMI,mBAAmBC,cAEzBrD,EAAIyB,KAAKwD,sBACL7E,EACE,SACA,CACEe,YACE,2BACFS,MAAO,CACLC,GACE,8BACAmB,EAAMtB,IAEV8B,GAAI,CACFC,MAAO,SAASC,GACd,OAAO1D,EAAIqF,sBACTrC,EACAC,MAKR,CACEjD,EAAIkB,GACF,iCACElB,EAAIoB,GACFpB,EAAIsF,mBACFtC,IAGJ,gCAIRhD,EAAI8C,KACR9C,EAAIkB,GAAG,KACPlB,EAAIyB,KAAKyD,oBACL9E,EACE,SACA,CACEe,YACE,0BACFqC,GAAI,CACFC,MAAO,SAASC,GACd,OAAO1D,EAAI2D,gBACTX,EACAC,MAKR,CACEjD,EAAIkB,GACF,iCACElB,EAAIoB,GACFpB,EAAIyB,KAAK8D,kBAEX,gCAIRvF,EAAI8C,OAGZ9C,EAAI8C,OAGZ9C,EAAI8C,aAMlB,GAEF9C,EAAI8C,KACR9C,EAAIkB,GAAG,KACPlB,EAAIuB,QAAgC,IAAtBvB,EAAIuB,OAAOC,OACrBpB,EACE,MACA,CACEE,WAAY,CACV,CACEC,KAAM,OACNC,QAAS,SACTC,MAAOT,EAAIa,WACXC,WAAY,eAGhBK,YAAa,2BAEf,CAACnB,EAAIkB,GAAGlB,EAAIoB,GAAGpB,EAAIqB,aAAamE,mBAElCxF,EAAI8C,KACR9C,EAAIkB,GAAG,KACU,GAAjBlB,EAAIY,UACAR,EAAG,MAAO,CAAEe,YAAa,2BAA6B,CACpDnB,EAAIkB,GAAGlB,EAAIoB,GAAGpB,EAAIqB,aAAaoE,uBAEjCzF,EAAI8C,KACR9C,EAAIkB,GAAG,KACiB,GAAxBlB,EAAIU,SAASC,QACTP,EAAG,MAAO,CAAEe,YAAa,2BAA6B,CACpDnB,EAAIkB,GAAGlB,EAAIoB,GAAGpB,EAAIqB,aAAaqE,mBAEjC1F,EAAI8C,KACR9C,EAAIkB,GAAG,KACPlB,EAAIuB,QAAUvB,EAAIuB,OAAOC,OAAS,GAAKxB,EAAIU,SAASiF,eAChDvF,EACE,MACA,CACEE,WAAY,CACV,CACEC,KAAM,OACNC,QAAS,SACTC,MAAOT,EAAIa,WACXC,WAAY,eAGhBK,YAAa,kDAEf,CACEf,EAAG,MAAO,CAACJ,EAAIkB,GAAGlB,EAAIoB,GAAGpB,EAAIqB,aAAauE,mBAC1C5F,EAAIkB,GAAG,KACPlB,EAAIiB,GAAG,KAGXjB,EAAI8C,KACR9C,EAAIkB,GAAG,KACPd,EACE,QACA,CACEwB,MAAO,CACL,cACE,sDACA5B,EAAIgB,IACJ,wBACAhB,EAAIyB,KAAKC,GACX,WAAY,sBACZ,gBAAiB,2BACjB,YAAa,8BACb,cAAe,QACfmE,MAAM,GAERrC,GAAI,CACF,cAAexD,EAAI8F,WACnB,eAAgB9F,EAAI+F,YACpB,cAAe/F,EAAIgG,WACnB,aAAchG,EAAIiG,WAEpBC,MAAO,CACLzF,MAAOT,EAAImG,UACXC,SAAU,SAASC,GACjBrG,EAAImG,UAAYE,GAElBvF,WAAY,cAGhB,CACEV,EACE,MACA,CACEE,WAAY,CACV,CACEC,KAAM,cACNC,QAAS,gBACTC,MAAOT,EAAImG,UACXrF,WAAY,cAGhBK,YAAa,kBAEf,CACEnB,EAAIyB,KAAK6E,MACLlG,EACE,MACA,CACEE,WAAY,CACV,CACEC,KAAM,OACNC,QAAS,SACTC,MAA8B,GAAvBT,EAAIuG,gBACXzF,WAAY,4BAGhBK,YAAa,yBAEf,CACEf,EACE,SACA,CACEwB,MAAO,CACL4E,KAAM,SACN1B,MAAO9E,EAAIqB,aAAaoF,kBAE1BjD,GAAI,CAAEC,MAAOzD,EAAI0G,eAEnB,CACEtG,EAAG,IAAK,CACNA,EAAG,MAAO,CAAEwB,MAAO,CAAEe,QAAS,cAAiB,CAC7CvC,EAAG,OAAQ,CACTwB,MAAO,CACL,YAAa,UACb,YAAa,UACbgB,EACE,0DASlB5C,EAAI8C,KACR9C,EAAIkB,GAAG,KACPd,EAAG,MAAO,CAAEe,YAAa,0BAA4B,CACnDf,EACE,SACA,CACEwB,MAAO,CACL4E,KAAM,SACN1B,MAAO9E,EAAIqB,aAAasF,mBAE1BnD,GAAI,CACFC,MAAO,SAASC,IACZ1D,EAAImG,WAAY,KAAWnG,EAAI4G,cAAe,MAItD,CACExG,EAAG,IAAK,CACNA,EAAG,MAAO,CAAEwB,MAAO,CAAEe,QAAS,cAAiB,CAC7CvC,EAAG,OAAQ,CACTwB,MAAO,CACLgB,EACE,+GAQhB5C,EAAIkB,GAAG,KACPd,EACE,MACA,CACEe,YAAa,uBACbS,MAAO,CAAEC,GAAI,yBAEf,CACEzB,EAAG,MAAO,CACRE,WAAY,CACV,CACEC,KAAM,OACNC,QAAS,SACTC,OAA0B,IAAnBT,EAAI6G,WACX/F,WAAY,yBAGhBc,MAAO,CACLkC,IAAK9D,EAAI6G,WACT9C,IAAK/D,EAAI8G,WAAW9C,WACpB1B,MAAO,MACPC,OAAQ,MACR0B,QAAS,UAGbjE,EAAIkB,GAAG,KACqB,GAA5BlB,EAAIyB,KAAKsF,iBACM,GAAf/G,EAAIgH,SAA+B,GAAZhH,EAAIiH,KACvB7G,EACE,MACA,CACEe,YACE,4CAEJ,CACEf,EACE,SACA,CACEe,YAAa,mCACbS,MAAO,CAAE4E,KAAM,UACfhD,GAAI,CAAEC,MAAOzD,EAAIkH,yBAEnB,CACE9G,EACE,MACA,CACEe,YAAa,UACbS,MAAO,CACLQ,MAAO,6BACPC,KAAM,OACNM,QAAS,YACTwE,OAAQ,iBAGZ,CACE/G,EAAG,OAAQ,CACTwB,MAAO,CACL,iBAAkB,QAClB,kBAAmB,QACnB,eAAgB,IAChBgB,EACE,8FAKV5C,EAAIkB,GAAG,KACPd,EAAG,OAAQ,CACTJ,EAAIkB,GACFlB,EAAIoB,GAAGpB,EAAIqB,aAAa+F,2BAKhCpH,EAAIkB,GAAG,KACPd,EACE,MACA,CACEE,WAAY,CACV,CACEC,KAAM,OACNC,QAAS,SACTC,MAAOT,EAAIqH,iBACXvG,WAAY,qBAGhBK,YACE,yCAEJ,CACEf,EACE,SACA,CACEe,YACE,8CACFS,MAAO,CAAE4E,KAAM,UACfhD,GAAI,CACFC,MAAO,SAASC,GACd1D,EAAIqH,kBAAmB,KAI7B,CACEjH,EACE,MACA,CACE+B,YAAa,CACX,oBAAqB,iBAEvBP,MAAO,CACLY,QAAS,MACTX,GAAI,UACJO,MAAO,6BACP,cACE,+BACFK,EAAG,MACHC,EAAG,MACHC,QAAS,YACT,YAAa,aAGjB,CACEvC,EAAG,OAAQ,CACTwB,MAAO,CACLgB,EACE,mVAOd5C,EAAIkB,GAAG,KACPd,EACE,IACA,CACEe,YACE,+FACFS,MAAO,CACLM,KAAM,eACND,OAAQ,UAEVuB,GAAI,CACFC,MAAO,SAASC,GACd1D,EAAIqH,kBAAmB,KAI7B,CAACrH,EAAIkB,GAAG,qBAEVlB,EAAIkB,GAAG,KACPd,EACE,IACA,CACEe,YACE,gGACFS,MAAO,CACLM,KAAM,eACND,OAAQ,UAEVuB,GAAI,CACFC,MAAO,SAASC,GACd1D,EAAIqH,kBAAmB,KAI7B,CAACrH,EAAIkB,GAAG,sBAEVlB,EAAIkB,GAAG,KACPd,EACE,IACA,CACEe,YACE,+FACFS,MAAO,CACLM,KAAM,eACND,OAAQ,UAEVuB,GAAI,CACFC,MAAO,SAASC,GACd1D,EAAIqH,kBAAmB,KAI7B,CAACrH,EAAIkB,GAAG,wBAEVlB,EAAIkB,GAAG,KACPd,EACE,IACA,CACEe,YACE,8FACFS,MAAO,CACLM,KAAM,eACND,OAAQ,UAEVuB,GAAI,CACFC,MAAO,SAASC,GACd1D,EAAIqH,kBAAmB,KAI7B,CAACrH,EAAIkB,GAAG,oBAEVlB,EAAIkB,GAAG,KACPd,EACE,IACA,CACEe,YACE,8FACFS,MAAO,CAAEM,KAAM,gBACfsB,GAAI,CACFC,MAAO,SAASC,GACd1D,EAAIqH,kBAAmB,KAI7B,CAACrH,EAAIkB,GAAG,2BAMlBlB,EAAI8C,OAGZ9C,EAAIkB,GAAG,KACPd,EACE,MACA,CACEE,WAAY,CACV,CACEC,KAAM,OACNC,QAAS,SACTC,OAAQT,EAAIsH,aACZxG,WAAY,kBAGhBK,YAAa,0BAEf,CACEnB,EAAI8G,WAAWlD,KACf5D,EAAI8G,WAAWlD,IAAI2D,YACnBvH,EAAI8G,WAAWlD,IAAI4D,SACfpH,EAAG,MAAO,CAAEe,YAAa,6BAA+B,CACtDf,EAAG,OAAQ,CACTJ,EAAIkB,GACF,6BACElB,EAAIoB,GAAGpB,EAAI8G,WAAWlD,IAAI2D,YAC1B,MACAvH,EAAIoB,GAAGpB,EAAI8G,WAAWlD,IAAI4D,UAC1B,8BAIRxH,EAAI8C,KACR9C,EAAIkB,GAAG,KACPlB,EAAI8G,WAAW9C,WACX5D,EAAG,KAAM,CAACJ,EAAIkB,GAAGlB,EAAIoB,GAAGpB,EAAI8G,WAAW9C,eACvChE,EAAI8C,KACR9C,EAAIkB,GAAG,KACPlB,EAAI8G,WAAWlD,KAAO5D,EAAI8G,WAAWlD,IAAIuB,SACrC/E,EACE,MACA,CACEe,YAAa,4BACbJ,MAAO,CACL,sCACEf,EAAI8G,WAAWW,MAAMC,SACO,GAA5B1H,EAAIyB,KAAKsF,iBAEbvD,GAAI,CACFC,MAAO,SAASC,GACd,OAAO1D,EAAI2H,iBACT3H,EAAI8G,WAAWlD,IAAIuB,aAK3B,CACkC,WAAhCnF,EAAI8G,WAAWlD,IAAIuB,SACf/E,EACE,MACA,CACEe,YAAa,iCACbgB,YAAa,CACX,oBAAqB,iBAEvBP,MAAO,CACLY,QAAS,MACTJ,MAAO,6BACP,cACE,+BACFK,EAAG,MACHC,EAAG,MACHC,QAAS,YACT,YAAa,aAGjB,CACEvC,EAAG,IAAK,CACNA,EAAG,OAAQ,CACTwB,MAAO,CACLgB,EACE,iQAGN5C,EAAIkB,GAAG,KACPd,EAAG,OAAQ,CACTwB,MAAO,CACLgB,EACE,uLAMZ5C,EAAI8C,KACR9C,EAAIkB,GAAG,KACwB,UAA/BlB,EAAI8G,WAAWlD,IAAIuB,SACf/E,EACE,MACA,CACEe,YACE,wCACFgB,YAAa,CACX,oBAAqB,mBAEvBP,MAAO,CACLY,QAAS,MACTJ,MAAO,6BACP,cACE,+BACFK,EAAG,MACHC,EAAG,MACHC,QAAS,cACT,YAAa,aAGjB,CACEvC,EAAG,IAAK,CACNA,EAAG,OAAQ,CACTwB,MAAO,CACLgB,EACE,4PAGN5C,EAAIkB,GAAG,KACPd,EAAG,OAAQ,CACTwB,MAAO,CACLgB,EACE,2NAGN5C,EAAIkB,GAAG,KACPd,EAAG,OAAQ,CACTwB,MAAO,CACLgB,EACE,oOAGN5C,EAAIkB,GAAG,KACPd,EAAG,OAAQ,CACTwB,MAAO,CACLgB,EACE,0NAGN5C,EAAIkB,GAAG,KACPd,EAAG,OAAQ,CACTwB,MAAO,CACLgB,EACE,oNAGN5C,EAAIkB,GAAG,KACPd,EAAG,OAAQ,CACTwB,MAAO,CACLgB,EACE,oNAGN5C,EAAIkB,GAAG,KACPd,EAAG,OAAQ,CACTwB,MAAO,CACLgB,EACE,oNAMZ5C,EAAI8C,KACR9C,EAAIkB,GAAG,KACPd,EAAG,OAAQ,CACTJ,EAAIkB,GAAGlB,EAAIoB,GAAGpB,EAAI8G,WAAWlD,IAAIuB,eAIvCnF,EAAI8C,OAGZ9C,EAAIkB,GAAG,KACPd,EAAG,MAAO,CAAEe,YAAa,4BAA8B,CACrDf,EAAG,MAAO,CAAEe,YAAa,mBAAqB,CAC5Cf,EACE,MACA,CACEE,WAAY,CACV,CACEC,KAAM,OACNC,QAAS,SACTC,OAAQT,EAAIsH,aACZxG,WAAY,kBAGhBK,YAAa,+BAEf,CACEnB,EAAI8G,WAAWlD,KAAO5D,EAAI8G,WAAWc,aACjCxH,EAAG,IAAK,CAAEe,YAAa,4BAA8B,CACnDnB,EAAIkB,GAAGlB,EAAIoB,GAAGpB,EAAI8G,WAAWc,iBAE/B5H,EAAI8C,KACR9C,EAAIkB,GAAG,KACPlB,EAAI+C,GAAG/C,EAAI6H,kBAAkB,SAASzC,GACpC,OAAOhF,EAAG,MAAO,CACf8C,IAAKkC,EAAY1D,GACjBP,YAAa,sCACbJ,MAAO,CACL,2CACEqE,EAAY0C,KAAKC,KACnB,0CACE3C,EAAY0C,KAAKjE,MACnB,4CACEuB,EAAY0C,KAAKE,OAErB1D,SAAU,CACRC,UAAWvE,EAAIoB,GAAGpB,EAAIiI,cAAc7C,UAI1CpF,EAAIkB,GAAG,KACW,GAAlBlB,EAAIkI,WACA9H,EACE,MACA,CACEe,YACE,mHAEJ,CACEf,EAAG,IAAK,CACNJ,EAAIkB,GACFlB,EAAIoB,GAAGpB,EAAIqB,aAAa8G,kBACtB,KAEJ/H,EAAG,MACHA,EACE,IACA,CACEwB,MAAO,CACLM,KAAMlC,EAAI8G,WAAWvD,IACrBtB,OACgC,GAA9BjC,EAAIyB,KAAK2G,kBACL,SACA,UAGV,CACEpI,EAAIkB,GACFlB,EAAIoB,GAAGpB,EAAIqB,aAAagH,yBAOpCrI,EAAI8C,MAEV,GAEF9C,EAAIkB,GAAG,KACPd,EACE,MACA,CACEE,WAAY,CACV,CACEC,KAAM,OACNC,QAAS,SACTC,MAAOT,EAAIsH,aACXxG,WAAY,iBAGhBK,YAAa,kCAEf,CACEf,EACE,MACA,CACEe,YACE,4CAEJ,CACEf,EACE,MACA,CAAEe,YAAa,4BACf,CACEf,EAAG,MAAO,CAAEe,YAAa,mBACzBnB,EAAIkB,GAAG,KACPd,EAAG,MAAO,CAAEe,YAAa,uBAKjCnB,EAAIkB,GAAG,KACPd,EAAG,MAAO,CACR+B,YAAa,CACXmG,SAAU,WACV,mBAAoB,OACpB,UAAW,KAEb1G,MAAO,CAAEC,GAAI,0CAMvB7B,EAAIkB,GAAG,KACPlB,EAAI8G,WAAWlD,KACf5D,EAAI8G,WAAWW,MAAMC,SACE,GAAvB1H,EAAIyB,KAAK8G,WACLnI,EACE,MACA,CACEE,WAAY,CACV,CACEC,KAAM,OACNC,QAAS,SACTC,OAAQT,EAAIsH,aACZxG,WAAY,kBAGhBK,YAAa,uBACbS,MAAO,CAAEC,GAAI,yBAEf,CACEzB,EAAG,SAAU,CACXwB,MAAO,CACLU,MAAO,OACPC,OAAQ,MACRiG,YAAa,IACbC,UAAW,KACXC,aAAc,IACdC,YAAa,IACb7E,IAAK9D,EAAI4I,cAAc3I,KAAK6G,WAAWlD,IAAIuB,UAC3ClB,QAAS,YAKjBjE,EAAI8C,KACR9C,EAAIkB,GAAG,KACPlB,EAAI8G,WAAW+B,WACf7I,EAAI8G,WAAW+B,UAAUtI,MACE,GAA3BP,EAAIyB,KAAKqH,eACL1I,EACE,MACA,CACEE,WAAY,CACV,CACEC,KAAM,OACNC,QAAS,SACTC,OAAQT,EAAIsH,aACZxG,WAAY,kBAGhBK,YAAa,8BAEf,CACEf,EACE,MACA,CAAEe,YAAa,kCACf,CACEnB,EAAI+I,gBACJ/I,EAAI+I,eAAeC,MACnBhJ,EAAI+I,eAAeC,KAAKzF,IACpBnD,EACE,MACA,CACEe,YACE,oCAEJ,CACEf,EAAG,MAAO,CACRwB,MAAO,CACLkC,IAAK9D,EAAI+I,eAAeC,KAAKzF,IAC7BQ,IAAK,2BAKb/D,EAAI8C,KACR9C,EAAIkB,GAAG,KACPd,EACE,MACA,CAAEe,YAAa,mCACf,CACEnB,EAAI8G,WAAW+B,UAAUtI,KACrBH,EACE,MACA,CACEe,YACE,mCAEJ,CACEf,EAAG,OAAQ,CACTJ,EAAIkB,GACFlB,EAAIoB,GAAGpB,EAAI8G,WAAW+B,UAAUtI,WAKxCP,EAAI8C,KACR9C,EAAIkB,GAAG,KACPd,EACE,MACA,CACEe,YACE,oCAEJ,CACEnB,EAAIkB,GACFlB,EAAIoB,GAAGpB,EAAIqB,aAAa4H,oBAI9BjJ,EAAIkB,GAAG,KACPlB,EAAI8G,WAAW+B,UAAUzD,YACrBhF,EAAG,MAAO,CACRe,YACE,yCACFmD,SAAU,CACRC,UAAWvE,EAAIoB,GACbpB,EAAI8G,WAAW+B,UAAUzD,YACtB8D,SAITlJ,EAAI8C,KACR9C,EAAIkB,GAAG,KACPlB,EAAI+I,eACA3I,EACE,MACA,CACEe,YACE,qCAEJ,CACEnB,EAAI+I,eAAeI,SACf/I,EACE,IACA,CACEe,YACE,4CACFS,MAAO,CACLM,KACE,4BACAlC,EAAI+I,eAAeI,SACrBlH,OAEE,GADAjC,EAAIyB,KAAK2G,kBAEL,SACA,QACNtD,MAAO,aAGX,CACE1E,EACE,MACA,CACEe,YACE,uCACFS,MAAO,CACLQ,MACE,6BACF,cAAe,OACfgH,UAAW,QACX,cAAe,MACf,YAAa,aACbC,KAAM,MACN1G,QAAS,gBAGb,CACEvC,EAAG,OAAQ,CACTwB,MAAO,CACLS,KAAM,eACNO,EACE,sLAOd5C,EAAI8C,KACR9C,EAAIkB,GAAG,KACPlB,EAAI+I,eAAeO,QACflJ,EACE,IACA,CACEe,YACE,4CACFS,MAAO,CACLM,KACE,2BACAlC,EAAI+I,eAAeO,QACrBrH,OAEE,GADAjC,EAAIyB,KAAK2G,kBAEL,SACA,QACNtD,MAAO,YAGX,CACE1E,EACE,MACA,CACEe,YACE,oCACFS,MAAO,CACLQ,MACE,6BACF,cAAe,OACfgH,UAAW,QACX,cAAe,MACf,YAAa,UACbC,KAAM,MACN1G,QAAS,gBAGb,CACEvC,EAAG,OAAQ,CACTwB,MAAO,CACLS,KAAM,eACNO,EACE,iyBAOd5C,EAAI8C,KACR9C,EAAIkB,GAAG,KACPlB,EAAI8G,WAAW+B,UAAUU,QACrBnJ,EACE,IACA,CACEe,YACE,6CACFS,MAAO,CACLM,KACElC,EAAI8G,WAAW+B,UACZU,QACLtH,OAEE,GADAjC,EAAIyB,KAAK2G,kBAEL,SACA,QACNtD,MACE9E,EAAIqB,aACDmI,iBAGT,CACEpJ,EACE,MACA,CACEe,YACE,kCACFS,MAAO,CACLQ,MACE,6BACF,cAAe,OACfgH,UAAW,QACX,cAAe,MACf,YAAa,QACbC,KAAM,MACN1G,QAAS,gBAGb,CACEvC,EAAG,OAAQ,CACTwB,MAAO,CACLS,KAAM,eACNO,EACE,oyBAOd5C,EAAI8C,OAGZ9C,EAAI8C,WAOpB9C,EAAI8C,OAGZ9C,EAAIkB,GAAG,KACPd,EACE,MACA,CACEE,WAAY,CACV,CACEC,KAAM,OACNC,QAAS,SACTC,OAAQT,EAAIsH,aACZxG,WAAY,kBAGhBK,YAAa,oBAEf,CAC8B,GAA5BnB,EAAIyB,KAAKgI,gBACLrJ,EAAG,MAAO,CAAEe,YAAa,gCAAkC,CACzDf,EACE,IACA,CACEe,YAAa,mCACbS,MAAO,CACLM,KAAMlC,EAAI8G,WAAWvD,IACrBtB,OACgC,GAA9BjC,EAAIyB,KAAK2G,kBACL,SACA,UAGV,CACEhI,EAAG,OAAQ,CAAEe,YAAa,WAAa,CACrCnB,EAAIkB,GAAGlB,EAAIoB,GAAGpB,EAAIqB,aAAaoI,iBAAmB,OAEpDrJ,EAAG,OAAQ,CAAEe,YAAa,UAAY,CACpCnB,EAAIkB,GAAG,oBAKflB,EAAI8C,KACR9C,EAAIkB,GAAG,KACPd,EAAG,MAAO,CAAEe,YAAa,iCAAmC,CAC1DnB,EAAI8G,WAAWlD,KACf5D,EAAI8G,WAAWlD,IAAIS,OACnBrE,EAAIyB,KAAKyC,cACL9D,EAAG,MAAO,CACRe,YAAa,yBACbmD,SAAU,CAAEC,UAAWvE,EAAIoB,GAAGpB,EAAI8G,WAAWlD,IAAIS,UAEnDrE,EAAI8C,KACR9C,EAAIkB,GAAG,KACPlB,EAAI8G,YAC8C,WAAlD9G,EAAI8G,WAAW1D,mBAAmBC,aAC9BjD,EACE,SACA,CACEe,YAAa,mCACbqC,GAAI,CACFC,MAAO,SAASC,GACd,OAAO1D,EAAI0J,QACT1J,EAAI8G,WACJ9G,EAAI2J,oBAKZ,CACE3J,EAAIkB,GACF,yBACElB,EAAIoB,GAAGpB,EAAIsF,mBAAmBtF,EAAI8G,aAClC,wBAIR9G,EAAI8C,WAMlB9C,EAAIkB,GAAG,KACPd,EACE,MACA,CACEW,MACE,qEACAf,EAAIgB,KAER,CAAChB,EAAIiB,GAAG,MAGZ,IAwCJlB,EAAO6J,eAAgB,E,ICxwCvB,E,6CCrWmL,EDuWnL,CACE,KAAF,SACE,MAAF,4DACE,KAAF,WACI,MAAJ,CAGM,IAAN,gBACM,KAAN,iBACM,SAAN,qBACM,IAAN,gBACM,MAAN,sCACM,QAAN,wCACM,KAAN,qCACM,aAAN,sCACM,SAAN,qFAGM,OAAN,GAGM,WAAN,EACM,YAAN,EAGM,iBAAN,EACM,WAAN,EACM,YAAN,EACM,YAAN,EACM,cAAN,EACM,kBAAN,EACM,gBAAN,EACM,YAAN,EACM,qBAAN,EACM,iBAAN,EACM,YAAN,EACM,kBAAN,EAGM,UAAN,IAIE,WAAF,CACA,UACI,YAAJ,KAEE,QAAF,CAGI,WAAJ,WAAM,IAAN,OAGM,KAAN,cAGM,KAAN,iCAEA,GAAQ,EAAR,SACM,IAAN,kBACM,EAAN,WAEA,kDAGA,iCAEU,IAAV,4EAAY,YAAZ,UACA,kBAEY,EAAZ,sBACY,EAAZ,sBAGA,kBACgB,KAAhB,4BAEgB,KAAhB,oBAKA,mBAGA,gBACc,EAAd,sBAIc,EAAd,cACc,EAAd,kCAEc,EAAd,sBAGA,kBACkB,KAAlB,4BAEkB,KAAlB,sBAUU,KAAV,qCAGA,kBACY,KAAZ,4BAEY,KAAZ,eAQA,kCAEU,IAAV,4IAAY,YAAZ,UACA,kBACY,EAAZ,wBACY,EAAZ,4CACY,EAAZ,gCACY,EAAZ,sBAGA,kBACgB,KAAhB,4BAEgB,KAAhB,oBAOA,mBAGA,gBACc,EAAd,sBAIc,EAAd,cACc,EAAd,kCACc,EAAd,oBACc,EAAd,kBAEc,EAAd,sBAGA,kBACkB,KAAlB,4BAEkB,KAAlB,qBAQA,6DACY,KAAZ,8CAKU,IAAV,0GAAY,YAAZ,UACA,kBACY,EAAZ,kCACY,EAAZ,4CACY,EAAZ,gCACY,EAAZ,sBAGA,kBACgB,KAAhB,4BAEgB,KAAhB,oBAOA,mBAGA,gBACc,EAAd,sBAIc,EAAd,cACc,EAAd,kCACc,EAAd,oBACc,EAAd,kBAEc,EAAd,sBAGA,kBACkB,KAAlB,4BAEkB,KAAlB,qBAQA,6DACY,KAAZ,6CAQA,sBACQ,KAAR,0BAMI,UAAJ,WAGM,GAAN,8DACQ,IAAR,gCACQ,EAAR,sCACQ,EAAR,+CACQ,OAAR,wCAMI,YAAJ,WACM,KAAN,iBAKI,WAAJ,WACM,KAAN,eACM,KAAN,oBAGM,IAAN,gCACM,EAAN,4BACM,EAAN,+BACM,OAAN,0CAII,QAAJ,cAGA,4BAEQ,KAAR,qBACQ,KAAR,oBAKA,+BACU,OAAV,uBAEU,OAAV,gCAQI,mBAAJ,YAEM,KAAN,aACM,KAAN,gBACM,KAAN,mBACM,KAAN,cACM,KAAN,aAEA,sEACA,qBAEM,EAAN,UAKI,gBAAJ,cAGA,mDACQ,KAAR,aACQ,KAAR,aACQ,KAAR,mBAKA,+BACU,OAAV,YAEU,OAAV,qBAQI,sBAAJ,cAGA,4BACQ,KAAR,aACQ,KAAR,aACQ,KAAR,kBACQ,KAAR,sBAIA,+BACU,OAAV,uBAEU,OAAV,gCAMI,mBAAJ,WACM,KAAN,gCACA,oDACQ,KAAR,oCACQ,KAAR,2BAKI,cAAJ,WACM,SAAN,oDAII,aAAJ,WACM,KAAN,qBACM,KAAN,iCACM,KAAN,oCAII,aAAJ,WACM,KAAN,aACM,KAAN,gBACM,KAAN,cACM,KAAN,oBACM,KAAN,cACM,KAAN,uBACM,KAAN,mBACM,KAAN,qBAKI,uBAAJ,WACM,IAAN,GACQ,WAAR,WACQ,QAAR,mBACQ,kBAAR,kCAEM,OAAN,2BAII,WAAJ,YACM,IAAN,aAMM,OALA,EAAN,uCACM,EAAN,0CACM,EAAN,+BACM,EAAN,oCACM,EAAN,qCACA,cAII,SAAJ,YACM,IAAN,aAIM,OAHA,EAAN,iCACM,EAAN,kCACM,EAAN,oCACA,cAII,cAAJ,YAEM,MADN,iDAKI,cAAJ,YAGM,GAAN,YACQ,OAAR,iBAGA,gBACQ,MAAR,yDAGA,iBAGQ,GAAR,2CACU,MAAV,kKAGA,4CACU,MAAV,mNAOI,iBAAJ,YACM,IAAN,kDACM,QAAN,eACQ,EAAR,gBAAU,SAAV,gBACA,iBACQ,IAAR,yCACA,+BACU,OAAV,QAEU,OAAV,aAMI,iBAAJ,WACM,IAAN,uDACA,gBACQ,EAAR,gBAAU,SAAV,UAKI,uBAAJ,WACM,KAAN,wCACA,yBACQ,OAAR,qDAEQ,OAAR,yDAKI,sBAAJ,YACA,gEACQ,KAAR,sBAKI,2BAAJ,YACM,IAAN,GACQ,MAAR,aACQ,YAAR,eACQ,MAAR,YACQ,IAAR,UACQ,SAAR,gBAEM,SAAN,qGACM,SAAN,sGACM,SAAN,qGACM,SAAN,oGACM,SAAN,qGAII,SAAJ,YACM,KAAN,6BACM,KAAN,uBAEI,oBAAJ,WACA,kBACQ,SAAR,6DAEQ,SAAR,iEAKI,YAAJ,YACM,IAAN,KACM,GAAN,OACA,QAEM,OAAN,GAII,mBAAJ,YAOM,OALN,aACA,6BAEA,gCAME,MAAF,CAGI,WAAJ,CACM,QAAN,cAEQ,IAAR,wDACA,sDACA,wEAGA,0BACA,IAAY,EAAZ,sBACA,IAAY,EAAZ,uBAGA,qBACA,IAAY,EAAZ,sBACA,IAAY,EAAZ,sBACU,KAAV,iBAGA,OACA,IAAY,EAAZ,sBACA,IAAY,EAAZ,uBAIA,iBACA,GAAY,EAAZ,6BAEA,GAAY,EAAZ,kCAOI,aAAJ,CACM,QAAN,cAEQ,IAAR,wDAEA,KACU,EAAV,+BAEU,EAAV,qCAOE,QA1kBF,WA0kBI,IAAJ,OAGI,IAAJ,gEACA,kBACM,EAAN,qBACM,EAAN,cACM,EAAN,sBAGM,IAAN,gCACM,GAAN,2DACA,4BACA,WACU,IAAV,gCACA,0DAAY,OAAZ,cACA,kDACY,EAAZ,aACY,EAAZ,uBACY,EAAZ,oBAGY,EAAZ,4BACY,EAAZ,+BACY,OAAZ,8CAKA,mBACM,EAAN,gBAGI,KAAJ,sBAAM,IAAN,OAGM,OAAN,wCAGQ,GAAR,wCAEU,IAAV,4DAEU,EAAV,oBACU,EAAV,wBAIA,oFAGA,iBACY,EAAZ,wBAKA,gIA+BA,2EAEU,YAAV,WAAY,OAAZ,kCAGU,YAAV,WAAY,OAAZ,6BAjCA,iBAGA,6CAEc,YAAd,WAAgB,OAAhB,oCAGA,0CACgB,YAAhB,WAAkB,OAAlB,8BAKc,EAAd,2BAGA,2CACgB,EAAhB,yBAsBM,OAAN,yCAGA,4DACA,sDAKE,cAvrBF,WA0rBI,OAAJ,4CAGI,OAAJ,uDE7hCIC,GAAY,E,OAAA,GACd,EACA9J,EH+jDoB,CACpB,WACE,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eACTC,EAAKJ,EAAIK,MAAMD,IAAMF,EACzB,OAAOE,EAAG,MAAO,CAAEe,YAAa,4BAA8B,CAC5Df,EAAG,MAAO,CAAEe,YAAa,mBACzBnB,EAAIkB,GAAG,KACPd,EAAG,MAAO,CAAEe,YAAa,sBAG7B,WACE,IAAInB,EAAMC,KACNC,EAAKF,EAAIG,eACTC,EAAKJ,EAAIK,MAAMD,IAAMF,EACzB,OAAOE,EACL,IACA,CAAEwB,MAAO,CAAEM,KAAM,2BAA4BD,OAAQ,WACrD,CACE7B,EAAG,OAAQ,CAAEe,YAAa,cAAgB,CACxCf,EAAG,SAAU,CAACJ,EAAIkB,GAAG,gBACrBlB,EAAIkB,GAAG,wBAKf,WACE,IAAIlB,EAAMC,KACNC,EAAKF,EAAIG,eACTC,EAAKJ,EAAIK,MAAMD,IAAMF,EACzB,OAAOE,EAAG,MAAO,CAAEe,YAAa,4BAA8B,CAC5Df,EAAG,MAAO,CAAEe,YAAa,mBACzBnB,EAAIkB,GAAG,KACPd,EAAG,MAAO,CAAEe,YAAa,wBG9lD7B,EACA,KACA,KACA,MAuBF0I,EAAUC,QAAQC,OAAS,sCAC3B,MAAeF,EAAiB", "sources": ["webpack://event-feed-for-eventbrite/./public/src/js/components/Widget.vue?a51f", "webpack://event-feed-for-eventbrite/public/src/js/components/Widget.vue", "webpack://event-feed-for-eventbrite/./public/src/js/components/Widget.vue?a068", "webpack://event-feed-for-eventbrite/./public/src/js/components/Widget.vue"], "sourcesContent": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    [\n      _c(\n        \"div\",\n        {\n          directives: [\n            {\n              name: \"show\",\n              rawName: \"v-show\",\n              value: _vm.settings.api_key && !_vm.feedError && !_vm.feedLoaded,\n              expression: \"(settings.api_key) && !feedError && !feedLoaded\"\n            }\n          ],\n          class:\n            \"eventbrite-modal-spinner-feed eventbrite-modal-spinner-feed-\" +\n            _vm.uid\n        },\n        [\n          _vm._m(0),\n          _vm._v(\" \"),\n          _c(\"div\", { staticClass: \"eventbrite-modal-spinner-text\" }, [\n            _c(\"p\", [_vm._v(_vm._s(_vm.translations.loading_text))])\n          ])\n        ]\n      ),\n      _vm._v(\" \"),\n      _vm.settings.api_key && _vm.events && _vm.events.length > 0\n        ? _c(\n            \"div\",\n            {\n              directives: [\n                {\n                  name: \"show\",\n                  rawName: \"v-show\",\n                  value: _vm.feedLoaded,\n                  expression: \"feedLoaded\"\n                }\n              ],\n              class:\n                \"eventbrite-feed eventbrite-feed-id-\" +\n                _vm.feed.ID +\n                \" eventbrite-feed-widget \" +\n                _vm.feed.css_classes,\n              attrs: { id: _vm.feed.css_id }\n            },\n            [\n              _vm.admin && _vm.edit_url\n                ? _c(\n                    \"a\",\n                    {\n                      staticClass: \"eventbrite-feed-edit-link\",\n                      attrs: { target: \"_blank\", href: _vm.edit_url }\n                    },\n                    [\n                      _c(\n                        \"svg\",\n                        {\n                          staticStyle: { \"enable-background\": \"new 0 0 64 64\" },\n                          attrs: {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            \"xmlns:xlink\": \"http://www.w3.org/1999/xlink\",\n                            fill: \"#334ecd\",\n                            width: \"16\",\n                            height: \"16\",\n                            version: \"1.1\",\n                            id: \"lni_lni-pencil-alt\",\n                            x: \"0px\",\n                            y: \"0px\",\n                            viewBox: \"0 0 64 64\",\n                            \"xml:space\": \"preserve\"\n                          }\n                        },\n                        [\n                          _c(\"path\", {\n                            attrs: {\n                              d:\n                                \"M62.7,11.2c0-0.7-0.3-1.3-0.8-1.8c-1.3-1.3-2.5-2.5-3.7-3.7c-1.1-1.1-2.2-2.2-3.3-3.4c-0.4-0.5-1-0.9-1.6-1  c-0.7-0.1-1.5,0.1-2.1,0.6l-7.2,7.2H8.7c-4.1,0-7.4,3.3-7.4,7.4v38.9c0,4.1,3.3,7.4,7.4,7.4h38.9c4.1,0,7.4-3.3,7.4-7.4V19.9  l6.9-6.9C62.4,12.5,62.7,11.8,62.7,11.2z M33.3,36.6c-0.1,0.1-0.3,0.2-0.4,0.3l-8.6,2.9l2.8-8.6c0.1-0.2,0.1-0.3,0.3-0.4l19-19  l6,5.9L33.3,36.6z M51.5,55.4c0,2.1-1.7,3.9-3.9,3.9H8.7c-2.1,0-3.9-1.7-3.9-3.9V16.4c0-2.1,1.7-3.9,3.9-3.9h31.9L24.9,28.2  c-0.5,0.5-0.9,1.1-1.1,1.8l-3.8,11.6c-0.2,0.6-0.1,1.2,0.2,1.7c0.3,0.4,0.7,0.8,1.6,0.8h0.3l11.9-3.9c0.7-0.2,1.3-0.6,1.8-1.1  l15.8-15.7V55.4z M54.8,15.1l-6-5.9l4-4c1,1,1.9,1.9,2.9,2.9c1,1,2,2,3,3.1L54.8,15.1z\"\n                            }\n                          })\n                        ]\n                      ),\n                      _vm._v(\" \"),\n                      _c(\"span\", [\n                        _vm._v(_vm._s(_vm.translations.edit_link_text))\n                      ])\n                    ]\n                  )\n                : _vm._e(),\n              _vm._v(\" \"),\n              _vm._l(_vm.events, function(event, eventIndex) {\n                return _c(\n                  \"div\",\n                  {\n                    key: event.ID,\n                    class: [\n                      \"eventbrite-item\",\n                      [\n                        \"on_sale\",\n                        \"not_yet_on_sale\",\n                        \"sales_ended\",\n                        \"sold_out\",\n                        \"unavailable\"\n                      ].includes(event.event_sales_status.sales_status)\n                        ? event.event_sales_status.sales_status\n                        : \"\"\n                    ],\n                    attrs: {\n                      \"data-event-id\": event.ID,\n                      \"data-event-index\": eventIndex\n                    }\n                  },\n                  [\n                    _vm.feed.display_image && event.url\n                      ? _c(\"div\", { staticClass: \"eventbrite-item-image\" }, [\n                          _c(\n                            \"div\",\n                            {\n                              staticClass: \"eventbrite-item-image-inner\",\n                              on: {\n                                click: function($event) {\n                                  return _vm.getEventDetails(event, eventIndex)\n                                }\n                              }\n                            },\n                            [\n                              event.vue.image\n                                ? _c(\"img\", {\n                                    attrs: {\n                                      src: event.vue.image,\n                                      alt: event.post_title,\n                                      width: \"400\",\n                                      height: \"200\",\n                                      loading: \"lazy\"\n                                    }\n                                  })\n                                : _vm._e(),\n                              _vm._v(\" \"),\n                              (_vm.feed.display_price ||\n                                _vm.feed.display_tickets) &&\n                              _vm.feed.price_overlay\n                                ? _c(\n                                    \"span\",\n                                    {\n                                      staticClass: \"eventbrite-item-image-tags\"\n                                    },\n                                    [\n                                      _vm.feed.display_price && event.vue.price\n                                        ? _c(\"span\", {\n                                            staticClass:\n                                              \"eventbrite-item-image-price\",\n                                            domProps: {\n                                              innerHTML: _vm._s(event.vue.price)\n                                            }\n                                          })\n                                        : _vm._e(),\n                                      _vm._v(\" \"),\n                                      _vm.feed.display_tickets &&\n                                      event.tickets_remaining &&\n                                      event.tickets_remaining > 0\n                                        ? _c(\n                                            \"span\",\n                                            {\n                                              staticClass:\n                                                \"eventbrite-item-image-available-tickets\"\n                                            },\n                                            [\n                                              _vm._v(\n                                                _vm._s(\n                                                  event.tickets_remaining\n                                                ) +\n                                                  _vm._s(\n                                                    _vm.ticketsText(\n                                                      _vm.feed.tickets_text\n                                                    )\n                                                  )\n                                              )\n                                            ]\n                                          )\n                                        : _vm._e()\n                                    ]\n                                  )\n                                : _vm._e()\n                            ]\n                          )\n                        ])\n                      : _vm._e(),\n                    _vm._v(\" \"),\n                    _c(\"div\", { staticClass: \"eventbrite-item-content\" }, [\n                      _c(\"div\", { staticClass: \"eventbrite-item-top-row\" }, [\n                        _c(\n                          \"div\",\n                          { staticClass: \"eventbrite-item-title-col\" },\n                          [\n                            _vm.feed.display_datetime && event.vue.start\n                              ? _c(\n                                  \"time\",\n                                  { staticClass: \"eventbrite-item-datetime\" },\n                                  [\n                                    _vm._v(\n                                      \"\\n                            \" +\n                                        _vm._s(event.vue.start) +\n                                        \" - \" +\n                                        _vm._s(event.vue.end) +\n                                        \"\\n                        \"\n                                    )\n                                  ]\n                                )\n                              : _vm._e(),\n                            _vm._v(\" \"),\n                            event.vue.title\n                              ? _c(\n                                  \"h3\",\n                                  {\n                                    staticClass: \"eventbrite-item-title\",\n                                    on: {\n                                      click: function($event) {\n                                        return _vm.getEventDetails(\n                                          event,\n                                          eventIndex\n                                        )\n                                      }\n                                    }\n                                  },\n                                  [\n                                    _vm._v(\n                                      \"\\n                            \" +\n                                        _vm._s(event.vue.title) +\n                                        \"\\n                        \"\n                                    )\n                                  ]\n                                )\n                              : _vm._e()\n                          ]\n                        )\n                      ]),\n                      _vm._v(\" \"),\n                      _vm.feed.display_location ||\n                      _vm.feed.display_description ||\n                      (_vm.feed.display_price &&\n                        !_vm.feed.display_image &&\n                        event.vue.price) ||\n                      _vm.feed.display_signup_button ||\n                      _vm.feed.display_more_button\n                        ? _c(\n                            \"div\",\n                            { staticClass: \"eventbrite-item-bottom-row\" },\n                            [\n                              _vm.feed.display_location\n                                ? _c(\n                                    \"p\",\n                                    { staticClass: \"eventbrite-item-location\" },\n                                    [\n                                      _c(\n                                        \"span\",\n                                        {\n                                          staticClass:\n                                            \"eventbrite-item-location-icon-wrapper\"\n                                        },\n                                        [\n                                          event.vue.location !== \"Online\"\n                                            ? _c(\n                                                \"svg\",\n                                                {\n                                                  staticClass:\n                                                    \"eventbrite-item-location-icon\",\n                                                  staticStyle: {\n                                                    \"enable-background\":\n                                                      \"new 0 0 48 64\"\n                                                  },\n                                                  attrs: {\n                                                    version: \"1.1\",\n                                                    xmlns:\n                                                      \"http://www.w3.org/2000/svg\",\n                                                    \"xmlns:xlink\":\n                                                      \"http://www.w3.org/1999/xlink\",\n                                                    x: \"0px\",\n                                                    y: \"0px\",\n                                                    viewBox: \"0 0 48 64\",\n                                                    \"xml:space\": \"preserve\"\n                                                  }\n                                                },\n                                                [\n                                                  _c(\"g\", [\n                                                    _c(\"path\", {\n                                                      attrs: {\n                                                        d:\n                                                          \"M24,0C10.7,0,0,11.2,0,25.3c0,12,16.5,31.7,21.6,37.6c0.5,0.8,1.6,1.1,2.4,1.1c1.1,0,1.9-0.5,2.4-1.1 C31.5,57.1,48,37.1,48,25.3C48,11.2,37.3,0,24,0z M24,57.6C14.9,46.9,5.3,32.8,5.3,25.3c0-11.2,8.3-20,18.7-20s18.7,9.1,18.7,20 C42.7,32.8,33.1,46.9,24,57.6z\"\n                                                      }\n                                                    }),\n                                                    _vm._v(\" \"),\n                                                    _c(\"path\", {\n                                                      attrs: {\n                                                        d:\n                                                          \"M24,13.3c-5.9,0-10.7,4.8-10.7,10.7S18.1,34.7,24,34.7S34.7,29.9,34.7,24S29.9,13.3,24,13.3z M24,29.3 c-2.9,0-5.3-2.4-5.3-5.3s2.4-5.3,5.3-5.3s5.3,2.4,5.3,5.3S26.9,29.3,24,29.3z\"\n                                                      }\n                                                    })\n                                                  ])\n                                                ]\n                                              )\n                                            : _vm._e(),\n                                          _vm._v(\" \"),\n                                          event.vue.location == \"Online\"\n                                            ? _c(\n                                                \"svg\",\n                                                {\n                                                  staticClass:\n                                                    \"eventbrite-item-location-icon-online\",\n                                                  staticStyle: {\n                                                    \"enable-background\":\n                                                      \"new 0 0 64.1 48\"\n                                                  },\n                                                  attrs: {\n                                                    version: \"1.1\",\n                                                    xmlns:\n                                                      \"http://www.w3.org/2000/svg\",\n                                                    \"xmlns:xlink\":\n                                                      \"http://www.w3.org/1999/xlink\",\n                                                    x: \"0px\",\n                                                    y: \"0px\",\n                                                    viewBox: \"0 0 64.1 48\",\n                                                    \"xml:space\": \"preserve\"\n                                                  }\n                                                },\n                                                [\n                                                  _c(\"g\", [\n                                                    _c(\"path\", {\n                                                      attrs: {\n                                                        d:\n                                                          \"M32,13.9c-3.5,0-6.4,2.9-6.4,6.1c0,2.4,1.6,4.5,3.7,5.6v19.7c0,1.6,1.1,2.7,2.7,2.7s2.7-1.1,2.7-2.7V25.9 c2.1-1.1,3.7-3.2,3.7-5.6C38.4,16.8,35.5,13.9,32,13.9z M32,19.2c0.5,0,1.1,0.5,1.1,0.8s-0.5,0.8-1.1,0.8c-0.5,0-1.1-0.5-1.1-0.8 S31.5,19.2,32,19.2z\"\n                                                      }\n                                                    }),\n                                                    _vm._v(\" \"),\n                                                    _c(\"path\", {\n                                                      attrs: {\n                                                        d:\n                                                          \"M24.5,10.9c-1.1-1.1-2.7-1.1-3.7,0c-2.4,2.4-3.5,5.3-3.5,8.5s1.3,6.4,3.5,8.8c0.5,0.5,1.3,0.8,1.9,0.8 c0.8,0,1.3-0.3,1.9-0.8c1.1-1.1,1.1-2.7,0-3.7c-1.3-1.3-2.1-2.9-2.1-4.8s0.8-3.5,2.1-4.8C25.6,13.6,25.6,12,24.5,10.9z\"\n                                                      }\n                                                    }),\n                                                    _vm._v(\" \"),\n                                                    _c(\"path\", {\n                                                      attrs: {\n                                                        d:\n                                                          \"M43.2,10.9c-1.1-1.1-2.7-1.1-3.7,0c-1.1,1.1-1.1,2.7,0,3.7c1.3,1.3,2.1,2.9,2.1,4.8s-0.8,3.5-2.1,4.8 c-1.1,1.1-1.1,2.7,0,3.7c0.5,0.5,1.3,0.8,1.9,0.8c0.8,0,1.3-0.3,1.9-0.8c2.4-2.4,3.5-5.3,3.5-8.8C46.9,16.3,45.6,13.1,43.2,10.9z\"\n                                                      }\n                                                    }),\n                                                    _vm._v(\" \"),\n                                                    _c(\"path\", {\n                                                      attrs: {\n                                                        d:\n                                                          \"M13.9,19.5c0-3.7,1.3-7.2,4-9.9c1.1-1.1,1.1-2.7,0-3.7c-1.1-1.1-2.7-1.1-3.7,0c-3.7,3.7-5.6,8.5-5.6,13.6s2.1,9.9,5.6,13.6 c0.5,0.5,1.1,0.8,1.9,0.8s1.3-0.3,1.9-0.8c1.1-1.1,1.1-2.7,0-3.7C15.5,26.7,13.9,23.2,13.9,19.5z\"\n                                                      }\n                                                    }),\n                                                    _vm._v(\" \"),\n                                                    _c(\"path\", {\n                                                      attrs: {\n                                                        d:\n                                                          \"M49.9,5.9c-1.1-1.1-2.7-1.1-3.7,0s-1.1,2.7,0,3.7c2.7,2.7,4,6.1,4,9.9s-1.3,7.2-4,9.9c-1.1,1.1-1.1,2.7,0,3.7 c0.5,0.5,1.3,0.8,1.9,0.8c0.8,0,1.3-0.3,1.9-0.8c3.7-3.7,5.6-8.5,5.6-13.6C55.5,14.4,53.3,9.3,49.9,5.9z\"\n                                                      }\n                                                    }),\n                                                    _vm._v(\" \"),\n                                                    _c(\"path\", {\n                                                      attrs: {\n                                                        d:\n                                                          \"M5.3,19.5c0-5.6,2.1-10.9,6.1-14.9c1.1-1.1,1.1-2.7,0-3.7s-2.7-1.1-3.7,0C2.7,5.9,0,12.3,0,19.5s2.7,13.6,7.7,18.7 C8.2,38.7,8.8,39,9.6,39c0.8,0,1.3-0.3,1.9-0.8c1.1-1.1,1.1-2.7,0-3.7C7.5,30.4,5.3,25.1,5.3,19.5z\"\n                                                      }\n                                                    }),\n                                                    _vm._v(\" \"),\n                                                    _c(\"path\", {\n                                                      attrs: {\n                                                        d:\n                                                          \"M56.3,0.8c-1.1-1.1-2.7-1.1-3.7,0s-1.1,2.7,0,3.7c4,4,6.1,9.3,6.1,14.9s-2.1,10.9-6.1,14.9c-1.1,1.1-1.1,2.7,0,3.7 c0.5,0.5,1.3,0.8,1.9,0.8c0.8,0,1.3-0.3,1.9-0.8c5.1-5.1,7.7-11.7,7.7-18.7S61.3,5.9,56.3,0.8z\"\n                                                      }\n                                                    })\n                                                  ])\n                                                ]\n                                              )\n                                            : _vm._e()\n                                        ]\n                                      ),\n                                      _vm._v(\" \"),\n                                      _c(\"span\", [\n                                        _vm._v(_vm._s(event.vue.location))\n                                      ])\n                                    ]\n                                  )\n                                : _vm._e(),\n                              _vm._v(\" \"),\n                              _vm.feed.display_description\n                                ? _c(\n                                    \"p\",\n                                    {\n                                      staticClass: \"eventbrite-item-description\"\n                                    },\n                                    [_vm._v(_vm._s(event.vue.description))]\n                                  )\n                                : _vm._e(),\n                              _vm._v(\" \"),\n                              (_vm.feed.display_price ||\n                                _vm.feed.display_tickets) &&\n                              !_vm.feed.price_overlay\n                                ? _c(\n                                    \"div\",\n                                    { staticClass: \"eventbrite-item-tags\" },\n                                    [\n                                      _vm.feed.display_price && event.vue.price\n                                        ? _c(\"div\", {\n                                            staticClass:\n                                              \"eventbrite-item-price\",\n                                            domProps: {\n                                              innerHTML: _vm._s(event.vue.price)\n                                            }\n                                          })\n                                        : _vm._e(),\n                                      _vm._v(\" \"),\n                                      _vm.feed.display_tickets &&\n                                      event.tickets_remaining &&\n                                      event.tickets_remaining > 0\n                                        ? _c(\n                                            \"div\",\n                                            {\n                                              staticClass:\n                                                \"eventbrite-item-available-tickets\"\n                                            },\n                                            [\n                                              _vm._v(\n                                                _vm._s(\n                                                  event.tickets_remaining\n                                                ) +\n                                                  _vm._s(\n                                                    _vm.ticketsText(\n                                                      _vm.feed.tickets_text\n                                                    )\n                                                  )\n                                              )\n                                            ]\n                                          )\n                                        : _vm._e()\n                                    ]\n                                  )\n                                : _vm._e(),\n                              _vm._v(\" \"),\n                              _vm.feed.display_signup_button ||\n                              _vm.feed.display_more_button\n                                ? _c(\n                                    \"div\",\n                                    { staticClass: \"eventbrite-item-buttons\" },\n                                    [\n                                      event.event_sales_status.sales_status ==\n                                        \"on_sale\" &&\n                                      _vm.feed.display_signup_button\n                                        ? _c(\n                                            \"button\",\n                                            {\n                                              staticClass:\n                                                \"eventbrite-item-checkout\",\n                                              attrs: {\n                                                id:\n                                                  \"eventbrite-checkout-button-\" +\n                                                  event.ID\n                                              },\n                                              on: {\n                                                click: function($event) {\n                                                  return _vm.checkoutFromEventCard(\n                                                    event,\n                                                    eventIndex\n                                                  )\n                                                }\n                                              }\n                                            },\n                                            [\n                                              _vm._v(\n                                                \"\\n                            \" +\n                                                  _vm._s(\n                                                    _vm.checkoutButtonText(\n                                                      event\n                                                    )\n                                                  ) +\n                                                  \"\\n                        \"\n                                              )\n                                            ]\n                                          )\n                                        : _vm._e(),\n                                      _vm._v(\" \"),\n                                      _vm.feed.display_more_button\n                                        ? _c(\n                                            \"button\",\n                                            {\n                                              staticClass:\n                                                \"eventbrite-item-details\",\n                                              on: {\n                                                click: function($event) {\n                                                  return _vm.getEventDetails(\n                                                    event,\n                                                    eventIndex\n                                                  )\n                                                }\n                                              }\n                                            },\n                                            [\n                                              _vm._v(\n                                                \"\\n                            \" +\n                                                  _vm._s(\n                                                    _vm.feed.more_button_text\n                                                  ) +\n                                                  \"\\n                        \"\n                                              )\n                                            ]\n                                          )\n                                        : _vm._e()\n                                    ]\n                                  )\n                                : _vm._e()\n                            ]\n                          )\n                        : _vm._e()\n                    ])\n                  ]\n                )\n              })\n            ],\n            2\n          )\n        : _vm._e(),\n      _vm._v(\" \"),\n      _vm.events && _vm.events.length === 0\n        ? _c(\n            \"div\",\n            {\n              directives: [\n                {\n                  name: \"show\",\n                  rawName: \"v-show\",\n                  value: _vm.feedLoaded,\n                  expression: \"feedLoaded\"\n                }\n              ],\n              staticClass: \"eventbrite-info-message\"\n            },\n            [_vm._v(_vm._s(_vm.translations.no_events_text))]\n          )\n        : _vm._e(),\n      _vm._v(\" \"),\n      _vm.feedError == true\n        ? _c(\"div\", { staticClass: \"eventbrite-info-message\" }, [\n            _vm._v(_vm._s(_vm.translations.loading_error_text))\n          ])\n        : _vm._e(),\n      _vm._v(\" \"),\n      _vm.settings.api_key == false\n        ? _c(\"div\", { staticClass: \"eventbrite-info-message\" }, [\n            _vm._v(_vm._s(_vm.translations.api_error_text))\n          ])\n        : _vm._e(),\n      _vm._v(\" \"),\n      _vm.events && _vm.events.length > 0 && _vm.settings.show_copyright\n        ? _c(\n            \"div\",\n            {\n              directives: [\n                {\n                  name: \"show\",\n                  rawName: \"v-show\",\n                  value: _vm.feedLoaded,\n                  expression: \"feedLoaded\"\n                }\n              ],\n              staticClass: \"eventbrite-copyright eventbrite-copyright-left\"\n            },\n            [\n              _c(\"div\", [_vm._v(_vm._s(_vm.translations.copyright_text))]),\n              _vm._v(\" \"),\n              _vm._m(1)\n            ]\n          )\n        : _vm._e(),\n      _vm._v(\" \"),\n      _c(\n        \"Modal\",\n        {\n          attrs: {\n            \"modal-class\":\n              \"eventbrite-modal scrollable-modal eventbrite-modal-\" +\n              _vm.uid +\n              \" eventbrite-modal-id-\" +\n              _vm.feed.ID,\n            \"bg-class\": \"eventbrite-modal-bg\",\n            \"wrapper-class\": \"eventbrite-modal-wrapper\",\n            \"append-to\": \"#eventbrite-modal-container\",\n            \"base-zindex\": \"20000\",\n            live: false\n          },\n          on: {\n            \"before-open\": _vm.beforeOpen,\n            \"before-close\": _vm.beforeClose,\n            \"after-close\": _vm.afterClose,\n            \"after-open\": _vm.afterOpen\n          },\n          model: {\n            value: _vm.modalShow,\n            callback: function($$v) {\n              _vm.modalShow = $$v\n            },\n            expression: \"modalShow\"\n          }\n        },\n        [\n          _c(\n            \"div\",\n            {\n              directives: [\n                {\n                  name: \"scroll-lock\",\n                  rawName: \"v-scroll-lock\",\n                  value: _vm.modalShow,\n                  expression: \"modalShow\"\n                }\n              ],\n              staticClass: \"scroll-content\"\n            },\n            [\n              _vm.feed.popup\n                ? _c(\n                    \"div\",\n                    {\n                      directives: [\n                        {\n                          name: \"show\",\n                          rawName: \"v-show\",\n                          value: _vm.modalAddBackBtn == true,\n                          expression: \"modalAddBackBtn == true\"\n                        }\n                      ],\n                      staticClass: \"eventbrite-modal-back\"\n                    },\n                    [\n                      _c(\n                        \"button\",\n                        {\n                          attrs: {\n                            type: \"button\",\n                            title: _vm.translations.back_button_text\n                          },\n                          on: { click: _vm.backBtnClick }\n                        },\n                        [\n                          _c(\"i\", [\n                            _c(\"svg\", { attrs: { viewBox: \"0 0 24 24\" } }, [\n                              _c(\"path\", {\n                                attrs: {\n                                  \"fill-rule\": \"evenodd\",\n                                  \"clip-rule\": \"evenodd\",\n                                  d:\n                                    \"M4 12l8 8 1.5-1.5L8 13h12v-2H8l5.5-5.5L12 4z\"\n                                }\n                              })\n                            ])\n                          ])\n                        ]\n                      )\n                    ]\n                  )\n                : _vm._e(),\n              _vm._v(\" \"),\n              _c(\"div\", { staticClass: \"eventbrite-modal-close\" }, [\n                _c(\n                  \"button\",\n                  {\n                    attrs: {\n                      type: \"button\",\n                      title: _vm.translations.close_button_text\n                    },\n                    on: {\n                      click: function($event) {\n                        ;(_vm.modalShow = false) && (_vm.requestModal = false)\n                      }\n                    }\n                  },\n                  [\n                    _c(\"i\", [\n                      _c(\"svg\", { attrs: { viewBox: \"0 0 24 24\" } }, [\n                        _c(\"path\", {\n                          attrs: {\n                            d:\n                              \"M13.4 12l3.5-3.5-1.4-1.4-3.5 3.5-3.5-3.5-1.4 1.4 3.5 3.5-3.5 3.5 1.4 1.4 3.5-3.5 3.5 3.5 1.4-1.4z\"\n                          }\n                        })\n                      ])\n                    ])\n                  ]\n                )\n              ]),\n              _vm._v(\" \"),\n              _c(\n                \"div\",\n                {\n                  staticClass: \"eventbrite-modal-img\",\n                  attrs: { id: \"eventbrite-modal-img\" }\n                },\n                [\n                  _c(\"img\", {\n                    directives: [\n                      {\n                        name: \"show\",\n                        rawName: \"v-show\",\n                        value: _vm.modalImage !== false,\n                        expression: \"modalImage !== false\"\n                      }\n                    ],\n                    attrs: {\n                      src: _vm.modalImage,\n                      alt: _vm.modalEvent.post_title,\n                      width: \"800\",\n                      height: \"400\",\n                      loading: \"lazy\"\n                    }\n                  }),\n                  _vm._v(\" \"),\n                  _vm.feed.calendar_button == true &&\n                  _vm.license == true && _vm.free != true\n                    ? _c(\n                        \"div\",\n                        {\n                          staticClass:\n                            \"eventbrite-modal-add-to-calendar-wrapper\"\n                        },\n                        [\n                          _c(\n                            \"button\",\n                            {\n                              staticClass: \"eventbrite-modal-add-to-calendar\",\n                              attrs: { type: \"button\" },\n                              on: { click: _vm.toggleModalCalendarAdd }\n                            },\n                            [\n                              _c(\n                                \"svg\",\n                                {\n                                  staticClass: \"h-6 w-6\",\n                                  attrs: {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\"\n                                  }\n                                },\n                                [\n                                  _c(\"path\", {\n                                    attrs: {\n                                      \"stroke-linecap\": \"round\",\n                                      \"stroke-linejoin\": \"round\",\n                                      \"stroke-width\": \"2\",\n                                      d:\n                                        \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                    }\n                                  })\n                                ]\n                              ),\n                              _vm._v(\" \"),\n                              _c(\"span\", [\n                                _vm._v(\n                                  _vm._s(_vm.translations.add_to_calendar_text)\n                                )\n                              ])\n                            ]\n                          ),\n                          _vm._v(\" \"),\n                          _c(\n                            \"div\",\n                            {\n                              directives: [\n                                {\n                                  name: \"show\",\n                                  rawName: \"v-show\",\n                                  value: _vm.modalCalendarAdd,\n                                  expression: \"modalCalendarAdd\"\n                                }\n                              ],\n                              staticClass:\n                                \"eventbrite-modal-add-to-calendar-menu\"\n                            },\n                            [\n                              _c(\n                                \"button\",\n                                {\n                                  staticClass:\n                                    \"eventbrite-modal-add-to-calendar-menu-close\",\n                                  attrs: { type: \"button\" },\n                                  on: {\n                                    click: function($event) {\n                                      _vm.modalCalendarAdd = false\n                                    }\n                                  }\n                                },\n                                [\n                                  _c(\n                                    \"svg\",\n                                    {\n                                      staticStyle: {\n                                        \"enable-background\": \"new 0 0 64 64\"\n                                      },\n                                      attrs: {\n                                        version: \"1.1\",\n                                        id: \"Layer_1\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        \"xmlns:xlink\":\n                                          \"http://www.w3.org/1999/xlink\",\n                                        x: \"0px\",\n                                        y: \"0px\",\n                                        viewBox: \"0 0 64 64\",\n                                        \"xml:space\": \"preserve\"\n                                      }\n                                    },\n                                    [\n                                      _c(\"path\", {\n                                        attrs: {\n                                          d:\n                                            \"M35.4,32l19.9-19.9c1-1,1-2.4,0-3.4s-2.4-1-3.4,0L32,28.6L12,8.8c-0.9-1-2.4-1-3.3,0s-1,2.4,0,3.4L28.6,32L8.7,51.9\\n                                    c-1,1-1,2.4,0,3.4c0.5,0.4,1,0.7,1.7,0.7s1.2-0.2,1.7-0.7l20-19.9l20,19.8c0.5,0.4,1.2,0.7,1.7,0.7c0.5,0,1.2-0.2,1.7-0.7\\n                                    c1-1,1-2.4,0-3.4L35.4,32z\"\n                                        }\n                                      })\n                                    ]\n                                  )\n                                ]\n                              ),\n                              _vm._v(\" \"),\n                              _c(\n                                \"a\",\n                                {\n                                  staticClass:\n                                    \"eventbrite-modal-add-to-calendar-menu-item eventbrite-modal-add-to-calendar-menu-item-google\",\n                                  attrs: {\n                                    href: \"javascript:;\",\n                                    target: \"_blank\"\n                                  },\n                                  on: {\n                                    click: function($event) {\n                                      _vm.modalCalendarAdd = false\n                                    }\n                                  }\n                                },\n                                [_vm._v(\"Google Calendar\")]\n                              ),\n                              _vm._v(\" \"),\n                              _c(\n                                \"a\",\n                                {\n                                  staticClass:\n                                    \"eventbrite-modal-add-to-calendar-menu-item eventbrite-modal-add-to-calendar-menu-item-outlook\",\n                                  attrs: {\n                                    href: \"javascript:;\",\n                                    target: \"_blank\"\n                                  },\n                                  on: {\n                                    click: function($event) {\n                                      _vm.modalCalendarAdd = false\n                                    }\n                                  }\n                                },\n                                [_vm._v(\"Outlook Calendar\")]\n                              ),\n                              _vm._v(\" \"),\n                              _c(\n                                \"a\",\n                                {\n                                  staticClass:\n                                    \"eventbrite-modal-add-to-calendar-menu-item eventbrite-modal-add-to-calendar-menu-item-office\",\n                                  attrs: {\n                                    href: \"javascript:;\",\n                                    target: \"_blank\"\n                                  },\n                                  on: {\n                                    click: function($event) {\n                                      _vm.modalCalendarAdd = false\n                                    }\n                                  }\n                                },\n                                [_vm._v(\"Office365 Calendar\")]\n                              ),\n                              _vm._v(\" \"),\n                              _c(\n                                \"a\",\n                                {\n                                  staticClass:\n                                    \"eventbrite-modal-add-to-calendar-menu-item eventbrite-modal-add-to-calendar-menu-item-yahoo\",\n                                  attrs: {\n                                    href: \"javascript:;\",\n                                    target: \"_blank\"\n                                  },\n                                  on: {\n                                    click: function($event) {\n                                      _vm.modalCalendarAdd = false\n                                    }\n                                  }\n                                },\n                                [_vm._v(\"Yahoo Calendar\")]\n                              ),\n                              _vm._v(\" \"),\n                              _c(\n                                \"a\",\n                                {\n                                  staticClass:\n                                    \"eventbrite-modal-add-to-calendar-menu-item eventbrite-modal-add-to-calendar-menu-item-apple\",\n                                  attrs: { href: \"javascript:;\" },\n                                  on: {\n                                    click: function($event) {\n                                      _vm.modalCalendarAdd = false\n                                    }\n                                  }\n                                },\n                                [_vm._v(\"Download ICS file\")]\n                              )\n                            ]\n                          )\n                        ]\n                      )\n                    : _vm._e()\n                ]\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"div\",\n                {\n                  directives: [\n                    {\n                      name: \"show\",\n                      rawName: \"v-show\",\n                      value: !_vm.modalTickets,\n                      expression: \"!modalTickets\"\n                    }\n                  ],\n                  staticClass: \"eventbrite-modal-title\"\n                },\n                [\n                  _vm.modalEvent.vue &&\n                  _vm.modalEvent.vue.start_full &&\n                  _vm.modalEvent.vue.end_full\n                    ? _c(\"div\", { staticClass: \"eventbrite-modal-datetime\" }, [\n                        _c(\"time\", [\n                          _vm._v(\n                            \"\\n                        \" +\n                              _vm._s(_vm.modalEvent.vue.start_full) +\n                              \" - \" +\n                              _vm._s(_vm.modalEvent.vue.end_full) +\n                              \"\\n                    \"\n                          )\n                        ])\n                      ])\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.modalEvent.post_title\n                    ? _c(\"h3\", [_vm._v(_vm._s(_vm.modalEvent.post_title))])\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.modalEvent.vue && _vm.modalEvent.vue.location\n                    ? _c(\n                        \"div\",\n                        {\n                          staticClass: \"eventbrite-modal-location\",\n                          class: {\n                            \"eventbrite-modal-location-clickable\":\n                              _vm.modalEvent.venue.address &&\n                              _vm.feed.calendar_button == true\n                          },\n                          on: {\n                            click: function($event) {\n                              return _vm.modalScrollToMap(\n                                _vm.modalEvent.vue.location\n                              )\n                            }\n                          }\n                        },\n                        [\n                          _vm.modalEvent.vue.location !== \"Online\"\n                            ? _c(\n                                \"svg\",\n                                {\n                                  staticClass: \"eventbrite-modal-location-icon\",\n                                  staticStyle: {\n                                    \"enable-background\": \"new 0 0 48 64\"\n                                  },\n                                  attrs: {\n                                    version: \"1.1\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    \"xmlns:xlink\":\n                                      \"http://www.w3.org/1999/xlink\",\n                                    x: \"0px\",\n                                    y: \"0px\",\n                                    viewBox: \"0 0 48 64\",\n                                    \"xml:space\": \"preserve\"\n                                  }\n                                },\n                                [\n                                  _c(\"g\", [\n                                    _c(\"path\", {\n                                      attrs: {\n                                        d:\n                                          \"M24,0C10.7,0,0,11.2,0,25.3c0,12,16.5,31.7,21.6,37.6c0.5,0.8,1.6,1.1,2.4,1.1c1.1,0,1.9-0.5,2.4-1.1 C31.5,57.1,48,37.1,48,25.3C48,11.2,37.3,0,24,0z M24,57.6C14.9,46.9,5.3,32.8,5.3,25.3c0-11.2,8.3-20,18.7-20s18.7,9.1,18.7,20 C42.7,32.8,33.1,46.9,24,57.6z\"\n                                      }\n                                    }),\n                                    _vm._v(\" \"),\n                                    _c(\"path\", {\n                                      attrs: {\n                                        d:\n                                          \"M24,13.3c-5.9,0-10.7,4.8-10.7,10.7S18.1,34.7,24,34.7S34.7,29.9,34.7,24S29.9,13.3,24,13.3z M24,29.3 c-2.9,0-5.3-2.4-5.3-5.3s2.4-5.3,5.3-5.3s5.3,2.4,5.3,5.3S26.9,29.3,24,29.3z\"\n                                      }\n                                    })\n                                  ])\n                                ]\n                              )\n                            : _vm._e(),\n                          _vm._v(\" \"),\n                          _vm.modalEvent.vue.location == \"Online\"\n                            ? _c(\n                                \"svg\",\n                                {\n                                  staticClass:\n                                    \"eventbrite-modal-location-icon-online\",\n                                  staticStyle: {\n                                    \"enable-background\": \"new 0 0 64.1 48\"\n                                  },\n                                  attrs: {\n                                    version: \"1.1\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    \"xmlns:xlink\":\n                                      \"http://www.w3.org/1999/xlink\",\n                                    x: \"0px\",\n                                    y: \"0px\",\n                                    viewBox: \"0 0 64.1 48\",\n                                    \"xml:space\": \"preserve\"\n                                  }\n                                },\n                                [\n                                  _c(\"g\", [\n                                    _c(\"path\", {\n                                      attrs: {\n                                        d:\n                                          \"M32,13.9c-3.5,0-6.4,2.9-6.4,6.1c0,2.4,1.6,4.5,3.7,5.6v19.7c0,1.6,1.1,2.7,2.7,2.7s2.7-1.1,2.7-2.7V25.9 c2.1-1.1,3.7-3.2,3.7-5.6C38.4,16.8,35.5,13.9,32,13.9z M32,19.2c0.5,0,1.1,0.5,1.1,0.8s-0.5,0.8-1.1,0.8c-0.5,0-1.1-0.5-1.1-0.8 S31.5,19.2,32,19.2z\"\n                                      }\n                                    }),\n                                    _vm._v(\" \"),\n                                    _c(\"path\", {\n                                      attrs: {\n                                        d:\n                                          \"M24.5,10.9c-1.1-1.1-2.7-1.1-3.7,0c-2.4,2.4-3.5,5.3-3.5,8.5s1.3,6.4,3.5,8.8c0.5,0.5,1.3,0.8,1.9,0.8 c0.8,0,1.3-0.3,1.9-0.8c1.1-1.1,1.1-2.7,0-3.7c-1.3-1.3-2.1-2.9-2.1-4.8s0.8-3.5,2.1-4.8C25.6,13.6,25.6,12,24.5,10.9z\"\n                                      }\n                                    }),\n                                    _vm._v(\" \"),\n                                    _c(\"path\", {\n                                      attrs: {\n                                        d:\n                                          \"M43.2,10.9c-1.1-1.1-2.7-1.1-3.7,0c-1.1,1.1-1.1,2.7,0,3.7c1.3,1.3,2.1,2.9,2.1,4.8s-0.8,3.5-2.1,4.8 c-1.1,1.1-1.1,2.7,0,3.7c0.5,0.5,1.3,0.8,1.9,0.8c0.8,0,1.3-0.3,1.9-0.8c2.4-2.4,3.5-5.3,3.5-8.8C46.9,16.3,45.6,13.1,43.2,10.9z\"\n                                      }\n                                    }),\n                                    _vm._v(\" \"),\n                                    _c(\"path\", {\n                                      attrs: {\n                                        d:\n                                          \"M13.9,19.5c0-3.7,1.3-7.2,4-9.9c1.1-1.1,1.1-2.7,0-3.7c-1.1-1.1-2.7-1.1-3.7,0c-3.7,3.7-5.6,8.5-5.6,13.6s2.1,9.9,5.6,13.6 c0.5,0.5,1.1,0.8,1.9,0.8s1.3-0.3,1.9-0.8c1.1-1.1,1.1-2.7,0-3.7C15.5,26.7,13.9,23.2,13.9,19.5z\"\n                                      }\n                                    }),\n                                    _vm._v(\" \"),\n                                    _c(\"path\", {\n                                      attrs: {\n                                        d:\n                                          \"M49.9,5.9c-1.1-1.1-2.7-1.1-3.7,0s-1.1,2.7,0,3.7c2.7,2.7,4,6.1,4,9.9s-1.3,7.2-4,9.9c-1.1,1.1-1.1,2.7,0,3.7 c0.5,0.5,1.3,0.8,1.9,0.8c0.8,0,1.3-0.3,1.9-0.8c3.7-3.7,5.6-8.5,5.6-13.6C55.5,14.4,53.3,9.3,49.9,5.9z\"\n                                      }\n                                    }),\n                                    _vm._v(\" \"),\n                                    _c(\"path\", {\n                                      attrs: {\n                                        d:\n                                          \"M5.3,19.5c0-5.6,2.1-10.9,6.1-14.9c1.1-1.1,1.1-2.7,0-3.7s-2.7-1.1-3.7,0C2.7,5.9,0,12.3,0,19.5s2.7,13.6,7.7,18.7 C8.2,38.7,8.8,39,9.6,39c0.8,0,1.3-0.3,1.9-0.8c1.1-1.1,1.1-2.7,0-3.7C7.5,30.4,5.3,25.1,5.3,19.5z\"\n                                      }\n                                    }),\n                                    _vm._v(\" \"),\n                                    _c(\"path\", {\n                                      attrs: {\n                                        d:\n                                          \"M56.3,0.8c-1.1-1.1-2.7-1.1-3.7,0s-1.1,2.7,0,3.7c4,4,6.1,9.3,6.1,14.9s-2.1,10.9-6.1,14.9c-1.1,1.1-1.1,2.7,0,3.7 c0.5,0.5,1.3,0.8,1.9,0.8c0.8,0,1.3-0.3,1.9-0.8c5.1-5.1,7.7-11.7,7.7-18.7S61.3,5.9,56.3,0.8z\"\n                                      }\n                                    })\n                                  ])\n                                ]\n                              )\n                            : _vm._e(),\n                          _vm._v(\" \"),\n                          _c(\"span\", [\n                            _vm._v(_vm._s(_vm.modalEvent.vue.location))\n                          ])\n                        ]\n                      )\n                    : _vm._e()\n                ]\n              ),\n              _vm._v(\" \"),\n              _c(\"div\", { staticClass: \"eventbrite-modal-content\" }, [\n                _c(\"div\", { staticClass: \"replace-content\" }, [\n                  _c(\n                    \"div\",\n                    {\n                      directives: [\n                        {\n                          name: \"show\",\n                          rawName: \"v-show\",\n                          value: !_vm.modalTickets,\n                          expression: \"!modalTickets\"\n                        }\n                      ],\n                      staticClass: \"eventbrite-modal-event-info\"\n                    },\n                    [\n                      _vm.modalEvent.vue && _vm.modalEvent.post_content\n                        ? _c(\"p\", { staticClass: \"eventbrite-modal-summary\" }, [\n                            _vm._v(_vm._s(_vm.modalEvent.post_content))\n                          ])\n                        : _vm._e(),\n                      _vm._v(\" \"),\n                      _vm._l(_vm.modalDescription, function(description) {\n                        return _c(\"div\", {\n                          key: description.ID,\n                          staticClass: \"eventbrite-modal-description-module\",\n                          class: {\n                            \"eventbrite-modal-description-module-text\":\n                              description.data.body,\n                            \"eventbrite-modal-description-module-img\":\n                              description.data.image,\n                            \"eventbrite-modal-description-module-video\":\n                              description.data.video\n                          },\n                          domProps: {\n                            innerHTML: _vm._s(_vm.moduleContent(description))\n                          }\n                        })\n                      }),\n                      _vm._v(\" \"),\n                      _vm.modalError == true\n                        ? _c(\n                            \"div\",\n                            {\n                              staticClass:\n                                \"eventbrite-modal-description-module eventbrite-modal-description-module-text eventbrite-modal-description-error\"\n                            },\n                            [\n                              _c(\"p\", [\n                                _vm._v(\n                                  _vm._s(_vm.translations.modal_error_text) +\n                                    \" \"\n                                ),\n                                _c(\"br\"),\n                                _c(\n                                  \"a\",\n                                  {\n                                    attrs: {\n                                      href: _vm.modalEvent.url,\n                                      target:\n                                        _vm.feed.link_target_blank == true\n                                          ? \"_blank\"\n                                          : \"_self\"\n                                    }\n                                  },\n                                  [\n                                    _vm._v(\n                                      _vm._s(_vm.translations.modal_error_link)\n                                    )\n                                  ]\n                                )\n                              ])\n                            ]\n                          )\n                        : _vm._e()\n                    ],\n                    2\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"div\",\n                    {\n                      directives: [\n                        {\n                          name: \"show\",\n                          rawName: \"v-show\",\n                          value: _vm.modalTickets,\n                          expression: \"modalTickets\"\n                        }\n                      ],\n                      staticClass: \"eventbrite-modal-event-tickets\"\n                    },\n                    [\n                      _c(\n                        \"div\",\n                        {\n                          staticClass:\n                            \"eventbrite-modal-content-spinner-wrapper\"\n                        },\n                        [\n                          _c(\n                            \"div\",\n                            { staticClass: \"eventbrite-modal-spinner\" },\n                            [\n                              _c(\"div\", { staticClass: \"double-bounce1\" }),\n                              _vm._v(\" \"),\n                              _c(\"div\", { staticClass: \"double-bounce2\" })\n                            ]\n                          )\n                        ]\n                      ),\n                      _vm._v(\" \"),\n                      _c(\"div\", {\n                        staticStyle: {\n                          position: \"relative\",\n                          \"background-color\": \"#fff\",\n                          \"z-index\": \"1\"\n                        },\n                        attrs: { id: \"eventbrite-modal-event-tickets\" }\n                      })\n                    ]\n                  )\n                ])\n              ]),\n              _vm._v(\" \"),\n              _vm.modalEvent.vue &&\n              _vm.modalEvent.venue.address &&\n              _vm.feed.google_map == true\n                ? _c(\n                    \"div\",\n                    {\n                      directives: [\n                        {\n                          name: \"show\",\n                          rawName: \"v-show\",\n                          value: !_vm.modalTickets,\n                          expression: \"!modalTickets\"\n                        }\n                      ],\n                      staticClass: \"eventbrite-modal-map\",\n                      attrs: { id: \"eventbrite-modal-map\" }\n                    },\n                    [\n                      _c(\"iframe\", {\n                        attrs: {\n                          width: \"100%\",\n                          height: \"250\",\n                          frameborder: \"0\",\n                          scrolling: \"no\",\n                          marginheight: \"0\",\n                          marginwidth: \"0\",\n                          src: _vm.googleMapsSrc(this.modalEvent.vue.location),\n                          loading: \"lazy\"\n                        }\n                      })\n                    ]\n                  )\n                : _vm._e(),\n              _vm._v(\" \"),\n              _vm.modalEvent.organizer &&\n              _vm.modalEvent.organizer.name &&\n              _vm.feed.organizer_info == true\n                ? _c(\n                    \"div\",\n                    {\n                      directives: [\n                        {\n                          name: \"show\",\n                          rawName: \"v-show\",\n                          value: !_vm.modalTickets,\n                          expression: \"!modalTickets\"\n                        }\n                      ],\n                      staticClass: \"eventbrite-modal-organizer\"\n                    },\n                    [\n                      _c(\n                        \"div\",\n                        { staticClass: \"eventbrite-modal-organizer-row\" },\n                        [\n                          _vm.modalOrganizer &&\n                          _vm.modalOrganizer.logo &&\n                          _vm.modalOrganizer.logo.url\n                            ? _c(\n                                \"div\",\n                                {\n                                  staticClass:\n                                    \"eventbrite-modal-organizer-image\"\n                                },\n                                [\n                                  _c(\"img\", {\n                                    attrs: {\n                                      src: _vm.modalOrganizer.logo.url,\n                                      alt: \"modalOrganizer.name\"\n                                    }\n                                  })\n                                ]\n                              )\n                            : _vm._e(),\n                          _vm._v(\" \"),\n                          _c(\n                            \"div\",\n                            { staticClass: \"eventbrite-modal-organizer-info\" },\n                            [\n                              _vm.modalEvent.organizer.name\n                                ? _c(\n                                    \"div\",\n                                    {\n                                      staticClass:\n                                        \"eventbrite-modal-organizer-name\"\n                                    },\n                                    [\n                                      _c(\"span\", [\n                                        _vm._v(\n                                          _vm._s(_vm.modalEvent.organizer.name)\n                                        )\n                                      ])\n                                    ]\n                                  )\n                                : _vm._e(),\n                              _vm._v(\" \"),\n                              _c(\n                                \"div\",\n                                {\n                                  staticClass:\n                                    \"eventbrite-modal-organizer-title\"\n                                },\n                                [\n                                  _vm._v(\n                                    _vm._s(_vm.translations.organizer_title)\n                                  )\n                                ]\n                              ),\n                              _vm._v(\" \"),\n                              _vm.modalEvent.organizer.description\n                                ? _c(\"div\", {\n                                    staticClass:\n                                      \"eventbrite-modal-organizer-description\",\n                                    domProps: {\n                                      innerHTML: _vm._s(\n                                        _vm.modalEvent.organizer.description\n                                          .html\n                                      )\n                                    }\n                                  })\n                                : _vm._e(),\n                              _vm._v(\" \"),\n                              _vm.modalOrganizer\n                                ? _c(\n                                    \"div\",\n                                    {\n                                      staticClass:\n                                        \"eventbrite-modal-organizer-social\"\n                                    },\n                                    [\n                                      _vm.modalOrganizer.facebook\n                                        ? _c(\n                                            \"a\",\n                                            {\n                                              staticClass:\n                                                \"eventbrite-modal-organizer-social-twitter\",\n                                              attrs: {\n                                                href:\n                                                  \"https://www.facebook.com/\" +\n                                                  _vm.modalOrganizer.facebook,\n                                                target:\n                                                  _vm.feed.link_target_blank ==\n                                                  true\n                                                    ? \"_blank\"\n                                                    : \"_self\",\n                                                title: \"Facebook\"\n                                              }\n                                            },\n                                            [\n                                              _c(\n                                                \"svg\",\n                                                {\n                                                  staticClass:\n                                                    \"svg-inline--fa fa-facebook-f fa-w-10\",\n                                                  attrs: {\n                                                    xmlns:\n                                                      \"http://www.w3.org/2000/svg\",\n                                                    \"aria-hidden\": \"true\",\n                                                    focusable: \"false\",\n                                                    \"data-prefix\": \"fab\",\n                                                    \"data-icon\": \"facebook-f\",\n                                                    role: \"img\",\n                                                    viewBox: \"0 0 320 512\"\n                                                  }\n                                                },\n                                                [\n                                                  _c(\"path\", {\n                                                    attrs: {\n                                                      fill: \"currentColor\",\n                                                      d:\n                                                        \"M279.14 288l14.22-92.66h-88.91v-60.13c0-25.35 12.42-50.06 52.24-50.06h40.42V6.26S260.43 0 225.36 0c-73.22 0-121.08 44.38-121.08 124.72v70.62H22.89V288h81.39v224h100.17V288z\"\n                                                    }\n                                                  })\n                                                ]\n                                              )\n                                            ]\n                                          )\n                                        : _vm._e(),\n                                      _vm._v(\" \"),\n                                      _vm.modalOrganizer.twitter\n                                        ? _c(\n                                            \"a\",\n                                            {\n                                              staticClass:\n                                                \"eventbrite-modal-organizer-social-website\",\n                                              attrs: {\n                                                href:\n                                                  \"https://www.twitter.com/\" +\n                                                  _vm.modalOrganizer.twitter,\n                                                target:\n                                                  _vm.feed.link_target_blank ==\n                                                  true\n                                                    ? \"_blank\"\n                                                    : \"_self\",\n                                                title: \"Twitter\"\n                                              }\n                                            },\n                                            [\n                                              _c(\n                                                \"svg\",\n                                                {\n                                                  staticClass:\n                                                    \"svg-inline--fa fa-twitter fa-w-16\",\n                                                  attrs: {\n                                                    xmlns:\n                                                      \"http://www.w3.org/2000/svg\",\n                                                    \"aria-hidden\": \"true\",\n                                                    focusable: \"false\",\n                                                    \"data-prefix\": \"fab\",\n                                                    \"data-icon\": \"twitter\",\n                                                    role: \"img\",\n                                                    viewBox: \"0 0 512 512\"\n                                                  }\n                                                },\n                                                [\n                                                  _c(\"path\", {\n                                                    attrs: {\n                                                      fill: \"currentColor\",\n                                                      d:\n                                                        \"M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z\"\n                                                    }\n                                                  })\n                                                ]\n                                              )\n                                            ]\n                                          )\n                                        : _vm._e(),\n                                      _vm._v(\" \"),\n                                      _vm.modalEvent.organizer.website\n                                        ? _c(\n                                            \"a\",\n                                            {\n                                              staticClass:\n                                                \"eventbrite-modal-organizer-social-facebook\",\n                                              attrs: {\n                                                href:\n                                                  _vm.modalEvent.organizer\n                                                    .website,\n                                                target:\n                                                  _vm.feed.link_target_blank ==\n                                                  true\n                                                    ? \"_blank\"\n                                                    : \"_self\",\n                                                title:\n                                                  _vm.translations\n                                                    .organizer_link\n                                              }\n                                            },\n                                            [\n                                              _c(\n                                                \"svg\",\n                                                {\n                                                  staticClass:\n                                                    \"svg-inline--fa fa-globe fa-w-16\",\n                                                  attrs: {\n                                                    xmlns:\n                                                      \"http://www.w3.org/2000/svg\",\n                                                    \"aria-hidden\": \"true\",\n                                                    focusable: \"false\",\n                                                    \"data-prefix\": \"far\",\n                                                    \"data-icon\": \"globe\",\n                                                    role: \"img\",\n                                                    viewBox: \"0 0 496 512\"\n                                                  }\n                                                },\n                                                [\n                                                  _c(\"path\", {\n                                                    attrs: {\n                                                      fill: \"currentColor\",\n                                                      d:\n                                                        \"M248 8C111 8 0 119 0 256s111 248 248 248 248-111 248-248S385 8 248 8zm179.3 160h-67.2c-6.7-36.5-17.5-68.8-31.2-94.7 42.9 19 77.7 52.7 98.4 94.7zM248 56c18.6 0 48.6 41.2 63.2 112H184.8C199.4 97.2 229.4 56 248 56zM48 256c0-13.7 1.4-27.1 4-40h77.7c-1 13.1-1.7 26.3-1.7 40s.7 26.9 1.7 40H52c-2.6-12.9-4-26.3-4-40zm20.7 88h67.2c6.7 36.5 17.5 68.8 31.2 94.7-42.9-19-77.7-52.7-98.4-94.7zm67.2-176H68.7c20.7-42 55.5-75.7 98.4-94.7-13.7 25.9-24.5 58.2-31.2 94.7zM248 456c-18.6 0-48.6-41.2-63.2-112h126.5c-14.7 70.8-44.7 112-63.3 112zm70.1-160H177.9c-1.1-12.8-1.9-26-1.9-40s.8-27.2 1.9-40h140.3c1.1 12.8 1.9 26 1.9 40s-.9 27.2-2 40zm10.8 142.7c13.7-25.9 24.4-58.2 31.2-94.7h67.2c-20.7 42-55.5 75.7-98.4 94.7zM366.3 296c1-13.1 1.7-26.3 1.7-40s-.7-26.9-1.7-40H444c2.6 12.9 4 26.3 4 40s-1.4 27.1-4 40h-77.7z\"\n                                                    }\n                                                  })\n                                                ]\n                                              )\n                                            ]\n                                          )\n                                        : _vm._e()\n                                    ]\n                                  )\n                                : _vm._e()\n                            ]\n                          )\n                        ]\n                      )\n                    ]\n                  )\n                : _vm._e()\n            ]\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            {\n              directives: [\n                {\n                  name: \"show\",\n                  rawName: \"v-show\",\n                  value: !_vm.modalTickets,\n                  expression: \"!modalTickets\"\n                }\n              ],\n              staticClass: \"noscroll-content\"\n            },\n            [\n              _vm.feed.eventbrite_link == true\n                ? _c(\"div\", { staticClass: \"eventbrite-modal-footer-left\" }, [\n                    _c(\n                      \"a\",\n                      {\n                        staticClass: \"eventbrite-modal-external-button\",\n                        attrs: {\n                          href: _vm.modalEvent.url,\n                          target:\n                            _vm.feed.link_target_blank == true\n                              ? \"_blank\"\n                              : \"_self\"\n                        }\n                      },\n                      [\n                        _c(\"span\", { staticClass: \"desktop\" }, [\n                          _vm._v(_vm._s(_vm.translations.eventbrite_link) + \" \")\n                        ]),\n                        _c(\"span\", { staticClass: \"mobile\" }, [\n                          _vm._v(\"Eventbrite\")\n                        ])\n                      ]\n                    )\n                  ])\n                : _vm._e(),\n              _vm._v(\" \"),\n              _c(\"div\", { staticClass: \"eventbrite-modal-footer-right\" }, [\n                _vm.modalEvent.vue &&\n                _vm.modalEvent.vue.price &&\n                _vm.feed.display_price\n                  ? _c(\"div\", {\n                      staticClass: \"eventbrite-modal-price\",\n                      domProps: { innerHTML: _vm._s(_vm.modalEvent.vue.price) }\n                    })\n                  : _vm._e(),\n                _vm._v(\" \"),\n                _vm.modalEvent &&\n                _vm.modalEvent.event_sales_status.sales_status == \"on_sale\"\n                  ? _c(\n                      \"button\",\n                      {\n                        staticClass: \"eventbrite-modal-checkout-button\",\n                        on: {\n                          click: function($event) {\n                            return _vm.toOrder(\n                              _vm.modalEvent,\n                              _vm.modalEventIndex\n                            )\n                          }\n                        }\n                      },\n                      [\n                        _vm._v(\n                          \"\\n                    \" +\n                            _vm._s(_vm.checkoutButtonText(_vm.modalEvent)) +\n                            \"\\n                \"\n                        )\n                      ]\n                    )\n                  : _vm._e()\n              ])\n            ]\n          )\n        ]\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"div\",\n        {\n          class:\n            \"eventbrite-modal-spinner-wrapper eventbrite-modal-spinner-wrapper-\" +\n            _vm.uid\n        },\n        [_vm._m(2)]\n      )\n    ],\n    1\n  )\n}\nvar staticRenderFns = [\n  function() {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"div\", { staticClass: \"eventbrite-modal-spinner\" }, [\n      _c(\"div\", { staticClass: \"double-bounce1\" }),\n      _vm._v(\" \"),\n      _c(\"div\", { staticClass: \"double-bounce2\" })\n    ])\n  },\n  function() {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\n      \"a\",\n      { attrs: { href: \"https://eventfeed.click/\", target: \"_blank\" } },\n      [\n        _c(\"span\", { staticClass: \"brand-text\" }, [\n          _c(\"strong\", [_vm._v(\"Event Feed\")]),\n          _vm._v(\" for Eventbrite\")\n        ])\n      ]\n    )\n  },\n  function() {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"div\", { staticClass: \"eventbrite-modal-spinner\" }, [\n      _c(\"div\", { staticClass: \"double-bounce1\" }),\n      _vm._v(\" \"),\n      _c(\"div\", { staticClass: \"double-bounce2\" })\n    ])\n  }\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "<template>\n    \n    <div>\n\n        <!-- Event feed preloader -->\n        <div :class=\"'eventbrite-modal-spinner-feed eventbrite-modal-spinner-feed-' + uid\" v-show=\"(settings.api_key) && !feedError && !feedLoaded\" v-cloak>\n            <div class=\"eventbrite-modal-spinner\">\n                <div class=\"double-bounce1\"></div>\n                <div class=\"double-bounce2\"></div>\n            </div>\n            <div class=\"eventbrite-modal-spinner-text\">\n                <p>{{ translations.loading_text }}</p>\n            </div>\n        </div>\n\n        <!-- Events wrapper -->\n        <div :id=\"feed.css_id\" :class=\"'eventbrite-feed eventbrite-feed-id-' + feed.ID + ' eventbrite-feed-widget ' + feed.css_classes\" v-if=\"(settings.api_key) && (events) && (events.length > 0)\" v-show=\"feedLoaded\" v-cloak>\n\n            <!-- Event feed edit link -->\n            <a class=\"eventbrite-feed-edit-link\" target=\"_blank\" v-if=\"admin && edit_url\" :href=\"edit_url\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" fill=\"#334ecd\" width=\"16\" height=\"16\" version=\"1.1\" id=\"lni_lni-pencil-alt\" x=\"0px\" y=\"0px\" viewBox=\"0 0 64 64\" style=\"enable-background:new 0 0 64 64;\" xml:space=\"preserve\">\n                    <path d=\"M62.7,11.2c0-0.7-0.3-1.3-0.8-1.8c-1.3-1.3-2.5-2.5-3.7-3.7c-1.1-1.1-2.2-2.2-3.3-3.4c-0.4-0.5-1-0.9-1.6-1  c-0.7-0.1-1.5,0.1-2.1,0.6l-7.2,7.2H8.7c-4.1,0-7.4,3.3-7.4,7.4v38.9c0,4.1,3.3,7.4,7.4,7.4h38.9c4.1,0,7.4-3.3,7.4-7.4V19.9  l6.9-6.9C62.4,12.5,62.7,11.8,62.7,11.2z M33.3,36.6c-0.1,0.1-0.3,0.2-0.4,0.3l-8.6,2.9l2.8-8.6c0.1-0.2,0.1-0.3,0.3-0.4l19-19  l6,5.9L33.3,36.6z M51.5,55.4c0,2.1-1.7,3.9-3.9,3.9H8.7c-2.1,0-3.9-1.7-3.9-3.9V16.4c0-2.1,1.7-3.9,3.9-3.9h31.9L24.9,28.2  c-0.5,0.5-0.9,1.1-1.1,1.8l-3.8,11.6c-0.2,0.6-0.1,1.2,0.2,1.7c0.3,0.4,0.7,0.8,1.6,0.8h0.3l11.9-3.9c0.7-0.2,1.3-0.6,1.8-1.1  l15.8-15.7V55.4z M54.8,15.1l-6-5.9l4-4c1,1,1.9,1.9,2.9,2.9c1,1,2,2,3,3.1L54.8,15.1z\"/>\n                </svg>\n                <span>{{ translations.edit_link_text }}</span>\n            </a>\n            \n            <!-- Event -->\n            <div :class=\"['eventbrite-item', (['on_sale', 'not_yet_on_sale', 'sales_ended', 'sold_out', 'unavailable'].includes(event.event_sales_status.sales_status) ? event.event_sales_status.sales_status : '')]\" v-for=\"(event, eventIndex) in events\" :key=\"event.ID\" :data-event-id=\"event.ID\" :data-event-index=\"eventIndex\">\n\n                <!-- Image wrapper -->\n                <div class=\"eventbrite-item-image\" v-if=\"feed.display_image && event.url\">\n\n                    <div class=\"eventbrite-item-image-inner\" @click=\"getEventDetails(event, eventIndex)\">\n                            \n                        <!-- Image -->\n                        <img :src=\"event.vue.image\" :alt=\"event.post_title\" v-if=\"event.vue.image\" width=\"400\" height=\"200\" loading=\"lazy\">\n\n                        <!-- Tags -->\n                        <span class=\"eventbrite-item-image-tags\" v-if=\"( feed.display_price || feed.display_tickets ) && feed.price_overlay\">\n\n                            <!-- Price -->\n                            <span class=\"eventbrite-item-image-price\" v-if=\"feed.display_price && event.vue.price\" v-html=\"event.vue.price\"></span>\n\n                            <!-- Tickets left -->\n                            <span class=\"eventbrite-item-image-available-tickets\" v-if=\"feed.display_tickets && event.tickets_remaining && ( event.tickets_remaining > 0 )\">{{ event.tickets_remaining }}{{ ticketsText( feed.tickets_text ) }}</span>\n                            \n                        </span>\n\n                    </div>\n\n                </div>\n                    \n                <!-- Event content -->\n                <div class=\"eventbrite-item-content\">\n                    \n                    <div class=\"eventbrite-item-top-row\">\n\n                        <div class=\"eventbrite-item-title-col\">\n\n                            <!-- Date and time -->\n                            <time class=\"eventbrite-item-datetime\" v-if=\"feed.display_datetime && event.vue.start\">\n                                {{ event.vue.start }} - {{ event.vue.end }}\n                            </time>\n\n                            <!-- Title -->\n                            <h3 class=\"eventbrite-item-title\" v-if=\"event.vue.title\" @click=\"getEventDetails(event, eventIndex)\">\n                                {{ event.vue.title }}\n                            </h3>\n\n                        </div>\n\n                    </div>\n\n                    <div class=\"eventbrite-item-bottom-row\" v-if=\"(feed.display_location) || (feed.display_description) || (feed.display_price && !feed.display_image && event.vue.price) || (feed.display_signup_button) || (feed.display_more_button)\">\n\n                        <!-- Location -->\n                        <p class=\"eventbrite-item-location\" v-if=\"feed.display_location\">\n                            <span class=\"eventbrite-item-location-icon-wrapper\">\n                                <svg version=\"1.1\" class=\"eventbrite-item-location-icon\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\" viewBox=\"0 0 48 64\" style=\"enable-background:new 0 0 48 64;\" xml:space=\"preserve\" v-if=\"event.vue.location !== 'Online'\">\n                                    <g>\n                                        <path d=\"M24,0C10.7,0,0,11.2,0,25.3c0,12,16.5,31.7,21.6,37.6c0.5,0.8,1.6,1.1,2.4,1.1c1.1,0,1.9-0.5,2.4-1.1 C31.5,57.1,48,37.1,48,25.3C48,11.2,37.3,0,24,0z M24,57.6C14.9,46.9,5.3,32.8,5.3,25.3c0-11.2,8.3-20,18.7-20s18.7,9.1,18.7,20 C42.7,32.8,33.1,46.9,24,57.6z\"/>\n                                        <path d=\"M24,13.3c-5.9,0-10.7,4.8-10.7,10.7S18.1,34.7,24,34.7S34.7,29.9,34.7,24S29.9,13.3,24,13.3z M24,29.3 c-2.9,0-5.3-2.4-5.3-5.3s2.4-5.3,5.3-5.3s5.3,2.4,5.3,5.3S26.9,29.3,24,29.3z\"/>\n                                    </g>\n                                </svg>\n                                <svg version=\"1.1\" class=\"eventbrite-item-location-icon-online\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\" viewBox=\"0 0 64.1 48\" style=\"enable-background:new 0 0 64.1 48;\" xml:space=\"preserve\" v-if=\"event.vue.location == 'Online'\">\n                                    <g>\n                                        <path d=\"M32,13.9c-3.5,0-6.4,2.9-6.4,6.1c0,2.4,1.6,4.5,3.7,5.6v19.7c0,1.6,1.1,2.7,2.7,2.7s2.7-1.1,2.7-2.7V25.9 c2.1-1.1,3.7-3.2,3.7-5.6C38.4,16.8,35.5,13.9,32,13.9z M32,19.2c0.5,0,1.1,0.5,1.1,0.8s-0.5,0.8-1.1,0.8c-0.5,0-1.1-0.5-1.1-0.8 S31.5,19.2,32,19.2z\"/>\n                                        <path d=\"M24.5,10.9c-1.1-1.1-2.7-1.1-3.7,0c-2.4,2.4-3.5,5.3-3.5,8.5s1.3,6.4,3.5,8.8c0.5,0.5,1.3,0.8,1.9,0.8 c0.8,0,1.3-0.3,1.9-0.8c1.1-1.1,1.1-2.7,0-3.7c-1.3-1.3-2.1-2.9-2.1-4.8s0.8-3.5,2.1-4.8C25.6,13.6,25.6,12,24.5,10.9z\"/>\n                                        <path d=\"M43.2,10.9c-1.1-1.1-2.7-1.1-3.7,0c-1.1,1.1-1.1,2.7,0,3.7c1.3,1.3,2.1,2.9,2.1,4.8s-0.8,3.5-2.1,4.8 c-1.1,1.1-1.1,2.7,0,3.7c0.5,0.5,1.3,0.8,1.9,0.8c0.8,0,1.3-0.3,1.9-0.8c2.4-2.4,3.5-5.3,3.5-8.8C46.9,16.3,45.6,13.1,43.2,10.9z\"/>\n                                        <path d=\"M13.9,19.5c0-3.7,1.3-7.2,4-9.9c1.1-1.1,1.1-2.7,0-3.7c-1.1-1.1-2.7-1.1-3.7,0c-3.7,3.7-5.6,8.5-5.6,13.6s2.1,9.9,5.6,13.6 c0.5,0.5,1.1,0.8,1.9,0.8s1.3-0.3,1.9-0.8c1.1-1.1,1.1-2.7,0-3.7C15.5,26.7,13.9,23.2,13.9,19.5z\"/>\n                                        <path d=\"M49.9,5.9c-1.1-1.1-2.7-1.1-3.7,0s-1.1,2.7,0,3.7c2.7,2.7,4,6.1,4,9.9s-1.3,7.2-4,9.9c-1.1,1.1-1.1,2.7,0,3.7 c0.5,0.5,1.3,0.8,1.9,0.8c0.8,0,1.3-0.3,1.9-0.8c3.7-3.7,5.6-8.5,5.6-13.6C55.5,14.4,53.3,9.3,49.9,5.9z\"/>\n                                        <path d=\"M5.3,19.5c0-5.6,2.1-10.9,6.1-14.9c1.1-1.1,1.1-2.7,0-3.7s-2.7-1.1-3.7,0C2.7,5.9,0,12.3,0,19.5s2.7,13.6,7.7,18.7 C8.2,38.7,8.8,39,9.6,39c0.8,0,1.3-0.3,1.9-0.8c1.1-1.1,1.1-2.7,0-3.7C7.5,30.4,5.3,25.1,5.3,19.5z\"/>\n                                        <path d=\"M56.3,0.8c-1.1-1.1-2.7-1.1-3.7,0s-1.1,2.7,0,3.7c4,4,6.1,9.3,6.1,14.9s-2.1,10.9-6.1,14.9c-1.1,1.1-1.1,2.7,0,3.7 c0.5,0.5,1.3,0.8,1.9,0.8c0.8,0,1.3-0.3,1.9-0.8c5.1-5.1,7.7-11.7,7.7-18.7S61.3,5.9,56.3,0.8z\"/>\n                                    </g>\n                                </svg>\n                            </span>\n                            <span>{{ event.vue.location }}</span>\n                        </p>\n                        \n                        <!-- Summary -->\n                        <p class=\"eventbrite-item-description\" v-if=\"feed.display_description\">{{ event.vue.description }}</p>\n\n                        <!-- Tags -->\n                        <div class=\"eventbrite-item-tags\" v-if=\"( feed.display_price || feed.display_tickets ) && !feed.price_overlay\">\n\n                            <!-- Price -->\n                            <div class=\"eventbrite-item-price\" v-if=\"feed.display_price && event.vue.price\" v-html=\"event.vue.price\"></div>\n\n                            <!-- Tickets left -->\n                            <div class=\"eventbrite-item-available-tickets\" v-if=\"feed.display_tickets && event.tickets_remaining && ( event.tickets_remaining > 0 )\">{{ event.tickets_remaining }}{{ ticketsText( feed.tickets_text ) }}</div>\n                            \n                        </div>\n\n                        <!-- Buttons -->\n                        <div class=\"eventbrite-item-buttons\" v-if=\"feed.display_signup_button || feed.display_more_button\">\n                            \n                            <!-- Checkout -->\n                            <button :id=\"'eventbrite-checkout-button-' + event.ID\" class=\"eventbrite-item-checkout\" v-if=\"(event.event_sales_status.sales_status == 'on_sale') && feed.display_signup_button\" @click=\"checkoutFromEventCard(event, eventIndex)\">\n                                {{ checkoutButtonText( event ) }}\n                            </button>\n\n                            <!-- Read more -->\n                            <button class=\"eventbrite-item-details\" v-if=\"feed.display_more_button\" @click=\"getEventDetails(event, eventIndex)\">\n                                {{ feed.more_button_text }}\n                            </button>\n\n                        </div>\n\n                    </div>\n                    \n                </div>\n\n            </div>\n\n        </div>\n\n        <!-- No results -->\n        <div class=\"eventbrite-info-message\" v-if=\"(events) && (events.length === 0)\" v-show=\"feedLoaded\" v-cloak>{{ translations.no_events_text }}</div>\n\n        <!-- Error messages -->\n        <div class=\"eventbrite-info-message\" v-if=\"feedError == true\" v-cloak>{{ translations.loading_error_text }}</div>\n        <div class=\"eventbrite-info-message\" v-if=\"settings.api_key == false\" v-cloak>{{ translations.api_error_text }}</div>\n\n        <!-- Copyright -->\n        <div class=\"eventbrite-copyright eventbrite-copyright-left\" v-if=\"(events) && (events.length > 0) && (settings.show_copyright)\" v-show=\"feedLoaded\" v-cloak>\n            <div>{{ translations.copyright_text }}</div>\n            <a href=\"https://eventfeed.click/\" target=\"_blank\">\n                <span class=\"brand-text\"><strong>Event Feed</strong> for Eventbrite</span>\n            </a>\n        </div>\n        \n        <!-- Modal -->\n        <Modal v-model=\"modalShow\" :modal-class=\"'eventbrite-modal scrollable-modal eventbrite-modal-' + uid + ' eventbrite-modal-id-' + feed.ID\" bg-class=\"eventbrite-modal-bg\" wrapper-class=\"eventbrite-modal-wrapper\" @before-open=\"beforeOpen\" @before-close=\"beforeClose\" @after-close=\"afterClose\" @after-open=\"afterOpen\" append-to=\"#eventbrite-modal-container\" base-zindex=\"20000\" :live=\"false\" v-cloak>\n                \n            <div class=\"scroll-content\" v-scroll-lock=\"modalShow\">\n\n                <!-- Back button -->\n                <div class=\"eventbrite-modal-back\" v-show=\"modalAddBackBtn == true\" v-if=\"feed.popup\">\n                    <button type=\"button\" @click=\"backBtnClick\" :title=\"translations.back_button_text\">\n                        <i>\n                            <svg viewBox=\"0 0 24 24\">\n                                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M4 12l8 8 1.5-1.5L8 13h12v-2H8l5.5-5.5L12 4z\"></path>\n                            </svg>\n                        </i>\n                    </button>\n                </div>\n\n                <!-- Close modal button -->\n                <div class=\"eventbrite-modal-close\">\n                    <button type=\"button\" @click=\"(modalShow = false) && (requestModal = false)\" :title=\"translations.close_button_text\">\n                        <i>\n                            <svg viewBox=\"0 0 24 24\">\n                                <path d=\"M13.4 12l3.5-3.5-1.4-1.4-3.5 3.5-3.5-3.5-1.4 1.4 3.5 3.5-3.5 3.5 1.4 1.4 3.5-3.5 3.5 3.5 1.4-1.4z\"></path>\n                            </svg>\n                        </i>\n                    </button>\n                </div>\n\n                <!-- Image -->\n                <div id=\"eventbrite-modal-img\" class=\"eventbrite-modal-img\">\n                    <img v-show=\"modalImage !== false\" :src=\"modalImage\" :alt=\"modalEvent.post_title\" width=\"800\" height=\"400\" loading=\"lazy\">\n\n                    <!-- Add to calendar button -->\n                    <div class=\"eventbrite-modal-add-to-calendar-wrapper\" v-if=\"(feed.calendar_button == true) && (license == true && free != true)\">\n                        <button type=\"button\" class=\"eventbrite-modal-add-to-calendar\" @click=\"toggleModalCalendarAdd\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                            </svg>\n                            <span>{{ translations.add_to_calendar_text }}</span>\n                        </button>\n                        <div class=\"eventbrite-modal-add-to-calendar-menu\" v-show=\"modalCalendarAdd\">\n                            <button type=\"button\" class=\"eventbrite-modal-add-to-calendar-menu-close\" @click=\"modalCalendarAdd = false\">\n                                <svg version=\"1.1\" id=\"Layer_1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\"\n                                viewBox=\"0 0 64 64\" style=\"enable-background:new 0 0 64 64;\" xml:space=\"preserve\">\n                                    <path d=\"M35.4,32l19.9-19.9c1-1,1-2.4,0-3.4s-2.4-1-3.4,0L32,28.6L12,8.8c-0.9-1-2.4-1-3.3,0s-1,2.4,0,3.4L28.6,32L8.7,51.9\n                                        c-1,1-1,2.4,0,3.4c0.5,0.4,1,0.7,1.7,0.7s1.2-0.2,1.7-0.7l20-19.9l20,19.8c0.5,0.4,1.2,0.7,1.7,0.7c0.5,0,1.2-0.2,1.7-0.7\n                                        c1-1,1-2.4,0-3.4L35.4,32z\"/>\n                                </svg>\n                            </button>\n                            <a class=\"eventbrite-modal-add-to-calendar-menu-item eventbrite-modal-add-to-calendar-menu-item-google\" href=\"javascript:;\" target=\"_blank\" @click=\"modalCalendarAdd = false\">Google Calendar</a>\n                            <a class=\"eventbrite-modal-add-to-calendar-menu-item eventbrite-modal-add-to-calendar-menu-item-outlook\" href=\"javascript:;\" target=\"_blank\" @click=\"modalCalendarAdd = false\">Outlook Calendar</a>\n                            <a class=\"eventbrite-modal-add-to-calendar-menu-item eventbrite-modal-add-to-calendar-menu-item-office\" href=\"javascript:;\" target=\"_blank\" @click=\"modalCalendarAdd = false\">Office365 Calendar</a>\n                            <a class=\"eventbrite-modal-add-to-calendar-menu-item eventbrite-modal-add-to-calendar-menu-item-yahoo\" href=\"javascript:;\" target=\"_blank\" @click=\"modalCalendarAdd = false\">Yahoo Calendar</a>\n                            <a class=\"eventbrite-modal-add-to-calendar-menu-item eventbrite-modal-add-to-calendar-menu-item-apple\" href=\"javascript:;\" @click=\"modalCalendarAdd = false\">Download ICS file</a>\n                        </div>\n                    </div>\n                </div>\n\n                <div class=\"eventbrite-modal-title\" v-show=\"!modalTickets\">\n                    \n                    <!-- Date and time -->\n                    <div class=\"eventbrite-modal-datetime\" v-if=\"(modalEvent.vue) && (modalEvent.vue.start_full) && (modalEvent.vue.end_full)\">\n                        <time>\n                            {{ modalEvent.vue.start_full }} - {{ modalEvent.vue.end_full }}\n                        </time>\n                    </div>\n\n                    <!-- Title -->\n                    <h3 v-if=\"modalEvent.post_title\">{{ modalEvent.post_title }}</h3>\n\n                    <!-- Location -->\n                    <div class=\"eventbrite-modal-location\" :class=\"{'eventbrite-modal-location-clickable': (modalEvent.venue.address) && (feed.calendar_button == true)}\" v-if=\"(modalEvent.vue) && (modalEvent.vue.location)\" @click=\"modalScrollToMap(modalEvent.vue.location)\">\n                        <svg version=\"1.1\" class=\"eventbrite-modal-location-icon\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\" viewBox=\"0 0 48 64\" style=\"enable-background:new 0 0 48 64;\" xml:space=\"preserve\" v-if=\"modalEvent.vue.location !== 'Online'\">\n                            <g>\n                                <path d=\"M24,0C10.7,0,0,11.2,0,25.3c0,12,16.5,31.7,21.6,37.6c0.5,0.8,1.6,1.1,2.4,1.1c1.1,0,1.9-0.5,2.4-1.1 C31.5,57.1,48,37.1,48,25.3C48,11.2,37.3,0,24,0z M24,57.6C14.9,46.9,5.3,32.8,5.3,25.3c0-11.2,8.3-20,18.7-20s18.7,9.1,18.7,20 C42.7,32.8,33.1,46.9,24,57.6z\"/>\n                                <path d=\"M24,13.3c-5.9,0-10.7,4.8-10.7,10.7S18.1,34.7,24,34.7S34.7,29.9,34.7,24S29.9,13.3,24,13.3z M24,29.3 c-2.9,0-5.3-2.4-5.3-5.3s2.4-5.3,5.3-5.3s5.3,2.4,5.3,5.3S26.9,29.3,24,29.3z\"/>\n                            </g>\n                        </svg>\n                        <svg version=\"1.1\" class=\"eventbrite-modal-location-icon-online\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\" viewBox=\"0 0 64.1 48\" style=\"enable-background:new 0 0 64.1 48;\" xml:space=\"preserve\" v-if=\"modalEvent.vue.location == 'Online'\">\n                            <g>\n                                <path d=\"M32,13.9c-3.5,0-6.4,2.9-6.4,6.1c0,2.4,1.6,4.5,3.7,5.6v19.7c0,1.6,1.1,2.7,2.7,2.7s2.7-1.1,2.7-2.7V25.9 c2.1-1.1,3.7-3.2,3.7-5.6C38.4,16.8,35.5,13.9,32,13.9z M32,19.2c0.5,0,1.1,0.5,1.1,0.8s-0.5,0.8-1.1,0.8c-0.5,0-1.1-0.5-1.1-0.8 S31.5,19.2,32,19.2z\"/>\n                                <path d=\"M24.5,10.9c-1.1-1.1-2.7-1.1-3.7,0c-2.4,2.4-3.5,5.3-3.5,8.5s1.3,6.4,3.5,8.8c0.5,0.5,1.3,0.8,1.9,0.8 c0.8,0,1.3-0.3,1.9-0.8c1.1-1.1,1.1-2.7,0-3.7c-1.3-1.3-2.1-2.9-2.1-4.8s0.8-3.5,2.1-4.8C25.6,13.6,25.6,12,24.5,10.9z\"/>\n                                <path d=\"M43.2,10.9c-1.1-1.1-2.7-1.1-3.7,0c-1.1,1.1-1.1,2.7,0,3.7c1.3,1.3,2.1,2.9,2.1,4.8s-0.8,3.5-2.1,4.8 c-1.1,1.1-1.1,2.7,0,3.7c0.5,0.5,1.3,0.8,1.9,0.8c0.8,0,1.3-0.3,1.9-0.8c2.4-2.4,3.5-5.3,3.5-8.8C46.9,16.3,45.6,13.1,43.2,10.9z\"/>\n                                <path d=\"M13.9,19.5c0-3.7,1.3-7.2,4-9.9c1.1-1.1,1.1-2.7,0-3.7c-1.1-1.1-2.7-1.1-3.7,0c-3.7,3.7-5.6,8.5-5.6,13.6s2.1,9.9,5.6,13.6 c0.5,0.5,1.1,0.8,1.9,0.8s1.3-0.3,1.9-0.8c1.1-1.1,1.1-2.7,0-3.7C15.5,26.7,13.9,23.2,13.9,19.5z\"/>\n                                <path d=\"M49.9,5.9c-1.1-1.1-2.7-1.1-3.7,0s-1.1,2.7,0,3.7c2.7,2.7,4,6.1,4,9.9s-1.3,7.2-4,9.9c-1.1,1.1-1.1,2.7,0,3.7 c0.5,0.5,1.3,0.8,1.9,0.8c0.8,0,1.3-0.3,1.9-0.8c3.7-3.7,5.6-8.5,5.6-13.6C55.5,14.4,53.3,9.3,49.9,5.9z\"/>\n                                <path d=\"M5.3,19.5c0-5.6,2.1-10.9,6.1-14.9c1.1-1.1,1.1-2.7,0-3.7s-2.7-1.1-3.7,0C2.7,5.9,0,12.3,0,19.5s2.7,13.6,7.7,18.7 C8.2,38.7,8.8,39,9.6,39c0.8,0,1.3-0.3,1.9-0.8c1.1-1.1,1.1-2.7,0-3.7C7.5,30.4,5.3,25.1,5.3,19.5z\"/>\n                                <path d=\"M56.3,0.8c-1.1-1.1-2.7-1.1-3.7,0s-1.1,2.7,0,3.7c4,4,6.1,9.3,6.1,14.9s-2.1,10.9-6.1,14.9c-1.1,1.1-1.1,2.7,0,3.7 c0.5,0.5,1.3,0.8,1.9,0.8c0.8,0,1.3-0.3,1.9-0.8c5.1-5.1,7.7-11.7,7.7-18.7S61.3,5.9,56.3,0.8z\"/>\n                            </g>\n                        </svg>\n                        <span>{{ modalEvent.vue.location }}</span>\n                    </div>\n\n                </div>\n\n                <div class=\"eventbrite-modal-content\">\n                    \n                    <div class=\"replace-content\">\n\n                        <!-- Event information screen -->\n                        <div class=\"eventbrite-modal-event-info\" v-show=\"!modalTickets\">\n\n                            <!-- Summary -->\n                            <p class=\"eventbrite-modal-summary\" v-if=\"(modalEvent.vue) && (modalEvent.post_content)\">{{ modalEvent.post_content }}</p>\n\n                            <!-- Description modules -->\n                            <div v-for=\"description in modalDescription\" class=\"eventbrite-modal-description-module\" :class=\"{'eventbrite-modal-description-module-text': description.data.body, 'eventbrite-modal-description-module-img': description.data.image, 'eventbrite-modal-description-module-video': description.data.video}\" v-html=\"moduleContent( description )\" :key=\"description.ID\"></div>\n\n                            <div class=\"eventbrite-modal-description-module eventbrite-modal-description-module-text eventbrite-modal-description-error\" v-if=\"modalError == true\">\n                                <p>{{ translations.modal_error_text }} <br><a :href=\"modalEvent.url\" :target=\"(feed.link_target_blank == true) ? '_blank' : '_self'\">{{ translations.modal_error_link }}</a></p>\n                            </div>\n\n                        </div>\n\n                        <!-- Event checkout screen -->\n                        <div class=\"eventbrite-modal-event-tickets\" v-show=\"modalTickets\">\n\n                            <!-- Modal content spinner -->\n                            <div class=\"eventbrite-modal-content-spinner-wrapper\">\n                                <div class=\"eventbrite-modal-spinner\">\n                                    <div class=\"double-bounce1\"></div>\n                                    <div class=\"double-bounce2\"></div>\n                                </div>\n                            </div>\n\n                            <!-- Container for Eventbrite checkout data -->\n                            <div id=\"eventbrite-modal-event-tickets\" style=\"position: relative; background-color: #fff; z-index: 1;\"></div>\n\n                        </div>\n\n                    </div>\n\n                </div>\n\n                <!-- Map -->\n                <div id=\"eventbrite-modal-map\" class=\"eventbrite-modal-map\" v-show=\"!modalTickets\" v-if=\"(modalEvent.vue) && (modalEvent.venue.address) && (feed.google_map == true)\">\n                    <iframe width=\"100%\" height=\"250\" frameborder=\"0\" scrolling=\"no\" marginheight=\"0\" marginwidth=\"0\" :src=\"googleMapsSrc( this.modalEvent.vue.location )\" loading=\"lazy\"></iframe>\n                </div>\n\n                <!-- Organizer -->\n                <div class=\"eventbrite-modal-organizer\" v-show=\"!modalTickets\" v-if=\"(modalEvent.organizer) && (modalEvent.organizer.name) && (feed.organizer_info == true)\">\n                    <div class=\"eventbrite-modal-organizer-row\">\n                        <div class=\"eventbrite-modal-organizer-image\" v-if=\"(modalOrganizer) && (modalOrganizer.logo) && (modalOrganizer.logo.url)\">\n                            <img :src=\"modalOrganizer.logo.url\" alt=\"modalOrganizer.name\">\n                        </div>\n                        <div class=\"eventbrite-modal-organizer-info\">\n                            <div class=\"eventbrite-modal-organizer-name\" v-if=\"modalEvent.organizer.name\">\n                                <span>{{ modalEvent.organizer.name }}</span>\n                            </div>\n                            <div class=\"eventbrite-modal-organizer-title\">{{ translations.organizer_title }}</div>\n                            <div class=\"eventbrite-modal-organizer-description\" v-if=\"modalEvent.organizer.description\" v-html=\"modalEvent.organizer.description.html\"></div>\n                            <div class=\"eventbrite-modal-organizer-social\" v-if=\"modalOrganizer\">\n                                <a :href=\"'https://www.facebook.com/' + modalOrganizer.facebook\" v-if=\"modalOrganizer.facebook\" :target=\"(feed.link_target_blank == true) ? '_blank' : '_self'\" class=\"eventbrite-modal-organizer-social-twitter\" title=\"Facebook\">\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" aria-hidden=\"true\" focusable=\"false\" data-prefix=\"fab\" data-icon=\"facebook-f\" class=\"svg-inline--fa fa-facebook-f fa-w-10\" role=\"img\" viewBox=\"0 0 320 512\"><path fill=\"currentColor\" d=\"M279.14 288l14.22-92.66h-88.91v-60.13c0-25.35 12.42-50.06 52.24-50.06h40.42V6.26S260.43 0 225.36 0c-73.22 0-121.08 44.38-121.08 124.72v70.62H22.89V288h81.39v224h100.17V288z\"/></svg>\n                                </a>\n                                <a :href=\"'https://www.twitter.com/' + modalOrganizer.twitter\" v-if=\"modalOrganizer.twitter\" :target=\"(feed.link_target_blank == true) ? '_blank' : '_self'\" class=\"eventbrite-modal-organizer-social-website\" title=\"Twitter\">\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" aria-hidden=\"true\" focusable=\"false\" data-prefix=\"fab\" data-icon=\"twitter\" class=\"svg-inline--fa fa-twitter fa-w-16\" role=\"img\" viewBox=\"0 0 512 512\"><path fill=\"currentColor\" d=\"M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z\"/></svg>\n                                </a>\n                                <a :href=\"modalEvent.organizer.website\" v-if=\"modalEvent.organizer.website\" :target=\"(feed.link_target_blank == true) ? '_blank' : '_self'\" class=\"eventbrite-modal-organizer-social-facebook\" :title=\"translations.organizer_link\">\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" aria-hidden=\"true\" focusable=\"false\" data-prefix=\"far\" data-icon=\"globe\" class=\"svg-inline--fa fa-globe fa-w-16\" role=\"img\" viewBox=\"0 0 496 512\"><path fill=\"currentColor\" d=\"M248 8C111 8 0 119 0 256s111 248 248 248 248-111 248-248S385 8 248 8zm179.3 160h-67.2c-6.7-36.5-17.5-68.8-31.2-94.7 42.9 19 77.7 52.7 98.4 94.7zM248 56c18.6 0 48.6 41.2 63.2 112H184.8C199.4 97.2 229.4 56 248 56zM48 256c0-13.7 1.4-27.1 4-40h77.7c-1 13.1-1.7 26.3-1.7 40s.7 26.9 1.7 40H52c-2.6-12.9-4-26.3-4-40zm20.7 88h67.2c6.7 36.5 17.5 68.8 31.2 94.7-42.9-19-77.7-52.7-98.4-94.7zm67.2-176H68.7c20.7-42 55.5-75.7 98.4-94.7-13.7 25.9-24.5 58.2-31.2 94.7zM248 456c-18.6 0-48.6-41.2-63.2-112h126.5c-14.7 70.8-44.7 112-63.3 112zm70.1-160H177.9c-1.1-12.8-1.9-26-1.9-40s.8-27.2 1.9-40h140.3c1.1 12.8 1.9 26 1.9 40s-.9 27.2-2 40zm10.8 142.7c13.7-25.9 24.4-58.2 31.2-94.7h67.2c-20.7 42-55.5 75.7-98.4 94.7zM366.3 296c1-13.1 1.7-26.3 1.7-40s-.7-26.9-1.7-40H444c2.6 12.9 4 26.3 4 40s-1.4 27.1-4 40h-77.7z\"/></svg>\n                                </a>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n\n            </div>\n\n            <div class=\"noscroll-content\" v-show=\"!modalTickets\">\n                <div class=\"eventbrite-modal-footer-left\" v-if=\"feed.eventbrite_link == true\">\n                    <a class=\"eventbrite-modal-external-button\" :href=\"modalEvent.url\" :target=\"(feed.link_target_blank == true) ? '_blank' : '_self'\"><span class=\"desktop\">{{ translations.eventbrite_link }} </span><span class=\"mobile\">Eventbrite</span></a>\n                </div>\n                <div class=\"eventbrite-modal-footer-right\">\n                    <div class=\"eventbrite-modal-price\" v-if=\"(modalEvent.vue) && (modalEvent.vue.price) && feed.display_price\" v-html=\"modalEvent.vue.price\"></div>\n                    <button class=\"eventbrite-modal-checkout-button\" v-if=\"(modalEvent) && (modalEvent.event_sales_status.sales_status == 'on_sale')\" @click=\"toOrder(modalEvent, modalEventIndex)\">\n                        {{ checkoutButtonText( modalEvent ) }}\n                    </button>\n                </div>\n            </div>\n\n        </Modal>\n        \n        <!-- Modal spinner -->\n        <div :class=\"'eventbrite-modal-spinner-wrapper eventbrite-modal-spinner-wrapper-' + uid\">\n            <div class=\"eventbrite-modal-spinner\">\n                <div class=\"double-bounce1\"></div>\n                <div class=\"double-bounce2\"></div>\n            </div>\n        </div>\n\n    </div>\n\n</template>\n\t\n<script>\n\n     // Vue Modal\n    import VueModal from '@kouts/vue-modal';\n    \n    // Import V-scroll-lock\n    import VScrollLock from 'v-scroll-lock';\n\n    // Calendar links\n    import { google, outlook, office365, yahoo, ics } from 'calendar-link';\n\n    // Axios\n    import axios from 'axios';\n\n    var source;\n\n\texport default {\n        name: 'Widget',\n        props: ['initialUid', 'initialFeed', 'initialSettings', 'initialApi'],\n        data: function() {\n            return  {\n\n                // Get user data from WP admin\n                uid: this.initialUid,\n                feed: this.initialFeed,\n                settings: this.initialSettings,\n                api: this.initialApi,\n                admin: (EventFeedForEventbrite.admin === 'true'),\n                license: (EventFeedForEventbrite.premium === 'true'),\n                free: (EventFeedForEventbrite.free === 'true'),\n                translations: EventFeedForEventbriteAppTranslations,\n                edit_url: EventFeedForEventbrite.admin_url + 'post.php?post=' + this.initialFeed.ID + '&action=edit',\n\n                // Prepare variable for events\n                events: {},\n                \n                // If events data has been loaded from Eventbrite API\n                feedError: false,\n                feedLoaded: false,\n                \n                // Modal\n                modalEventIndex: false,\n                modalShow: false,\n                modalImage: false,\n                modalEvent: false,\n                modalTickets: false,\n                modalDescription: false,\n                modalOrganizer: false,\n                modalState: false,\n                modalCheckoutLoaded: false,\n                modalAddBackBtn: false,\n                modalError: false,\n                modalCalendarAdd: false,\n\n                // Scroll\n                windowTop: 0\n\n            }\n        },\n        components: {\n\t\t    'Modal': VueModal,\n            'VScrollLock': VScrollLock\n\t    },\n        methods: {\n\n            // Fires before modal is opened\n            beforeOpen: function() {\n\n                // Resets error variable\n                this.modalError = false;\n\n                // Sets data to waiting state and turn on modal\n                this.modalState = 'waitingForEventData';\n\n                if( source ) { source.cancel(); }\n                const CancelToken = axios.CancelToken;\n                source = CancelToken.source();\n                \n                if( ( this.feed.popup == false ) || ( this.license == false || this.free == true ) ) {\n\n                    // Loads HQ image\n                    if( this.modalEvent.vue.has_image == true ) {\n\n                        axios.get( this.api + 'event-feed-for-eventbrite/v1/image/' + this.modalEvent.logo.id, { cancelToken: source.token } )\n                        .then( response => {\n\n                            this.modalImage = response.data.url;\n                            this.$nextTick( function() {\n\n                                // Check if user didn't close the modal before loading\n                                if( this.modalShow == true ) {\n                                    this.modalState = 'EventDataReady';\t\n                                } else {\n                                    this.modalState = false;\n                                }\n                            })\n\n                        })\n                        .catch( errors => {\n                            \n                            // Request cancelled by user\n                            if ( axios.isCancel(errors) ) {\n                                this.manuallyCloseModal();\n\n                            // Error\n                            } else {\n                                this.modalError = true;\n                                this.modalImage = this.modalEvent.vue.image;\n\n                                this.$nextTick( function() {\n\n                                    // Check if user didn't close the modal before loading\n                                    if( this.modalShow == true ) {\n                                        this.modalState = 'EventDataReady';\t\n                                    } else {\n                                        this.modalState = false;\n                                    }\n                                    \n                                })\n                            }\n\n                        })\n\n                    } else {\n\n                        this.modalImage = this.modalEvent.vue.image;\n\n                        // Check if user didn't close the modal before loading\n                        if( this.modalShow == true ) {\n                            this.modalState = 'EventDataReady';\t\n                        } else {\n                            this.modalState = false;\n                        }\n\n                    }\n\n                } else {\n\n                    // Loads modal data\n                    if( this.modalEvent.vue.has_image == true ) {\n\n                        axios.get( this.api + 'event-feed-for-eventbrite/v1/details_image/' + this.modalEvent.ID + '/' + this.modalEvent.logo.id + '/' + this.modalEvent.organizer.id, { cancelToken: source.token } )\n                        .then( response => {\n                            this.modalImage = response.data.media;\n                            this.modalDescription = response.data.description.modules;\n                            this.modalOrganizer = response.data.organizer;\n                            this.$nextTick( function() {\n\n                                // Check if user didn't close the modal before loading\n                                if( this.modalShow == true ) {\n                                    this.modalState = 'EventDataReady';\t\n                                } else {\n                                    this.modalState = false;\n                                }\n\n                            })\n                        })\n\n                        // Catch errors\n                        .catch( errors => {\n\n                            // Request cancelled by user\n                            if ( axios.isCancel(errors) ) {\n                                this.manuallyCloseModal();\n\n                            // Error\n                            } else {\n                                this.modalError = true;\n                                this.modalImage = this.modalEvent.vue.image;\n                                this.modalDescription = false;\n                                this.modalOrganizer = false;\n\n                                this.$nextTick( function() {\n\n                                    // Check if user didn't close the modal before loading\n                                    if( this.modalShow == true ) {\n                                        this.modalState = 'EventDataReady';\t\n                                    } else {\n                                        this.modalState = false;\n                                    }\n                                    \n                                })\n                            }\n                        })\n\n                        // Generate calendar links\n                        if( this.feed.calendar_button == true && this.license == true && this.free != true ) {\n                            this.generateAddToCalendarLinks( this.modalEvent );\n                        }\n\n                    } else {\n\n                        axios.get( this.api + 'event-feed-for-eventbrite/v1/details/' + this.modalEvent.ID + '/' + this.modalEvent.organizer.id, { cancelToken: source.token } )\n                        .then( response => {\n                            this.modalImage = this.modalEvent.vue.image;\n                            this.modalDescription = response.data.description.modules;\n                            this.modalOrganizer = response.data.organizer;\n                            this.$nextTick( function() {\n\n                                // Check if user didn't close the modal before loading\n                                if( this.modalShow == true ) {\n                                    this.modalState = 'EventDataReady';\n                                } else {\n                                    this.modalState = false;\n                                }\n\n                            })\n                        })\n\n                        // Catch errors\n                        .catch( errors => {\n\n                            // Request cancelled by user\n                            if ( axios.isCancel(errors) ) {\n                                this.manuallyCloseModal();\n\n                            // Error\n                            } else {\n                                this.modalError = true;\n                                this.modalImage = this.modalEvent.vue.image;\n                                this.modalDescription = false;\n                                this.modalOrganizer = false;\n\n                                this.$nextTick( function() {\n\n                                    // Check if user didn't close the modal before loading\n                                    if( this.modalShow == true ) {\n                                        this.modalState = 'EventDataReady';\t\n                                    } else {\n                                        this.modalState = false;\n                                    }\n                                    \n                                })\n                            }\n                        })\n\n                        // Generate calendar links\n                        if( this.feed.calendar_button == true && this.license == true && this.free != true ) {\n                            this.generateAddToCalendarLinks( this.modalEvent );\n                        }\n\n                    }\n\n                }\n\n                // Load Eventbrite checkout widget\n                if( this.modalTickets == true ) {\n                    this.loadEventbriteCheckout();\n                }\n\n            },\n\n            // Fires after modal is opened\n            afterOpen: function() {\n\n                // Add modal and event ID to URL\n                if( this.feed.ID && this.modalEvent && ( this.license == true && this.free != true ) ) {\n                    const url = new URL(window.location.href);\n                    url.searchParams.set('effe', this.feed.ID);\n                    url.searchParams.set('effe-id', this.modalEvent.ID);\n                    window.history.pushState({}, '', url.toString());\n                }\n\n            },\n\n            // Fires before modal is closed\n            beforeClose: function() {\n                this.requestModal = false;\n\n            },\n\n            // Fires after modal is closed\n            afterClose: function() {\n                this.modalCleanUp();\n                this.modalCalendarAdd = false;\n\n                // Remove modal and event ID from URL\n                const url = new URL(window.location.href);\n                url.searchParams.delete('effe');\n                url.searchParams.delete('effe-id');\n                window.history.replaceState({}, '', url.toString());\n            },\n            \n            // Runs after user advances to ticket order\n            toOrder: function(event, eventIndex) {\n                \n                // Popup on website (and is not IE)\n                if( this.feed.link_to == 'popup' ) {\n\n                    this.toggleModalContent();\n                    this.modalAddBackBtn = true;\n\n                // Popup on Eventbrite\n                } else {\n\n                    if( this.feed.link_target_blank == true ) {\n                        window.open( event.url + '#tickets' );\n                    } else {\n                        window.location.href = event.url + '#tickets';\n                    }\n\n                }\n\n            },\n\n            // Check clicks outside the modal before modal is fully loaded (will cancel opening of the modal)\n            manuallyCloseModal: function( event ) {\n\n                this.modalShow = false;\n                this.requestModal = false;\n                this.modalAddBackBtn = true;\n                this.beforeClose();\n                this.afterClose();\n                \n                var spinnerEl = document.querySelector('.eventbrite-modal-spinner-wrapper-' + this.uid);\n                spinnerEl.style.display = 'none';\n\n                source.cancel();\n\n            },\n\n            // When clicked on a event card link\n            getEventDetails: function(event, eventIndex) {\n                \n                // If popup is set, open popup\n                if( ( this.feed.popup == true ) && ( this.license == true && this.free != true ) ) {\n                    this.modalShow = true;\n                    this.modalEvent = event;\n                    this.modalEventIndex = eventIndex;\n                \n                // Else go to event URL\n                } else {\n\n                    if( this.feed.link_target_blank == true ) {\n                        window.open( event.url );\n                    } else {\n                        window.location.href = event.url;\n                    }\n                    \n                }\n                \n            },\n\n            // When clicked on checkout button from event card\n            checkoutFromEventCard: function(event, eventIndex) {\n\n                // Popup on website (and is not IE)\n                if( this.feed.link_to == 'popup' ) {\n                    this.modalShow = true;\n                    this.modalEvent = event;\n                    this.modalEventIndex = eventIndex;\n                    this.toggleModalContent();\n\n                // Popup on Eventbrite\n                } else {\n                    if( this.feed.link_target_blank == true ) {\n                        window.open( event.url + '#tickets' );\n                    } else {\n                        window.location.href = event.url + '#tickets';\n                    }\n                }\n            },\n\n            // Toggles modal content after\n            toggleModalContent: function() {\n                this.modalTickets = !this.modalTickets;\n                if( ( this.modalTickets == true ) && ( this.modalCheckoutLoaded == false ) ) {\n                    this.modalState = 'waitingForCheckoutData';\n                    this.loadEventbriteCheckout();\n                }\n            },\n\n            // Set focus to modal element\n            setFocusModal: function() {\n                document.querySelector('.eventbrite-modal-wrapper').focus();\n            },\n            \n            // Modal back button behavior\n            backBtnClick: function() {\n                this.toggleModalContent();\n                this.$nextTick(this.modalScrollToTop);\n                this.$nextTick(this.modalAddBackBtn = false);\n            },\n\n            // Clean-up after closing modal\n            modalCleanUp: function() {\n                this.modalShow = false;\n                this.modalTickets = false;\n                this.modalImage = false;\n                this.modalDescription = false;\n                this.modalState = false;\n                this.modalCheckoutLoaded = false;\n                this.modalAddBackBtn = false;\n                this.modalCalendarAdd = false;\n\n            },\n\n            // Load Eventbrite checkout\n            loadEventbriteCheckout: function() {\n                let widgetAttributes = {\n                    widgetType: \"checkout\",\n                    eventId: this.modalEvent.ID,\n                    iframeContainerId: \"eventbrite-modal-event-tickets\",\n                };\n                window.EBWidgets.createWidget(widgetAttributes);\n            },\n\n            // Add parameters to YouTube embed URL\n            youtubeUrl: function( url ) {\n                var videoUrl = new URL( url );\n                videoUrl.searchParams.append( 'enablejsapi', '1' );\n                videoUrl.searchParams.append( 'modestbranding', '1' );\n                videoUrl.searchParams.append( 'rel', '0' );\n                videoUrl.searchParams.append( 'showinfo', '0' );\n                videoUrl.searchParams.append( 'showtitle', '0' );\n                return videoUrl.toString();\n            },\n\n            // Add parameters to Vimeo embed URL\n            vimeoUrl: function( url ) {\n                var videoUrl = new URL( url );\n                videoUrl.searchParams.append( 'title', '0' );\n                videoUrl.searchParams.append( 'byline', '0' );\n                videoUrl.searchParams.append( 'portrait', '0' );\n                return videoUrl.toString();\n            },\n\n            // Get Google maps embed source URL\n            googleMapsSrc: function( address ) {\n                var url = 'https://maps.google.com/?q=' + address + '&output=embed';\n                return url;\n            },\n\n            // Description modules\n            moduleContent: function( description ) {\n\n                // Text module\n                if( description.data.body ) {\n                    return description.data.body.text;\n\n                // Image module\n                } else if( description.data.image ) {\n                    return '<img src=\"' + description.data.image.url + '\" alt=\"\" loading=\"lazy\">';\n\n                // Video module\n                } else if( description.data.video ) {\n\n                    // YouTube\n                    if( description.data.video.embed_url.includes( 'youtube' ) ) {\n                        return '<div class=\"iframe-container\"><iframe width=\"640\" height=\"360\" src=\"' + this.youtubeUrl( description.data.video.embed_url ) + '\" frameborder=\"0\" loading=\"lazy\"></iframe></div>';\n                    \n                    // Vimeo\n                    } else if( description.data.video.embed_url.includes( 'vimeo' ) ) {\n                        return '<div class=\"iframe-container\"><iframe width=\"640\" height=\"360\" src=\"' + this.vimeoUrl( description.data.video.embed_url ) + '\" frameborder=\"0\" allow=\"autoplay; fullscreen; picture-in-picture\" loading=\"lazy\"></iframe></div>';\n                    }\n                }\n\n            },\n\n            // Scroll to modal map\n            modalScrollToMap: function( address ) {\n                var map = document.querySelector( '#eventbrite-modal-map' );\n                if ( typeof(map) != 'undefined' && map != null ) {\n                    map.scrollIntoView( { behavior: 'smooth' } );\n                } else if( address !== 'Online' ) {\n                    var googleMapUrl = 'https://www.google.com/maps/place/' + address;\n                    if( this.feed.link_target_blank == true ) {\n                        window.open( googleMapUrl );\n                    } else {\n                        window.location = googleMapUrl;\n                    }\n                }\n            },\n\n            // Scroll to top of the modal info screen\n            modalScrollToTop: function() {\n                var image = document.querySelector( '#eventbrite-modal-img' );\n                if ( typeof(image) != 'undefined' && image != null ) {\n                    image.scrollIntoView( { behavior: 'auto' } );\n                }\n            },\n            \n            // Toggle modal add to calendar menu\n            toggleModalCalendarAdd: function() {\n                this.modalCalendarAdd = !this.modalCalendarAdd;\n                if( this.modalCalendarAdd == true ) {\n                    window.addEventListener( 'click', this.autoCloseCalendarMenu );\n                } else {\n                    window.removeEventListener( 'click', this.autoCloseCalendarMenu );\n                }\n            },\n\n            // Automatically close add to calendar menu on click outside the wrapper\n            autoCloseCalendarMenu: function( event ) {\n                if( ! event.target.closest('.eventbrite-modal-add-to-calendar-wrapper') ) {\n                    this.modalCalendarAdd = false;\n\t\t\t\t}\n            },\n\n            // Generate add to calendar links\n            generateAddToCalendarLinks: function( event ) {\n                const AddToCalendarEvent = {\n                    title: event.post_title,\n                    description: event.post_content,\n                    start: event.start.utc,\n                    end: event.end.utc,\n                    location: event.vue.location\n                };\n                document.querySelector( '.eventbrite-modal-add-to-calendar-menu-item-google' ).setAttribute( 'href', google( AddToCalendarEvent ) ) ;\n                document.querySelector( '.eventbrite-modal-add-to-calendar-menu-item-outlook' ).setAttribute( 'href', outlook( AddToCalendarEvent ) ) ;\n                document.querySelector( '.eventbrite-modal-add-to-calendar-menu-item-office' ).setAttribute( 'href', office365( AddToCalendarEvent ) ) ;\n                document.querySelector( '.eventbrite-modal-add-to-calendar-menu-item-yahoo' ).setAttribute( 'href', yahoo( AddToCalendarEvent ) ) ;\n                document.querySelector( '.eventbrite-modal-add-to-calendar-menu-item-apple' ).setAttribute( 'href', ics( AddToCalendarEvent ) ) ;\n            },\n\n            // Check top scroll position (for WP admin bar modal shift)\n            onScroll: function( event ) {\n                this.windowTop = window.top.scrollY;\n                this.checkScrollPosition();\n            },\n            checkScrollPosition: function() {\n                if( this.windowTop == 0 ) {\n                    document.querySelector( 'body' ).classList.add('eventbrite-scroll-top');\n                } else {\n                    document.querySelector( 'body' ).classList.remove('eventbrite-scroll-top');\n                }\n            },\n\n            // Remaining tickets text\n            ticketsText: function( text ) {\n                var textNew = '';\n                if( text !== '' ) {\n                    var textNew = ' ' + text;\n                }\n                return textNew;\n            },\n\n            // Checkout button text\n            checkoutButtonText: function( event ) {\n                var buttonText;\n                if( event.is_free == true ) {\n                    buttonText = this.feed.signup_button_text;\n                } else {\n                    buttonText = this.feed.tickets_button_text;\n                }\n                return buttonText;\n            }\n                        \n        },\n        watch: {\n\n            // Handling modal states\n            modalState: {\n                handler: function(val, oldVal) {\n                    \n                    var modalEl = document.querySelector('#eventbrite-modal-container'); \n                    var contentEl = document.querySelector('.eventbrite-modal-wrapper');\n                    var spinnerEl = document.querySelector('.eventbrite-modal-spinner-wrapper-' + this.uid);\n\n                    // Modal is opened but waiting for data\n                    if( val == 'waitingForEventData' ) {\n                        if( contentEl ) { contentEl.style.display = 'none'; }\n                        if( spinnerEl ) { spinnerEl.style.display = 'flex'; } \n                    \n                    // Data ready, can show the modal window\n                    } else if( val == 'EventDataReady' ) {\n                        if( contentEl ) { contentEl.style.display = 'flex'; }\n                        if( spinnerEl ) { spinnerEl.style.display = 'none'; }\n                        this.setFocusModal();\n                    \n                    // Hide spinner\n                    } else if( val == true ) {\n                        if( contentEl ) { contentEl.style.display = 'none'; }\n                        if( spinnerEl ) { spinnerEl.style.display = 'none'; }\n                    }\n                    \n                    // Order screen\n                    if( val == 'orderScreen' ) {\n                        if( contentEl ) { modalEl.classList.add('modal-order'); }\n                    } else {\n                        if( contentEl ) { modalEl.classList.remove('modal-order'); }\n                    }\n\n                }\n            },\n\n            // Class toggling\n            modalTickets: {\n                handler: function(val, oldVal) {\n                    \n                    var modalEl = document.querySelector('#eventbrite-modal-container'); \n\n                    if( val == true ) {\n                        modalEl.classList.add('modal-tickets');\n                    } else {\n                        modalEl.classList.remove('modal-tickets');\n                    }\n\n                }\n            }\n\n        },\n        mounted() {\n\n            // Get main Eventbrite data\n            axios.get( this.api + 'event-feed-for-eventbrite/v1/feed/' + this.feed.ID )\n            .then( response => {\n                this.events = response.data.events;\n                this.feedLoaded = true;\n                this.checkScrollPosition();\n\n                // Check if modal is opened from URL\n                const url = new URL(window.location.href);\n                if( url.searchParams.has('effe') && url.searchParams.has('effe-id') ) {\n                    var modalId = url.searchParams.get('effe');\n                    if( modalId == this.feed.ID ) {\n                        var modalEventId = url.searchParams.get('effe-id');\n                        var modalEventIndex = Array.isArray(this.events) ? this.events.findIndex(event => event.ID == modalEventId) : -1;\n                        if( ( this.feed.popup == true ) && ( this.license == true && this.free != true ) && ( modalEventIndex !== -1 ) ) {\n                            this.modalShow = true;\n                            this.modalEvent = this.events[modalEventIndex];\n                            this.modalEventIndex = modalEventIndex;\n                        } else {\n                            // Remove modal and event ID from URL\n                            url.searchParams.delete('effe');\n                            url.searchParams.delete('effe-id');\n                            window.history.replaceState({}, '', url.toString());\n                        }\n                    }\n                }\n            })\n            .catch( errors => {\n                this.feedError = true;\n            })\n\n            this.$nextTick(function () {\n\n                // Listen to Eventbrite widget events\n                window.addEventListener('message', (event) => {\n\n                    // Remove widget transitions and set height to 100% on any event\n                    if ( event.origin == \"https://www.eventbrite.com\") {\n\n                        var tickets = document.querySelector('#eventbrite-modal-event-tickets');\n\n                        tickets.style.height = '100%';\n                        tickets.style.transition = 'none';\n                    }\n\n                    // Widget rendered event\n                    if( ( event.origin == \"https://www.eventbrite.com\" ) && ( event.data.messageName == ( 'widgetRenderComplete' ) ) ) {\n                        \n                        // Bug fix - rule will apply only for current modal\n                        if( this.modalShow == true) {\n                            this.modalCheckoutLoaded = true;\n                        }\n                    }\n                    \n                    // Tickets screen (if coming from event details and back from ticket order)\n                    if ( ( event.origin == \"https://www.eventbrite.com\" ) && ( ( event.data.messageName == 'widgetRenderComplete' ) || ( event.data.messageName == 'backToTicketSelection' ) ) ) {\n\n                        // Bug fix - rule will apply only for current modal\n                        if( this.modalShow == true) {\n\n                            // Count with the Eventbrite delay\n                            if( event.data.messageName == 'backToTicketSelection' ) {\n\n                                setTimeout(() => this.modalState = 'ticketsScreen', 810);\n\n                                // Add back button\n                                if( ( this.feed.popup == true ) && ( this.license == true && this.free != true ) ) {\n                                    setTimeout(() => this.modalAddBackBtn = true, 810);\n                                }\n\n                            } else {\n\n                                this.modalState = 'ticketsScreen';\n\n                                // Add back button\n                                if( ( this.feed.popup == true ) && ( this.license == true && this.free != true ) ) {\n                                    this.modalAddBackBtn = true;\n                                }\n\n                            }\n\n                        }\n\n                    }\n\n                    // Order start event - contact information and payment\n                    else if( ( event.origin == \"https://www.eventbrite.com\" ) && (event.data.messageName == 'orderStart' ) ) {\n                        \n                        setTimeout(() => this.modalState = 'orderScreen', 810);\n\n                        // Remove back button\n                        setTimeout(() => this.modalAddBackBtn = false, 810);\n\n                    }\n\n                });\n\n                // Add scroll event listener\n                window.addEventListener( 'scroll', this.onScroll );\n\n                // Add click event listener to spinner (when loading the feed)\n                var spinner =  this.$el.querySelector('.eventbrite-modal-spinner-wrapper');\n                spinner.addEventListener( 'click', this.manuallyCloseModal );\n\n            });\n\n        },\n        beforeDestroy() {\n\n            // Remove scroll event listener\n            window.removeEventListener( 'scroll', this.onScroll );\n\n            // Remove click event listener\n            window.removeEventListener( 'click', this.manuallyCloseModal );\n\n        },\n\t}\n\n</script>\n", "import mod from \"-!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Widget.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Widget.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Widget.vue?vue&type=template&id=e1e8796c&\"\nimport script from \"./Widget.vue?vue&type=script&lang=js&\"\nexport * from \"./Widget.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"/Users/<USER>/Sites/events/wp-content/plugins/event-feed-for-eventbrite/node_modules/vue-hot-reload-api/dist/index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('e1e8796c')) {\n      api.createRecord('e1e8796c', component.options)\n    } else {\n      api.reload('e1e8796c', component.options)\n    }\n    module.hot.accept(\"./Widget.vue?vue&type=template&id=e1e8796c&\", function () {\n      api.rerender('e1e8796c', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"public/src/js/components/Widget.vue\"\nexport default component.exports"], "names": ["render", "_vm", "this", "_h", "$createElement", "_c", "_self", "directives", "name", "rawName", "value", "settings", "api_key", "feedError", "feedLoaded", "expression", "class", "uid", "_m", "_v", "staticClass", "_s", "translations", "loading_text", "events", "length", "feed", "ID", "css_classes", "attrs", "id", "css_id", "admin", "edit_url", "target", "href", "staticStyle", "xmlns", "fill", "width", "height", "version", "x", "y", "viewBox", "d", "edit_link_text", "_e", "_l", "event", "eventIndex", "key", "includes", "event_sales_status", "sales_status", "display_image", "url", "on", "click", "$event", "getEventDetails", "vue", "image", "src", "alt", "post_title", "loading", "display_price", "display_tickets", "price_overlay", "price", "domProps", "innerHTML", "tickets_remaining", "ticketsText", "tickets_text", "display_datetime", "start", "end", "title", "display_location", "display_description", "display_signup_button", "display_more_button", "location", "description", "checkoutFromEventCard", "checkoutButtonText", "more_button_text", "no_events_text", "loading_error_text", "api_error_text", "show_copyright", "copyright_text", "live", "beforeOpen", "beforeClose", "afterClose", "afterOpen", "model", "modalShow", "callback", "$$v", "popup", "modalAddBackBtn", "type", "back_button_text", "backBtnClick", "close_button_text", "requestModal", "modalImage", "modalEvent", "calendar_button", "license", "free", "toggleModalCalendarAdd", "stroke", "add_to_calendar_text", "modalCalendarAdd", "modalTickets", "start_full", "end_full", "venue", "address", "modalScrollToMap", "post_content", "modalDescription", "data", "body", "video", "moduleContent", "modalError", "modal_error_text", "link_target_blank", "modal_error_link", "position", "google_map", "frameborder", "scrolling", "marginheight", "marginwidth", "googleMapsSrc", "organizer", "organizer_info", "modalOrganizer", "logo", "organizer_title", "html", "facebook", "focusable", "role", "twitter", "website", "organizer_link", "eventbrite_link", "toOrder", "modalEventIndex", "_withStripped", "component", "options", "__file"], "sourceRoot": ""}