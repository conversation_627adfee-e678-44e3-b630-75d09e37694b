{"version": 3, "file": "preview.js", "mappings": "CAAA,SAAYA,GACX,aAGOA,EAAE,sCAAsCC,OAAS,GACjDD,EAAE,sCAAsCE,MAAM,SAASC,GAGnDH,EAAEI,MAAMC,YAAY,CAChBC,KAAM,SACNC,MAAO,yBACPC,QAAS,SACTC,YAAa,CACTC,YAAY,GAEhBC,aAAc,CACVC,YAAY,GAEhBC,QAASC,0CAA0CC,UACnDC,MAAO,IAGX,IAAIC,YAAYb,MAGhBJ,EAAEI,MAAMc,GAAG,SAAS,WAChBlB,EAAEI,MAAMC,YAAY,UAAWS,0CAA0CK,gBAI7EnB,EAAEI,MAAMc,GAAG,cAAc,WACrBlB,EAAEI,MAAMC,YAAY,UAAWS,0CAA0CC,iBA/BzF,CAqCIK", "sources": ["webpack://event-feed-for-eventbrite/./public/src/js/preview.js"], "sourcesContent": ["( function( $ ) {\n\t'use strict';\n\n    // Copy shortcode functionality\n    if( $('.event-feed-for-eventbrite-tooltip').length > 0 ) {\n        $('.event-feed-for-eventbrite-tooltip').each( function(index) {\n\n            // Copy shortcode tooltip\n            $(this).tooltipster({\n                side: 'bottom',\n                theme: 'tooltipster-borderless',\n                trigger: 'custom',\n                triggerOpen: {\n                    mouseenter: true\n                },\n                triggerClose: {\n                    mouseleave: true\n                },\n                content: EventFeedForEventbritePreviewTranslations.copy_text,\n                delay: 0,\n            });\n\n            new ClipboardJS(this);\n\n            // On click destroy original tooltip and create new with 'Copied' text -> open it\n            $(this).on('click', function() {\n                $(this).tooltipster('content', EventFeedForEventbritePreviewTranslations.copied_text);\n            });\n\n            // On mouse leave recreate original tooltip\n            $(this).on('mouseleave', function() {\n                $(this).tooltipster('content', EventFeedForEventbritePreviewTranslations.copy_text);\n            });\n        \n        });\n    }\n\n})( jQuery );"], "names": ["$", "length", "each", "index", "this", "tooltipster", "side", "theme", "trigger", "triggerOpen", "mouseenter", "triggerClose", "mouseleave", "content", "EventFeedForEventbritePreviewTranslations", "copy_text", "delay", "ClipboardJS", "on", "copied_text", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}