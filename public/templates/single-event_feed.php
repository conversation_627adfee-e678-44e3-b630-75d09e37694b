<?php

/**
 * Single Event Feed template. (useful for previewing from administration)
 *
 * @since      1.0.0
 * @package    Event_Feed_For_Eventbrite
 * @subpackage Event_Feed_For_Eventbrite/public/partials/layouts
 * <AUTHOR> Plugins <https://www.bohemiaplugins.com>
 */

// If not admin user
if( ! current_user_can( 'manage_options' ) ) {
    wp_redirect( home_url() );
}

get_header();
?>

<div class="event-feed-for-eventbrite-preview-wrapper">
    <div class="event-feed-for-eventbrite-preview-info">
        <h1><?php echo esc_html( $post->post_title ); ?></h1>
        <p><?php esc_html_e( 'This is a non-public preview. To embed this event feed into your post/page, please copy and paste this shortcode where you want the feed to appear:', 'event-feed-for-eventbrite' ) ?></p>
        <p class="event-feed-for-eventbrite-preview-shortcode">
            <span class="event-feed-for-eventbrite-shortcode-field">
                <input id="event-feed-for-eventbrite-shortcode" type="text" value="<?php echo esc_attr( '[event-feed id=' . $post->ID . ']' ); ?>" readonly="">
                <span class="event-feed-for-eventbrite-tooltip tooltipstered" data-clipboard-target="#event-feed-for-eventbrite-shortcode">
                    <svg aria-hidden="true" role="img" class="StyledOcticon-sc-7ly0uy-0 hOvdVr" viewBox="0 0 16 16" width="16" height="16" fill="currentColor" style="display: inline-block; user-select: none; vertical-align: text-bottom;">
                        <path fill-rule="evenodd" d="M5.75 1a.75.75 0 00-.75.75v3c0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75v-3a.75.75 0 00-.75-.75h-4.5zm.75 3V2.5h3V4h-3zm-2.874-.467a.75.75 0 00-.752-1.298A1.75 1.75 0 002 3.75v9.5c0 .966.784 1.75 1.75 1.75h8.5A1.75 1.75 0 0014 13.25v-9.5a1.75 1.75 0 00-.874-1.515.75.75 0 10-.752 1.298.25.25 0 01.126.217v9.5a.25.25 0 01-.25.25h-8.5a.25.25 0 01-.25-.25v-9.5a.25.25 0 01.126-.217z"></path>
                    </svg>
                </span>
            </span>
        </p>
    </div>
    <?php echo do_shortcode('[event-feed id=' . esc_attr( $post->ID ) . ']') ?>
</div>

<?php
get_footer();
?>