/**
 * Variables
 */
body {

    // Preview colors
    --event-feed-for-eventbrite-preview-title-color:        #39364f;
    --event-feed-for-eventbrite-preview-text-color:         #39364f;
    --event-feed-for-eventbrite-preview-input-text-color:   #39364f;
    
}

.event-feed-for-eventbrite-app,
.eventbrite-modal,
.eventbrite-modal-error,
.eventbrite-modal-spinner-wrapper {

    // Accent color
    --event-feed-for-eventbrite-theme-color:                #334ECD;
    --event-feed-for-eventbrite-theme-color-h:              229;
    --event-feed-for-eventbrite-theme-color-s:              61;
    --event-feed-for-eventbrite-theme-color-l:              50;
    --event-feed-for-eventbrite-theme-color-l:              46;

    // Card colors
    --event-feed-for-eventbrite-card-bg:                    #fff;
    --event-feed-for-eventbrite-title-color:                #39364f;
    --event-feed-for-eventbrite-datetime-color:             #6F7287;
    --event-feed-for-eventbrite-full-date-color:            #6F7287;
    --event-feed-for-eventbrite-desc-color:                 #6F7287;
    --event-feed-for-eventbrite-location-icon-color:        #6F7287;
    --event-feed-for-eventbrite-location-color:             #6F7287;
    --event-feed-for-eventbrite-sign-up-button-color:       #fff;
    --event-feed-for-eventbrite-details-button-bg:          #EAEEFC;
    --event-feed-for-eventbrite-price-bg:                   #fff;
    --event-feed-for-eventbrite-price-color:                #39364f;
    --event-feed-for-eventbrite-separator-color:            #eaeefc;

    // Modal
    --event-feed-for-eventbrite-modal-text-color:           #6F7287;

    // Grid (Simple)
    --event-feed-for-eventbrite-rows-desktop:               4;
    --event-feed-for-eventbrite-rows-large-tablets:         3;
    --event-feed-for-eventbrite-rows-small-tablets:         2;
    --event-feed-for-eventbrite-rows-mobile:                1;

    line-height: 1.5;

}

/**
 * Reset styles
 */
.event-feed-for-eventbrite-app,
.eventbrite-modal {

    div, span, iframe, p, blockquote, a, button, em, img, small, strong, b, u, i, ol, ul, li, form, label, table, embed, figure, figcaption {
	    padding: 0;
        margin: 0;
    }

    button {
        cursor: pointer;
        border: none;
        transform: none;
        min-height: 0;
        &::before, &::after {
            content: none;
        }
        &:hover {
            transform: none;
            &::before, &::after {
                content: none;
            }
        }
    }

}

body {
    
    /**
    * Modal spinner
    */
    .eventbrite-modal-content-spinner-wrapper {
        position: absolute;
        top: 50px;
        width: 100%;
        display: block;
        text-align: center;
    }
    .eventbrite-modal-spinner-wrapper {
        display: none; // -> flex
        z-index: 20001;
        align-items: center;
        bottom: 0;
        left: 0;
        position: fixed;
        right: 0;
        top: 0;
        .eventbrite-modal-spinner {
            .double-bounce1, .double-bounce2 {
                opacity: .75;
            }
        }
    }
    .eventbrite-modal-spinner {
        position: relative;
        width: 40px;
        height: 40px;
        margin: 0 auto;
        .double-bounce1, .double-bounce2 {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background-color: var(--event-feed-for-eventbrite-theme-color);
            opacity: .5;
            position: absolute;
            top: 0;
            left: 0;
            -webkit-animation: sk-bounce 2.0s infinite ease-in-out;
            animation: sk-bounce 2.0s infinite ease-in-out;
        }
        .double-bounce2 {
            -webkit-animation-delay: -1.0s;
            animation-delay: -1.0s;
        }
    }

    /**
    * Feed loading spinner
    */
    .eventbrite-modal-spinner-feed {
        position: relative;
        top: auto;
        right: auto;
        bottom: auto;
        left: auto;
        display: flex;
        justify-content: center;
        align-items: center;
        .eventbrite-modal-spinner {
            margin: 0;
            width: 30px;
            height: 30px;
        }
        .eventbrite-modal-spinner-text {
            p {
                color: var(--event-feed-for-eventbrite-title-color);
                margin-left: 10px;
                margin-bottom: 0;
                font-size: 15px;
            }
        }
    }

    /**
    * Responsive iframes
    */
    .iframe-container { 
        margin: 0;
        overflow: hidden;
        padding-top: 56.25%;
        position: relative;
        line-height: 0;
    }
    .iframe-container iframe {
        display: block;
        line-height: 0;
        border: 0;
        height: 100%;
        left: 0;
        position: absolute;
        top: 0;
        width: 100%;
    }

    .event-feed-for-eventbrite-shortcode-field {
        width: 100%;
        max-width: 300px;
        display: inline-block;
        position: relative;
        input[type=text] {
            text-align: left;
            font-size: 16px;
            box-shadow: 0 0 0 transparent;
            border-radius: 4px;
            border: 1px solid #8c8f94;
            padding: 0 12px;
            line-height: 2;
            width: 100%;
            min-height: 40px;
            max-width: 100%;
            margin: 0;
            &[readonly] {
                background: #fff;
            }
            &:focus {
                border-color: #2271b1;
                box-shadow: 0 0 0 1px #2271b1;
                outline: 2px solid transparent;
                color: inherit;
            }
        }   
        span {
            cursor: pointer;
            top: 1px;
            right: 1px;
            bottom: 1px;
            width: 35px;
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
            position: absolute;
            display: flex!important;
            justify-content: center;
            align-items: center;
            background-color: #f6f7f7;
            &:hover,
            &:focus {
                outline: none;
                box-shadow: none;
                background-color: #f6f7f7;
            }
            svg {
                fill: #333;
            }
        }
    }
    .tooltipster-sidetip.tooltipster-borderless {
        font-size: 14px!important;
    }

}

/**
 * Admin utilities
 */
.event-feed-for-eventbrite-app {

    &:hover {
        .eventbrite-feed-edit-link {
            opacity: 1;
            visibility: visible;
        }
    }

    // edit feed link
    .eventbrite-feed-edit-link {
        opacity: 0;
        visibility: hidden;
        position: absolute;
        z-index: 5;
        top: 10px;
        right: 10px;
        display: flex;
        align-items: center;
        background-color: #fff;
        border-radius: 5px;
        padding: 5px 10px;
        box-shadow: rgba(50, 50, 93, 25%) 0px 2px 5px -1px, rgba(0, 0, 0, 30%) 0px 1px 3px -1px;
        color: var(--event-feed-for-eventbrite-title-color);
        transition: all .2s cubic-bezier( .4, 0, .2, 1);
        text-decoration: none!important;
        &:hover {
            text-decoration: none;
            color: var(--event-feed-for-eventbrite-theme-color);
            svg {
                fill: var(--event-feed-for-eventbrite-theme-color);
            }
            span {
                text-decoration: none;
            }
        }
        svg {
            position: relative;
            top: -1px;
            margin-right: 7px;
            fill: var(--event-feed-for-eventbrite-title-color);
            transition: all .2s cubic-bezier( .4, 0, .2, 1);
        }
        span {
            font-size: 12px;
            font-weight: 600;
            text-decoration: none;
        }
    }
}

/**
 * Event feed preview
 */ 
.event-feed-for-eventbrite-preview-wrapper {
    padding-top: 32px;
    padding-bottom: 32px;
    h1 {
        font-weight: 700;
        font-size: 24px;
        margin-bottom: 16px;
        color: var(--event-feed-for-eventbrite-preview-title-color);
    }
}
.event-feed-for-eventbrite-preview-info {
    margin-bottom: 32px;
    p {
        margin-bottom: 15px;
        color: var(--event-feed-for-eventbrite-preview-text-color);
    }
}
.event-feed-for-eventbrite-preview-shortcode {
    margin-top: 12px;
    input {
        width: 240px;
        max-width: 100%;
        background: #fff;
        text-align: center;
        color: var(--event-feed-for-eventbrite-preview-input-text-color);
    }
}

/**
 * Event feed styles
 */
.event-feed-for-eventbrite-app { 

    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    overflow: hidden;
    padding: 0 5px;
    // margin: 0 0 24px;
    margin: 0 -5px 24px;
    font-family: var(--event-feed-for-eventbrite-font-family);
    max-width: calc(100% + 10px);
    position: relative;

    * {
        box-sizing: border-box;
    }

    ::selection {
        background-color: var(--event-feed-for-eventbrite-theme-color);
        color: #fff;
    }

    // Message
    .eventbrite-info-message {
        font-size: 15px;
        width: 100%;
        text-align: center;
    }

    // Error
    .event-feed-for-eventbrite-error {
        color: var(--event-feed-for-eventbrite-preview-text-color);
        a {
            text-decoration: underline;
            &:hover {
                text-decoration: underline;
            }
        }
    }

    // Copyright info
    .eventbrite-copyright {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        font-size: 14px;
        font-weight: 400;
        color: var(--event-feed-for-eventbrite-title-color);
        max-width: 100%;
        padding-top: 24px;
        margin: 0 auto;
        position: relative;
        a {
            text-decoration: none;
            margin-left: 5px;
            align-items: center;
            display: flex;
            font-weight: 600;
            color: var(--event-feed-for-eventbrite-theme-color);
            &:hover {
                color: var(--event-feed-for-eventbrite-theme-color);
            }
            @media (max-width: 450px) {
                margin-left: 0;
            }
        }
        & > div {
            @media (max-width: 450px) {
                display: none;
            }
        }
        .brand-img {
            display: flex;
            width: 23px;
            height: 23px;
            // background-color: #eaeefc;
            background-color: hsl( 
                var(--event-feed-for-eventbrite-theme-color-h),
                var(--event-feed-for-eventbrite-theme-color-s),
                95%
            );
            justify-content: center;
            align-items: center;
            border-radius: 5px;
            box-shadow: 0 3px 6px hsl( 
                var(--event-feed-for-eventbrite-theme-color-h),
                var(--event-feed-for-eventbrite-theme-color-s),
                95%
            );
            span {
                display: block;
                font-size: 14px;
                font-style: italic;
            }
        }
        .brand-text {
            display: block;
            // margin-left: 7px;
            font-weight: 600;
            color: #323546;
            strong {
                padding-right: 2px;
                font-style: italic;
                color: var(--event-feed-for-eventbrite-theme-color);
                font-weight: 700;
            }
        }
    }
    .eventbrite-copyright-left {
        justify-content: flex-start;
        // padding-left: 60px;
    }

    // General styles for event feed
    .eventbrite-feed {

        .eventbrite-item {
            max-width: 100%;
            margin: 0 auto;
            // display: flex;
            width: 100%;
            // transition: all .2s cubic-bezier( .4, 0, .2, 1);
            padding: 0;
            // overflow: hidden;
        }
        .eventbrite-item-top-row {
            display: flex;
            padding: 0 0 10px;
        }
        .eventbrite-item-bottom-row {
            padding: 0;
            display: flex;
            flex-direction: column;
            flex: 1 1 0%;
        }
        .eventbrite-item-separator {
            border: none;
            border-bottom: 1px solid var(--event-feed-for-eventbrite-separator-color);
            padding: 0;
            margin: -8px 0 0;
            height: auto;
            background: none;
            border-top: none;
            box-shadow: none;
            max-width: 100%;
        }
        .eventbrite-item-image {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            margin: 0;
            padding: 0;
            display: block;
            position: relative;
            border: none;
            box-shadow: none;
            img {
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
                line-height: 0;
                display: block;
                margin: 0;
                padding: 0;
                width: 100%;
                height: auto;
            }
        }
        .eventbrite-item-image-inner {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            margin: 0;
            padding: 0;
            display: block;
            position: relative;
            border: none;
            box-shadow: none;
            cursor: pointer;
            opacity: 1;
            transition: all .2s cubic-bezier( .4, 0, .2, 1);
            &:hover {
                opacity: .9;
            }
        }
        .eventbrite-item-image-tags {
            margin: 0;
            position: absolute;
            top: 10px;
            left: 10px;
            display: flex;
            span {
                margin: 0 5px 0 0;
                text-align: left;
                display: inline-block;
                padding: 2px 8px;
                border-radius: 5px;
                background-color: #fff;
                text-transform: uppercase;
                font-size: 10px;
                line-height: 1.8;
                font-weight: 500;
                letter-spacing: normal;
                text-align: center;
                color: var(--event-feed-for-eventbrite-price-color);
                &:last-child {
                    margin-right: 0;
                }
            }
        }
        .eventbrite-item-image-price {
            .currency {
                text-transform: none;
            }
        }
        .eventbrite-item-content {
            border-radius: 5px;
            font-size: 14px;
            margin: 0;
            padding: 0;
        }
        .eventbrite-item-image + .eventbrite-item-content {
            border-bottom-right-radius: 5px;
            border-bottom-left-radius: 5px;
        }
        .eventbrite-item-tags {
            display: flex;
            align-items: baseline;
            margin: 3px 0 10px;
            div {
                padding: 2px 6px;
                margin-right: 5px;
                border: 1px solid #e5e5e5;
                border-radius: 5px;
                display: inline-block;
                text-transform: uppercase;
                font-size: 10px;
                line-height: 1.8;
                font-weight: 500;
                letter-spacing: normal;
                text-align: center;
                color: var(--event-feed-for-eventbrite-desc-color);
                .currency {
                    text-transform: none;
                }
            }
        }
        .eventbrite-item-date {
            padding-top: 2px;
            margin-right: 15px;
            letter-spacing: -0.2px;
            text-align: center;
        }
        .eventbrite-item-date-month {
            display: block;
            color: var(--event-feed-for-eventbrite-theme-color);
            font-size: 13px;
            font-weight: 500;
            text-transform: uppercase;
            line-height: 1;
            margin-bottom: 4px;
        }
        .eventbrite-item-date-day {
            display: block;
            color: var(--event-feed-for-eventbrite-theme-color);
            font-size: 20px;
            font-weight: 600;
            line-height: 1;
        }
        .eventbrite-item-date-next-events {
            svg {
                width: 15px;
                height: 15px;
                fill: var(--event-feed-for-eventbrite-theme-color);
            }
        }
        .eventbrite-item-title {
            font-family: var(--event-feed-for-eventbrite-font-family);
            position: relative;
            top: -2px;
            display: block;
            text-align: left;
            margin: 0;
            padding: 0;
            font-size: 0;
            line-height: 0;
            font-size: 18px;
            line-height: 1.29;
            font-weight: 600;
            font-stretch: normal;
            font-style: normal;
            letter-spacing: normal;
            text-decoration: none;
            text-transform: none;
            text-align: left;
            letter-spacing: -.3px;
            color: var(--event-feed-for-eventbrite-title-color);
            transition: all .3s ease-in-out;
            cursor: pointer;
            &:hover {
                color: var(--event-feed-for-eventbrite-theme-color);
            }
        }
        .eventbrite-item-datetime {
            display: block;
            text-transform: uppercase;
            margin: 3px 0 0 0;
            font-size: 13px;
            font-weight: 400;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.54;
            letter-spacing: normal;
            text-align: left;
            color: var(--event-feed-for-eventbrite-datetime-color);
        }
        .eventbrite-item-location {
            display: flex;
            align-items: flex-start;
            font-weight: 400;
            font-size: 13px;
            line-height: 1.43;
            font-stretch: normal;
            font-style: normal;
            margin: 0 0 8px;
            letter-spacing: normal;
            text-align: left;
            text-transform: none;
            color: var(--event-feed-for-eventbrite-location-color);
            display: flex;
        }
        .eventbrite-item-location-icon {
            position: relative;
            top: 1px;
            width: 11.1px;
            height: 14.8px;
            fill: var(--event-feed-for-eventbrite-location-icon-color);
            margin-right: 9px;
            opacity: .65;
        }
        .eventbrite-item-location-icon-online {
            position: relative;
            top: 1px;
            width: 17.4px;
            height: 13px;
            fill: var(--event-feed-for-eventbrite-location-icon-color);
            margin-right: 9px;
            opacity: .65;
        }
        .eventbrite-item-description {
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.43;
            letter-spacing: normal;
            text-align: left;
            margin: 0 0 8px;
            color: var(--event-feed-for-eventbrite-desc-color);
        }
        .eventbrite-item-buttons {
            display: flex;
            flex-wrap: wrap;
            justify-items: center;
            padding: 0;
            margin: auto 0 5px;
            button {
                margin-top: 8px;
            }
        }
        .eventbrite-item-checkout {
            font-family: var(--event-feed-for-eventbrite-font-family);
            display: inline-block;
            transition: all .3s ease-in-out;
            text-decoration: none;
            text-transform: none;
            border-radius: 5px;
            color: var(--event-feed-for-eventbrite-sign-up-button-color);
            background-color: var(--event-feed-for-eventbrite-theme-color);
            padding: 7px 16px 8px;
            font-size: 12px;
            font-weight: 600;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.5;
            letter-spacing: normal;
            text-align: left;
            margin-right: 10px;
            box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;
            &:hover {
                color: var(--event-feed-for-eventbrite-sign-up-button-color);
                background-color: hsl( 
                    var(--event-feed-for-eventbrite-theme-color-h),
                    var(--event-feed-for-eventbrite-theme-color-s),
                    var(--event-feed-for-eventbrite-theme-color-l-dark)
                );
            }
            &:focus {
                outline: none;
                background-color: hsl( 
                    var(--event-feed-for-eventbrite-theme-color-h),
                    var(--event-feed-for-eventbrite-theme-color-s),
                    var(--event-feed-for-eventbrite-theme-color-l-dark)
                );
            }
        }
        .eventbrite-item-details {
            font-family: var(--event-feed-for-eventbrite-font-family);
            display: inline-block;
            transition: all .3s ease-in-out;
            text-decoration: none;
            text-transform: none;
            border-radius: 5px;
            color: var(--event-feed-for-eventbrite-theme-color);
            background-color: #fff;
            box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;
            padding: 7px 16px 8px;
            font-size: 12px;
            font-weight: 600;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.5;
            letter-spacing: normal;
            text-align: left;
            &:hover {
                color: hsl( 
                    var(--event-feed-for-eventbrite-theme-color-h),
                    var(--event-feed-for-eventbrite-theme-color-s),
                    var(--event-feed-for-eventbrite-theme-color-l-dark)
                );
                background-color: #fff;
            }
            &:focus {
                outline: none;
                color: hsl( 
                    var(--event-feed-for-eventbrite-theme-color-h),
                    var(--event-feed-for-eventbrite-theme-color-s),
                    var(--event-feed-for-eventbrite-theme-color-l-dark)
                );
                background-color: #fff;
            }
        }

    }

    // List specific styles
    .eventbrite-feed-list {
        margin: 0 auto -40px;
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        max-width: 100%;
        .eventbrite-item-top-row {
            padding: 0 0 8px;
            @media (max-width: 991px) {
                padding: 18px 0 10px;
            }
        }
        .eventbrite-item {
            display: flex;
            margin-bottom: 40px;
            position: relative;
            @media (max-width: 991px) {
                flex-direction: column;
            }
        }
        .eventbrite-item-image {
            width: 40%;
            max-width: 400px;
            margin-bottom: 5px;
            flex-shrink: 0;
            margin-left: auto;
            img {
                border-radius: 5px;
            }
            @media (max-width: 1279px) {
                width: 40%;
                max-width: 100%;
            }
            @media (max-width: 991px) {
                order: 1;
                width: 400px;
                margin-bottom: 0;
                margin-left: 0;
            }
        }
        .eventbrite-item-content {
            // width: calc( 100% - 400px );
            padding-right: 30px;
            @media (max-width: 1279px) {
                width: 60%;
                max-width: 100%;
            }
            @media (max-width: 991px) {
                order: 2;
                width: 100%;
                padding-right: 0;
            }
        }
        .eventbrite-item-date {
            left: 0;
            top: 0;
            position: absolute;
            width: 45px;
        }
        .eventbrite-item-title {
            font-size: 20px;
            @media (max-width: 991px) {
                font-size: 18px;
            }
        }
        .eventbrite-item-date-day {
            font-size: 22px;
        }
        .eventbrite-item-buttons {
            margin: 0 0 5px;
        }
        .eventbrite-item-datetime {
            margin: -1px 0 5px;
            font-weight: 500;
            color: var(--event-feed-for-eventbrite-theme-color);
        }
        .eventbrite-item-short-date {
            padding-left: 60px;
            .eventbrite-item-datetime {
                margin: -1px 0 3px;
                font-weight: 400;
                color: var(--event-feed-for-eventbrite-datetime-color);
            }
        }
    }

    // Widget specific styles
    .eventbrite-feed-widget {
        display: block;
        margin-bottom: -30px;
        .eventbrite-item {
            display: block;
            margin-bottom: 30px;
        }
        .eventbrite-item-image {
            max-width: 100%;
            width: 400px;
            img {
                border-radius: 5px;
            }
        }
        .eventbrite-item-content {
            width: 100%;
            max-width: 100%;
        }
        .eventbrite-item-date {
            left: 0;
            top: 0;
            position: absolute;
            width: 45px;
        }
        .eventbrite-item-title {
            font-size: 18px;
        }
        .eventbrite-item-datetime {
            margin: 10px 0 5px;
            font-weight: 500;
            color: var(--event-feed-for-eventbrite-theme-color);
        }
        .eventbrite-item-top-row {
            padding: 0 0 8px;
        }
        .eventbrite-item-location {
            font-size: 13px;
        }
        .eventbrite-item-tags {
            margin: 0 0 7px;
        }
    }

    // Grid specific styles
    .eventbrite-feed-grid {
        width: calc( 100% + 24px );
        max-width: calc( 100% + 24px );
        margin: 0 -12px -30px;
        display: flex;
        flex-wrap: wrap;
        box-sizing: border-box;
        .eventbrite-item {
            box-sizing: border-box;
            padding: 0;
            margin: 0 12px 30px;
            width: calc( 100% / var(--event-feed-for-eventbrite-rows-desktop) - 24.01px );
            @media (max-width: 1279px) {
                width: calc( 100% / var(--event-feed-for-eventbrite-rows-large-tablets) - 24.01px );
            }
            @media (max-width: 1023px) {
                width: calc( 100% / var(--event-feed-for-eventbrite-rows-small-tablets) - 24.01px );
            }
            @media (max-width: 767px) {
                width: calc( 100% / var(--event-feed-for-eventbrite-rows-mobile) - 24.01px );
            }
        }
        .eventbrite-item-top-row {
            padding: 0 0 8px;
        }
        .eventbrite-item-bottom-row {
            padding: 0 0 13px 0;
        }
        .eventbrite-item-buttons {
            margin: 0 0 5px;
        }
        .eventbrite-item-image {
            border-bottom-right-radius: 5px;
            border-bottom-left-radius: 5px;
        }
        .eventbrite-item-content {
            border-radius: 0;
        }
        .eventbrite-item-title-col {
            display: flex;
            flex-direction: column;
            width: 100%;
        }
        .eventbrite-item-datetime {
            order: 1;
            margin: 18px 0 6px;
            font-weight: 500;
            color: var(--event-feed-for-eventbrite-theme-color);
        }
        .eventbrite-item-title {
            order: 2;
            font-size: 19px;
            @media (max-width: 767px) {
                font-size: 18px;
            }
        }
        .eventbrite-item-image {
            width: 100%;
            @media (max-width: 767px) {
                width: 400px;
                max-width: 100%;
            }
            img {
                border-radius: 5px;
            }
        }
        .eventbrite-item-short-date {
            .eventbrite-item-top-row {
                padding: 18px 0;
            }
            .eventbrite-item-bottom-row {
                padding: 13px 0 0;
            }
            .eventbrite-item-datetime {
                order: 2;
                margin: 3px 0 0 0;
                font-weight: 400;
                color: var(--event-feed-for-eventbrite-datetime-color);
            }
            .eventbrite-item-title {
                order: 1;
                font-size: 18px;
            }
        }

    }

    // Cards specific styles
    .eventbrite-feed-cards {
        width: calc( 100% + 24px );
        max-width: calc( 100% + 24px );
        margin: 0 -12px -24px;
        display: flex;
        flex-wrap: wrap;
        box-sizing: border-box;
        .eventbrite-item {
            display: flex;
            flex-direction: column;
            justify-content: stretch;
            box-sizing: border-box;
            padding: 0;
            margin: 0 12px 30px;
            width: calc( 100% / var(--event-feed-for-eventbrite-rows-desktop) - 24.01px );
            background-color: var(--event-feed-for-eventbrite-card-bg);
            border-radius: 5px;
            box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;
            @media (max-width: 1279px) {
                width: calc( 100% / var(--event-feed-for-eventbrite-rows-large-tablets) - 24.01px );
            }
            @media (max-width: 1023px) {
                width: calc( 100% / var(--event-feed-for-eventbrite-rows-small-tablets) - 24.01px );
            }
            @media (max-width: 767px) {
                width: calc( 100% / var(--event-feed-for-eventbrite-rows-mobile) - 24.01px );
            }
        }
        .eventbrite-item-image {
            flex: 0 1 auto;
        }
        .eventbrite-item-content {
            flex: 1 1 auto;
            display: flex;
            flex-direction: column;
            justify-content: stretch;
        }
        .eventbrite-item-title-col {
            display: flex;
            flex-direction: column;
            width: 100%;
        }
        .eventbrite-item-datetime {
            order: 1;
            font-weight: 500;
            color: var(--event-feed-for-eventbrite-theme-color);
            margin-bottom: 5px;
        }
        .eventbrite-item-title {
            order: 2;
        }
        .eventbrite-item-top-row {
            padding: 12px 18px 18px;
        }
        .eventbrite-item-bottom-row {
            padding: 13px 18px;
        }
        .eventbrite-item-short-date {
            .eventbrite-item-top-row {
                padding: 18px;
            }
            .eventbrite-item-datetime {
                order: 2;
                font-weight: 400;
                color: var(--event-feed-for-eventbrite-datetime-color);
                margin-bottom: 0;
            }
            .eventbrite-item-title {
                order: 1;
            }
        }

    }

}

/**
* Modal styles
*/
.eventbrite-modal-bg {
    background-color: rgba(57,54,79,.8);
}
.eventbrite-modal-wrapper {
    display: flex;
    align-items: center;
    .vm {
        top: auto;   
    }
}

// Admin bar
.admin-bar {
    .eventbrite-modal {
        margin-top: 32px;
        max-height: calc( 95% - 32px );
        @media (max-width: 782px) {
            margin-top: 46px;
            max-height: calc( 95% - 46px );
        }
        @media (max-width: 660px) {
            max-height: calc( 100% - 46px );
        }
        @media (max-width: 600px) {
            max-height: 100%;
            margin-top: 0;
        }
    }
}
.admin-bar.eventbrite-scroll-top .eventbrite-modal {
    @media (max-width: 600px) {
        max-height: calc( 100% - 46px );
        margin-top: 46px;
    }
}
.eventbrite-modal {
    cursor: auto;
    box-shadow: none;
    display: flex;
    flex-direction: column;
    top: 0;
    max-width: 600px;
    max-height: 95%;
    height: auto;
    @media (max-width: 660px) {
        width: 100%;
        max-width: 100%;
        max-height: 100%;
    }
    .vm-titlebar {
        display: none;
    }
    .vm-content {
        font-family: var(--event-feed-for-eventbrite-font-family);
        padding: 0;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        min-height: 0;
        overflow: hidden;
    }

    // Event info (scrollable)
    .scroll-content {
        position: relative;
        display: block;
        overflow: auto;
        -webkit-overflow-scrolling: touch;

        /**
        * Scrollbar styling
        */

        /* Works on Firefox */
        & {
            scrollbar-width: thin;
            scrollbar-color: #aaa #f1f1f1;
        }
        
        /* Works on Chrome, Edge, and Safari */
        &::-webkit-scrollbar {
            width: 12px;
        }
        
        &::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        
        &::-webkit-scrollbar-thumb {
            background-color: #aaa;
            border-radius: 20px;
            border: 3px solid #f1f1f1;
        }

    }

    .eventbrite-modal-img {
        position: relative;
        svg {
            display: block;
            line-height: 0;
        }
        img {
            width: 100%;
            height: auto;
            display: block;
        }
    }

    .eventbrite-modal-back {
        line-height: 0;
        display: block;
        right: auto;
        left: 10px;
    }

    // Add to calendar
    .eventbrite-modal-add-to-calendar-wrapper {
        right: 15px;
        bottom: 15px;
        position: absolute;
        z-index: 2;
    }
    .eventbrite-modal-add-to-calendar {
        font-family: var(--event-feed-for-eventbrite-font-family);
        background-color: var(--event-feed-for-eventbrite-card-bg);
        border-radius: 5px;
        color: var(--event-feed-for-eventbrite-title-color);
        display: flex;
        align-items: center;
        padding: 10px 12px;
        text-decoration: none;
        transition: all .3s ease-in-out;
        box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;
        @media (max-width: 479px) {
            padding: 10px;
        }
        span {
            padding-left: 7px;
            line-height: 1;
            font-size: 12px;
            font-stretch: normal;
            font-style: normal;
            font-weight: 500;
            letter-spacing: normal;
            text-transform: none;
        }
        i {
            position: relative;
            top: -1px;
        }
        svg {
            width: 18px;
            height: 18px;
            fill: none;
            transition: all .3s ease-in-out;
            stroke: var(--event-feed-for-eventbrite-title-color);
        }
        &:hover,
        &:focus {
            color: hsl( 
                var(--event-feed-for-eventbrite-theme-color-h),
                var(--event-feed-for-eventbrite-theme-color-s),
                var(--event-feed-for-eventbrite-theme-color-l-dark)
            );
            background-color: var(--event-feed-for-eventbrite-card-bg);
            svg {
                fill: none;
                stroke: hsl( 
                    var(--event-feed-for-eventbrite-theme-color-h),
                    var(--event-feed-for-eventbrite-theme-color-s),
                    var(--event-feed-for-eventbrite-theme-color-l-dark)
                );
            }
        }
    }
    .eventbrite-modal-add-to-calendar-menu {
        font-family: var(--event-feed-for-eventbrite-font-family);
        z-index: 1;
        position: absolute;
        right: 0;
        top: 0;
        min-width: 100%;
        border-radius: 5px;
        border-top-right-radius: 0;
        box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;
        a.eventbrite-modal-add-to-calendar-menu-item {
            text-decoration: none;
            color: var(--event-feed-for-eventbrite-title-color);
            background-color: var(--event-feed-for-eventbrite-card-bg);
            position: relative;
            z-index: 1;
            display: block;
            white-space: nowrap;
            font-size: 13px;
            font-weight: 500;
            line-height: 18px;
            padding: 8px 12px;
            border-bottom: 1px solid var(--event-feed-for-eventbrite-separator-color);
            &:hover,
            &:focus {
                color: var(--event-feed-for-eventbrite-theme-color);
            }
            &:last-child {
                border-bottom: none;
                border-bottom-left-radius: 5px;
                border-bottom-right-radius: 5px;
            }
            &:first-of-type {
                border-top-left-radius: 5px;
                border-top-right-radius: 0;
            }
        }
    }
    .eventbrite-modal-add-to-calendar-menu-close {
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: -26px;
        right: 0;
        margin: 0;
        padding: 0;
        border-radius: 0;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
        cursor: pointer;
        width: 26px;
        height: 26px;
        background-color: #fff;
        box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
        svg {
            fill: var(--event-feed-for-eventbrite-title-color);
            top: 1px;
            position: relative;
            width: 13px;
            height: 13px;
        }
        &:hover,
        &:focus {
            background-color: hsl( 
                var(--event-feed-for-eventbrite-theme-color-h),
                var(--event-feed-for-eventbrite-theme-color-s),
                95%
            );
        }
    }


    // Modal controls
    .eventbrite-modal-back,
    .eventbrite-modal-close {
        z-index: 3;
        position: absolute;
        right: 10px;
        top: 10px;
        border-radius: 40px;
        button {
            background-color: rgb(248,247,250,.75);
            border-radius: 40px;
            height: 40px;
            padding: 8px;
            transition: all .4s cubic-bezier(.4,0,.3,1);
            width: 40px;
        }
        &:hover {
            button {
                background-color: rgba(248,247,250,1);
            }
        }
        i {
            display: block;
            margin: 0 auto;
            background-size: contain;
            line-height: 0;
            width: 24px;
            height: 24px;
        }
        svg {
            width: 24px;
            height: 24px;
            path {
                fill: #4b4d63;
            }
        }
    }

    .eventbrite-modal-event-info {
        padding: 20px 24px 24px;
        @media (max-width: 479px) {
            padding: 15px 16px 24px;
        }
    }
    .eventbrite-modal-event-tickets {
        height: 98%;
        position: relative;
    }
    .eventbrite-modal-footer {
        padding: 0 24px 24px;
        @media (max-width: 479px) {
            padding: 0 16px 24px;
        }
    }
    .eventbrite-modal-title {
        padding: 20px 24px 15px;
        line-height: 1.5;
        border-bottom: 1px solid var(--event-feed-for-eventbrite-separator-color);
        @media (max-width: 479px) {
            padding: 20px 16px 15px;
        }
        h3 {
            font-family: var(--event-feed-for-eventbrite-font-family);
            color: var(--event-feed-for-eventbrite-title-color);
            font-weight: 600;
            margin: 0;
            padding: 2px 0 5px 0;
            font-size: 22px;
            line-height: 28px;
            text-transform: none;
            @media (max-width: 479px) {
                font-size: 19px;
                line-height: 24px;
            }
        }
    }
    .eventbrite-modal-datetime {
        color: var(--event-feed-for-eventbrite-theme-color);
        font-weight: 600;
        display: flex;
        align-items: center;
        font-size: 15px;
        line-height: 24px;
        @media (max-width: 479px) {
            font-size: 14px;
            line-height: 18px;
        }
        time {
            text-transform: uppercase;
        }
    }
    .eventbrite-modal-next-events {
        display: flex;
        align-items: center;
        padding-left: 7px;
        height: 24px;
        svg {
            padding-bottom: 1px;
            width: 18px;
            height: 18px;
            fill: var(--event-feed-for-eventbrite-theme-color);
        }
    }
    .eventbrite-modal-location {
        line-height: 24px;
        display: flex;
        align-items: center;
        font-size: 15px;
        color: var(--event-feed-for-eventbrite-modal-text-color);
        @media (max-width: 479px) {
            font-size: 14px;
            line-height: 18px;
            padding-top: 2px;
        }
        svg, span {
            transition: all .3s ease-in-out;
        }
        &.eventbrite-modal-location-clickable:hover {
            cursor: pointer;
            svg {
                fill: var(--event-feed-for-eventbrite-title-color);
            }
            span {
                color: var(--event-feed-for-eventbrite-title-color);
            }
        }
    }
    .eventbrite-modal-location-icon {
        width: 11.1px;
        height: 14.8px;
        fill: var(--event-feed-for-eventbrite-location-icon-color);
        margin-right: 9px;
        margin-top: -1px;
        opacity: .65;
    }
    .eventbrite-modal-location-icon-online {
        width: 17.4px;
        height: 13px;
        fill: var(--event-feed-for-eventbrite-location-icon-color);
        margin-right: 9px;
        opacity: .65;
    }
    
    // Description formatting
    .eventbrite-modal-summary,
    .eventbrite-modal-description-module {
        margin-bottom: 0;
        font-size: 15px;
        line-height: 24px;
        color: var(--event-feed-for-eventbrite-modal-text-color);
        @media (max-width: 479px) {
            line-height: 22px;
        }
        p, ul, ol, aside, blockquote {
            display: block;
            margin: 0;
            padding: 0;
            padding-top: 12px;
            font-size: 15px;
            line-height: 24px;
            color: var(--event-feed-for-eventbrite-modal-text-color);
            @media (max-width: 479px) {
                line-height: 22px;
            }
            &:last-child {
                padding-bottom: 0;
            }
        }
        ul {
            list-style-type: disc;
        }
        ol {
            list-style-type: decimal;
        }
        ul, ol {
            padding-top: 7px;
        }
        ol, ul, blockquote {
            font-size: 16px;
            padding-left: 32px;
            list-style-position: outside;
        }
        li {
            margin: 0;
            padding: 0;
            padding-top: 5px;
            font-size: 15px;
            line-height: 24px;
            color: var(--event-feed-for-eventbrite-modal-text-color);
        }
        strong, b {
            font-weight: 600;
        }
        em {
            font-style: italic;
        }
        a {
            color: var(--event-feed-for-eventbrite-theme-color);
            &:hover {
                text-decoration: none;
                color: hsl( 
                    var(--event-feed-for-eventbrite-theme-color-h),
                    var(--event-feed-for-eventbrite-theme-color-s),
                    var(--event-feed-for-eventbrite-theme-color-l-dark)
                );
            }
        }
        img {
            margin-top: 12px;
            display: block;
            line-height: 0;
            width: 100%;
            height: auto;
        }
        h1,h2,h3 {
            line-height: 28px;
            color: var(--event-feed-for-eventbrite-title-color);
            font-weight: 700;
            text-transform: none;
            margin: 0;
            padding: 0;
            padding-top: 15px;
        }
        h1 {
            font-size: 28px;
            line-height: 34px;
        }
        h2 {
            font-size: 24px;
            line-height: 32px;
        }
        h3 {
            font-size: 20px;
            line-height: 28px;
            font-weight: 400;
        }
        blockquote {
            color: var(--event-feed-for-eventbrite-title-color);
            border: none;
            font-size: 15px;
            font-style: normal;
            font-weight: 400;
        }
    }
    .eventbrite-modal-description-module-img,
    .eventbrite-modal-description-module-video {
        margin: 25px -24px 10px;
        @media (max-width: 479px) {
            margin: 20px -16px 5px;
        }
        &:last-child {
            margin-bottom: -24px;
        }
    }
    .eventbrite-modal-description-module-text {
        p:empty { display: none; } 
    }
    .eventbrite-modal-description-error {
        font-style: italic;
    }
    .eventbrite-modal-summary {
        font-size: 16px;
        font-weight: 600;
        color: var(--event-feed-for-eventbrite-title-color);
        margin-top: 0;
    }

    // Modal map
    .eventbrite-modal-map {
        line-height: 0;
        margin: 0;
        padding: 0;
    }
    .eventbrite-modal-map + .eventbrite-modal-organizer {
        border-top: none;    
    }

    // Event organizer
    .eventbrite-modal-organizer {
        border-top: 1px solid var(--event-feed-for-eventbrite-separator-color);
        padding: 20px 24px 5px;
        position: relative;
        @media (max-width: 479px) {
            padding: 20px 16px 5px;
        }
        h4 {
            color: var(--event-feed-for-eventbrite-modal-text-color);
            background-color: #fff;
            font-size: 11px;
            font-weight: 400;
            left: 20px;
            margin: 0;
            padding: 0 5px;
            position: absolute;
            text-transform: uppercase;
            top: -7px;
        }
        h4 + p {
            font-size: 15px;
            margin: 0;
            padding: 0;
        }
    }
    .eventbrite-modal-organizer-row {
        display: flex;
        img {
            display: block;
            line-height: 0;
            border-radius: 50%;
            max-width: 100%;
            height: auto;
        }
        .eventbrite-modal-organizer-image {
            flex-shrink: 0;
            padding-top: 5px;
            width: 50px;
            margin-right: 15px;
        }
        .eventbrite-modal-organizer-info {
            a {
                font-size: 15px;
                font-weight: 400;
                color: var(--event-feed-for-eventbrite-theme-color);
                text-decoration: none;
                &:hover {
                    color: hsl( 
                        var(--event-feed-for-eventbrite-theme-color-h),
                        var(--event-feed-for-eventbrite-theme-color-s),
                        var(--event-feed-for-eventbrite-theme-color-l-dark)
                    );
                }
            }
            p {
                margin-top: 0;
                margin-bottom: 15px;
                font-size: 14px;
                line-height: 21px;
                color: var(--event-feed-for-eventbrite-modal-text-color);
                @media (max-width: 479px) {
                    line-height: 20px;
                }
            }
        }
        .eventbrite-modal-organizer-description {
            margin-bottom: 15px;
            p:first-child, ul:first-child, ol:first-child, h1:first-child, h2:first-child, h3:first-child, aside:first-child, blockquote:first-child {
                margin-top: 0;
            }
            p, li {
                font-size: 14px;
                line-height: 21px;
                color: var(--event-feed-for-eventbrite-modal-text-color);
                @media (max-width: 479px) {
                    line-height: 20px;
                }
            }
            strong, b {
                font-weight: 600;
            }
            p, ul, ol, h1, h2, h3, aside, blockquote {
                margin-top: 10px;
                margin-bottom: 0;
            }
            ul {
                list-style-type: disc;
            }
            ol {
                list-style-type: decimal;
            }
            h1,h2,h3 {
                font-size: 14px;
                line-height: 21px;
                @media (max-width: 479px) {
                    line-height: 20px;
                }
            }
        }
    }
    .eventbrite-modal-organizer-name {
        line-height: 1.5;
        font-size: 15px;
        span:first-child {
            font-weight: 600;
            color: var(--event-feed-for-eventbrite-title-color);
        }
    }
    .eventbrite-modal-organizer-title {
        font-size: 14px;
        line-height: 21px;
        margin-bottom: 10px;
        color: var(--event-feed-for-eventbrite-title-color);
    }
    .eventbrite-modal-organizer-social {
        display: flex;
        margin-top: -5px;
        margin-bottom: 15px;
        a {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            margin-left: 6px;
            box-shadow: rgb(50, 50, 93, 25%) 0px 2px 5px -1px, rgb(0, 0, 0, 30%) 0px 1px 3px -1px;
            &:first-child {
                margin-left: 0;
            }
            &:hover {
                svg {
                    fill: hsl( 
                        var(--event-feed-for-eventbrite-theme-color-h),
                        var(--event-feed-for-eventbrite-theme-color-s),
                        var(--event-feed-for-eventbrite-theme-color-l-dark)
                    );
                    color: hsl( 
                        var(--event-feed-for-eventbrite-theme-color-h),
                        var(--event-feed-for-eventbrite-theme-color-s),
                        var(--event-feed-for-eventbrite-theme-color-l-dark)
                    );
                }
            }
            svg {
                transition: all 0.3s ease-in-out;
                max-height: 14px;
                width: 14px;
                height: auto;
                fill: var(--event-feed-for-eventbrite-title-color);
                color: var(--event-feed-for-eventbrite-title-color);
            }
        }
    }

    // Modal footer
    .noscroll-content {
        position: relative;
        z-index: 1;
        flex-shrink: 0;
        border-top: 1px solid #eeedf2;
        background-color: #fff;
        min-height: 44px;
        padding: 16px 24px;
        display: flex;
        align-items: center;
        // box-shadow: 0 -18px 50px 0 rgba( #fff, .8 );
        @media (max-width: 479px) {
            padding: 16px;
        }
        .eventbrite-modal-footer-left {
            display: flex;
            align-items: center;
            margin-right: auto;
        }
        .eventbrite-modal-footer-right {
            display: flex;
            align-items: center;
            margin-left: auto;
        }
        .eventbrite-modal-price {
            display: block;
            color: var(--event-feed-for-eventbrite-title-color);
            font-size: 15px;
            line-height: 24px;
            font-weight: 600;
        }
        .eventbrite-modal-external-button {
            font-family: var(--event-feed-for-eventbrite-font-family);
            display: block;
            transition: all .3s ease-in-out;
            text-decoration: none;
            box-shadow: none;
            border-radius: 5px;
            color: var(--event-feed-for-eventbrite-theme-color);
            background-color: #fff;
            padding: 12px 0;
            font-size: 14px;
            font-weight: 600;
            font-stretch: normal;
            font-style: normal;
            line-height: 20px;
            letter-spacing: normal;
            &:hover {
                transition: all .3s ease-in-out;
                color: hsl( 
                    var(--event-feed-for-eventbrite-theme-color-h),
                    var(--event-feed-for-eventbrite-theme-color-s),
                    var(--event-feed-for-eventbrite-theme-color-l-dark)
                );
            }
            &:focus {
                outline: none;
            }
            .desktop {
                @media (max-width: 450px) {
                    display: none;
                }
            }
        }
    }
    .eventbrite-modal-checkout-button {
        font-family: var(--event-feed-for-eventbrite-font-family);
        margin-left: 16px;
        display: block;
        transition: all .3s ease-in-out;
        text-decoration: none;
        text-transform: none;
        box-shadow: none;
        border-radius: 5px;
        color: var(--event-feed-for-eventbrite-sign-up-button-color);
        background-color: hsl( 
            var(--event-feed-for-eventbrite-theme-color-h),
            var(--event-feed-for-eventbrite-theme-color-s),
            var(--event-feed-for-eventbrite-theme-color-l)
        );
        padding: 12px 30px;
        font-size: 14px;
        font-weight: 600;
        font-stretch: normal;
        font-style: normal;
        line-height: 20px;
        letter-spacing: normal;
        &:hover {
            transition: all .3s ease-in-out;
            color: var(--event-feed-for-eventbrite-sign-up-button-color);
            background-color: hsl( 
                var(--event-feed-for-eventbrite-theme-color-h),
                var(--event-feed-for-eventbrite-theme-color-s),
                var(--event-feed-for-eventbrite-theme-color-l-dark)
            );
        }
        &:focus {
            outline: none;
            background-color: hsl( 
                var(--event-feed-for-eventbrite-theme-color-h),
                var(--event-feed-for-eventbrite-theme-color-s),
                var(--event-feed-for-eventbrite-theme-color-l-dark)
            );
        }
    }

}

// Event tickets (when toggled)
.modal-tickets {
    .eventbrite-modal {
        width: 95%;
        height: 820px;
        max-height: 95%;
        @media (max-width: 660px) {
            width: 100%;
            height: 100%;
            max-width: 100%;
            max-height: 100%;
        }
    }
    .scroll-content {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        @media (max-height: 700px) {
            .eventbrite-modal-img {
                display: none;
            }
        }
    }
    .eventbrite-modal-content {
        height: 100%;
        .replace-content {
            height: 100%;
        }
    }
}

// Event ticket order screen
.modal-order {
    .eventbrite-modal-img {
        display: none;
    }
    .scroll-content {
        @media (max-height: 700px) {
            padding-top: 0;
        }
    }
}

// Animation for spinner
@-webkit-keyframes sk-bounce {
    0%, 100% { -webkit-transform: scale(0.0) }
    50% { -webkit-transform: scale(1.0) }
}
@keyframes sk-bounce {
    0%, 100% { 
      transform: scale(0.0);
      -webkit-transform: scale(0.0);
    } 50% { 
      transform: scale(1.0);
      -webkit-transform: scale(1.0);
    }
}

/**
 * Vue.js related styles
 */
[v-cloak] {
    display: none
}