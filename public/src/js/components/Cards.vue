<template>
    
    <div>

        <!-- Event feed preloader -->
        <div :class="'eventbrite-modal-spinner-feed eventbrite-modal-spinner-feed-' + uid" v-show="(settings.api_key) && !feedError && !feedLoaded" v-cloak>
            <div class="eventbrite-modal-spinner">
                <div class="double-bounce1"></div>
                <div class="double-bounce2"></div>
            </div>
            <div class="eventbrite-modal-spinner-text">
                <p>{{ translations.loading_text }}</p>
            </div>
        </div>

        <!-- Events wrapper -->
        <div :id="feed.css_id" :class="'eventbrite-feed eventbrite-feed-id-' + feed.ID + ' eventbrite-feed-cards ' + feed.css_classes" v-if="(settings.api_key) && (events) && (events.length > 0)" v-show="feedLoaded" v-cloak>

            <!-- Event feed edit link -->
            <a class="eventbrite-feed-edit-link" target="_blank" v-if="admin && edit_url" :href="edit_url">
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#334ecd" width="16" height="16" version="1.1" id="lni_lni-pencil-alt" x="0px" y="0px" viewBox="0 0 64 64" style="enable-background:new 0 0 64 64;" xml:space="preserve">
                    <path d="M62.7,11.2c0-0.7-0.3-1.3-0.8-1.8c-1.3-1.3-2.5-2.5-3.7-3.7c-1.1-1.1-2.2-2.2-3.3-3.4c-0.4-0.5-1-0.9-1.6-1  c-0.7-0.1-1.5,0.1-2.1,0.6l-7.2,7.2H8.7c-4.1,0-7.4,3.3-7.4,7.4v38.9c0,4.1,3.3,7.4,7.4,7.4h38.9c4.1,0,7.4-3.3,7.4-7.4V19.9  l6.9-6.9C62.4,12.5,62.7,11.8,62.7,11.2z M33.3,36.6c-0.1,0.1-0.3,0.2-0.4,0.3l-8.6,2.9l2.8-8.6c0.1-0.2,0.1-0.3,0.3-0.4l19-19  l6,5.9L33.3,36.6z M51.5,55.4c0,2.1-1.7,3.9-3.9,3.9H8.7c-2.1,0-3.9-1.7-3.9-3.9V16.4c0-2.1,1.7-3.9,3.9-3.9h31.9L24.9,28.2  c-0.5,0.5-0.9,1.1-1.1,1.8l-3.8,11.6c-0.2,0.6-0.1,1.2,0.2,1.7c0.3,0.4,0.7,0.8,1.6,0.8h0.3l11.9-3.9c0.7-0.2,1.3-0.6,1.8-1.1  l15.8-15.7V55.4z M54.8,15.1l-6-5.9l4-4c1,1,1.9,1.9,2.9,2.9c1,1,2,2,3,3.1L54.8,15.1z"/>
                </svg>
                <span>{{ translations.edit_link_text }}</span>
            </a>

            <!-- Event -->
            <div :class="[(feed.display_short_datetime == 1) ? 'eventbrite-item-short-date' : '', 'eventbrite-item', (['on_sale', 'not_yet_on_sale', 'sales_ended', 'sold_out', 'unavailable'].includes(event.event_sales_status.sales_status) ? event.event_sales_status.sales_status : '')]" v-for="(event, eventIndex) in events" :key="event.ID" :data-event-id="event.ID" :data-event-index="eventIndex">

                <!-- Image wrapper -->
                <span class="eventbrite-item-image" v-if="feed.display_image && event.url">

                    <div class="eventbrite-item-image-inner" @click="getEventDetails(event, eventIndex)">
                        
                        <!-- Image -->
                        <img :src="event.vue.image" :alt="event.post_title" v-if="event.vue.image" width="400" height="200" loading="lazy">

                        <!-- Tags -->
                        <span class="eventbrite-item-image-tags" v-if="( feed.display_price || feed.display_tickets ) && feed.price_overlay">

                            <!-- Price -->
                            <span class="eventbrite-item-image-price" v-if="feed.display_price && event.vue.price" v-html="event.vue.price"></span>

                            <!-- Tickets left -->
                            <span class="eventbrite-item-image-available-tickets" v-if="feed.display_tickets && event.tickets_remaining && ( event.tickets_remaining > 0 )">{{ event.tickets_remaining }}{{ ticketsText( feed.tickets_text ) }}</span>
                            
                        </span>

                    </div>

                </span>
                    
                <!-- Event content -->
                <div class="eventbrite-item-content">
                    
                    <div class="eventbrite-item-top-row">
                        
                        <!-- Short date -->
                        <div class="eventbrite-item-date_col" v-if="feed.display_short_datetime && !settings.shortdate_months">
                            <div class="eventbrite-item-date" v-if="event.vue.start_weekday || event.vue.start_day">
                                <span class="eventbrite-item-date-month" v-if="event.vue.start_weekday">{{ event.vue.start_weekday }}</span>
                                <span class="eventbrite-item-date-day" v-if="event.vue.start_day">{{ event.vue.start_day }}</span>
                            </div>
                        </div>

                        <div class="eventbrite-item-date_col" v-if="feed.display_short_datetime && settings.shortdate_months">
                            <div class="eventbrite-item-date" v-if="event.vue.start_month || event.vue.start_day">
                                <span class="eventbrite-item-date-month" v-if="event.vue.start_month">{{ event.vue.start_month }}</span>
                                <span class="eventbrite-item-date-day" v-if="event.vue.start_day">{{ event.vue.start_day }}</span>
                            </div>
                        </div>

                        <div class="eventbrite-item-title-col">

                            <!-- Title -->
                            <h3 class="eventbrite-item-title" v-if="event.vue.title" @click="getEventDetails(event, eventIndex)">
                                {{ event.vue.title }}
                            </h3>

                            <!-- Date and time -->
                            <time class="eventbrite-item-datetime" v-if="feed.display_datetime && event.vue.start">
                                {{ event.vue.start }} - {{ event.vue.end }}
                            </time>

                        </div>

                    </div>

                    <hr class="eventbrite-item-separator" v-if="(feed.display_location) || (feed.display_description) || (feed.display_price && !feed.display_image && event.vue.price) || (feed.display_signup_button) || (feed.display_more_button)">

                    <div class="eventbrite-item-bottom-row" v-if="(feed.display_location) || (feed.display_description) || (feed.display_price && !feed.display_image && event.vue.price) || (feed.display_signup_button) || (feed.display_more_button)">

                        <!-- Location -->
                        <p class="eventbrite-item-location" v-if="feed.display_location">
                            <span class="eventbrite-item-location-icon-wrapper">
                                <svg version="1.1" class="eventbrite-item-location-icon" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 48 64" style="enable-background:new 0 0 48 64;" xml:space="preserve" v-if="event.vue.location !== 'Online'">
                                    <g>
                                        <path d="M24,0C10.7,0,0,11.2,0,25.3c0,12,16.5,31.7,21.6,37.6c0.5,0.8,1.6,1.1,2.4,1.1c1.1,0,1.9-0.5,2.4-1.1 C31.5,57.1,48,37.1,48,25.3C48,11.2,37.3,0,24,0z M24,57.6C14.9,46.9,5.3,32.8,5.3,25.3c0-11.2,8.3-20,18.7-20s18.7,9.1,18.7,20 C42.7,32.8,33.1,46.9,24,57.6z"/>
                                        <path d="M24,13.3c-5.9,0-10.7,4.8-10.7,10.7S18.1,34.7,24,34.7S34.7,29.9,34.7,24S29.9,13.3,24,13.3z M24,29.3 c-2.9,0-5.3-2.4-5.3-5.3s2.4-5.3,5.3-5.3s5.3,2.4,5.3,5.3S26.9,29.3,24,29.3z"/>
                                    </g>
                                </svg>
                                <svg version="1.1" class="eventbrite-item-location-icon-online" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 64.1 48" style="enable-background:new 0 0 64.1 48;" xml:space="preserve" v-if="event.vue.location == 'Online'">
                                    <g>
                                        <path d="M32,13.9c-3.5,0-6.4,2.9-6.4,6.1c0,2.4,1.6,4.5,3.7,5.6v19.7c0,1.6,1.1,2.7,2.7,2.7s2.7-1.1,2.7-2.7V25.9 c2.1-1.1,3.7-3.2,3.7-5.6C38.4,16.8,35.5,13.9,32,13.9z M32,19.2c0.5,0,1.1,0.5,1.1,0.8s-0.5,0.8-1.1,0.8c-0.5,0-1.1-0.5-1.1-0.8 S31.5,19.2,32,19.2z"/>
                                        <path d="M24.5,10.9c-1.1-1.1-2.7-1.1-3.7,0c-2.4,2.4-3.5,5.3-3.5,8.5s1.3,6.4,3.5,8.8c0.5,0.5,1.3,0.8,1.9,0.8 c0.8,0,1.3-0.3,1.9-0.8c1.1-1.1,1.1-2.7,0-3.7c-1.3-1.3-2.1-2.9-2.1-4.8s0.8-3.5,2.1-4.8C25.6,13.6,25.6,12,24.5,10.9z"/>
                                        <path d="M43.2,10.9c-1.1-1.1-2.7-1.1-3.7,0c-1.1,1.1-1.1,2.7,0,3.7c1.3,1.3,2.1,2.9,2.1,4.8s-0.8,3.5-2.1,4.8 c-1.1,1.1-1.1,2.7,0,3.7c0.5,0.5,1.3,0.8,1.9,0.8c0.8,0,1.3-0.3,1.9-0.8c2.4-2.4,3.5-5.3,3.5-8.8C46.9,16.3,45.6,13.1,43.2,10.9z"/>
                                        <path d="M13.9,19.5c0-3.7,1.3-7.2,4-9.9c1.1-1.1,1.1-2.7,0-3.7c-1.1-1.1-2.7-1.1-3.7,0c-3.7,3.7-5.6,8.5-5.6,13.6s2.1,9.9,5.6,13.6 c0.5,0.5,1.1,0.8,1.9,0.8s1.3-0.3,1.9-0.8c1.1-1.1,1.1-2.7,0-3.7C15.5,26.7,13.9,23.2,13.9,19.5z"/>
                                        <path d="M49.9,5.9c-1.1-1.1-2.7-1.1-3.7,0s-1.1,2.7,0,3.7c2.7,2.7,4,6.1,4,9.9s-1.3,7.2-4,9.9c-1.1,1.1-1.1,2.7,0,3.7 c0.5,0.5,1.3,0.8,1.9,0.8c0.8,0,1.3-0.3,1.9-0.8c3.7-3.7,5.6-8.5,5.6-13.6C55.5,14.4,53.3,9.3,49.9,5.9z"/>
                                        <path d="M5.3,19.5c0-5.6,2.1-10.9,6.1-14.9c1.1-1.1,1.1-2.7,0-3.7s-2.7-1.1-3.7,0C2.7,5.9,0,12.3,0,19.5s2.7,13.6,7.7,18.7 C8.2,38.7,8.8,39,9.6,39c0.8,0,1.3-0.3,1.9-0.8c1.1-1.1,1.1-2.7,0-3.7C7.5,30.4,5.3,25.1,5.3,19.5z"/>
                                        <path d="M56.3,0.8c-1.1-1.1-2.7-1.1-3.7,0s-1.1,2.7,0,3.7c4,4,6.1,9.3,6.1,14.9s-2.1,10.9-6.1,14.9c-1.1,1.1-1.1,2.7,0,3.7 c0.5,0.5,1.3,0.8,1.9,0.8c0.8,0,1.3-0.3,1.9-0.8c5.1-5.1,7.7-11.7,7.7-18.7S61.3,5.9,56.3,0.8z"/>
                                    </g>
                                </svg>
                            </span>
                            <span>{{ event.vue.location }}</span>
                        </p>
                        
                        <!-- Summary -->
                        <p class="eventbrite-item-description" v-if="feed.display_description">{{ event.vue.description }}</p>

                        <!-- Tags -->
                        <div class="eventbrite-item-tags" v-if="( feed.display_price || feed.display_tickets ) && !feed.price_overlay">

                            <!-- Price -->
                            <div class="eventbrite-item-price" v-if="feed.display_price && event.vue.price" v-html="event.vue.price"></div>

                            <!-- Tickets left -->
                            <div class="eventbrite-item-available-tickets" v-if="feed.display_tickets && event.tickets_remaining && ( event.tickets_remaining > 0 )">{{ event.tickets_remaining }}{{ ticketsText( feed.tickets_text ) }}</div>
                            
                        </div>

                        <!-- Buttons -->
                        <div class="eventbrite-item-buttons" v-if="feed.display_signup_button || feed.display_more_button">
                            
                            <!-- Checkout -->
                            <button :id="'eventbrite-checkout-button-' + event.ID" class="eventbrite-item-checkout" v-if="(event.event_sales_status.sales_status == 'on_sale') && feed.display_signup_button" @click="checkoutFromEventCard(event, eventIndex)">
                                {{ checkoutButtonText( event ) }}
                            </button>

                            <!-- Read more -->
                            <button class="eventbrite-item-details" v-if="feed.display_more_button" @click="getEventDetails(event, eventIndex)">
                                {{ feed.more_button_text }}
                            </button>

                        </div>

                    </div>
                    
                </div>

            </div>

        </div>

        <!-- No results -->
        <div class="eventbrite-info-message" v-if="(events) && (events.length === 0)" v-show="feedLoaded" v-cloak>{{ translations.no_events_text }}</div>

        <!-- Error messages -->
        <div class="eventbrite-info-message" v-if="feedError == true" v-cloak>{{ translations.loading_error_text }}</div>
        <div class="eventbrite-info-message" v-if="settings.api_key == false" v-cloak>{{ translations.api_error_text }}</div>

        <!-- Copyright -->
        <div class="eventbrite-copyright" v-if="(events) && (events.length > 0) && (settings.show_copyright)" v-show="feedLoaded" v-cloak>
            <div>{{ translations.copyright_text }}</div>
            <a href="https://eventfeed.click/" target="_blank">
                <span class="brand-text"><strong>Event Feed</strong> for Eventbrite</span>
            </a>
        </div>
        
        <!-- Modal -->
        <Modal v-model="modalShow" :modal-class="'eventbrite-modal scrollable-modal eventbrite-modal-' + uid + ' eventbrite-modal-id-' + feed.ID" bg-class="eventbrite-modal-bg" wrapper-class="eventbrite-modal-wrapper" @before-open="beforeOpen" @before-close="beforeClose" @after-close="afterClose" @after-open="afterOpen" append-to="#eventbrite-modal-container" base-zindex="20000" :live="false" v-cloak>
                
            <div class="scroll-content" v-scroll-lock="modalShow">

                <!-- Back button -->
                <div class="eventbrite-modal-back" v-show="modalAddBackBtn == true" v-if="feed.popup">
                    <button type="button" @click="backBtnClick" :title="translations.back_button_text">
                        <i>
                            <svg viewBox="0 0 24 24">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M4 12l8 8 1.5-1.5L8 13h12v-2H8l5.5-5.5L12 4z"></path>
                            </svg>
                        </i>
                    </button>
                </div>

                <!-- Close modal button -->
                <div class="eventbrite-modal-close">
                    <button type="button" @click="(modalShow = false) && (requestModal = false)" :title="translations.close_button_text">
                        <i>
                            <svg viewBox="0 0 24 24">
                                <path d="M13.4 12l3.5-3.5-1.4-1.4-3.5 3.5-3.5-3.5-1.4 1.4 3.5 3.5-3.5 3.5 1.4 1.4 3.5-3.5 3.5 3.5 1.4-1.4z"></path>
                            </svg>
                        </i>
                    </button>
                </div>

                <!-- Image -->
                <div id="eventbrite-modal-img" class="eventbrite-modal-img">
                    <img v-show="modalImage !== false" :src="modalImage" :alt="modalEvent.post_title" width="800" height="400" loading="lazy">

                    <!-- Add to calendar button -->
                    <div class="eventbrite-modal-add-to-calendar-wrapper" v-if="(feed.calendar_button == true) && (license == true && free != true)">
                        <button type="button" class="eventbrite-modal-add-to-calendar" @click="toggleModalCalendarAdd">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <span>{{ translations.add_to_calendar_text }}</span>
                        </button>
                        <div class="eventbrite-modal-add-to-calendar-menu" v-show="modalCalendarAdd">
                            <button type="button" class="eventbrite-modal-add-to-calendar-menu-close" @click="modalCalendarAdd = false">
                                <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                                viewBox="0 0 64 64" style="enable-background:new 0 0 64 64;" xml:space="preserve">
                                    <path d="M35.4,32l19.9-19.9c1-1,1-2.4,0-3.4s-2.4-1-3.4,0L32,28.6L12,8.8c-0.9-1-2.4-1-3.3,0s-1,2.4,0,3.4L28.6,32L8.7,51.9
                                        c-1,1-1,2.4,0,3.4c0.5,0.4,1,0.7,1.7,0.7s1.2-0.2,1.7-0.7l20-19.9l20,19.8c0.5,0.4,1.2,0.7,1.7,0.7c0.5,0,1.2-0.2,1.7-0.7
                                        c1-1,1-2.4,0-3.4L35.4,32z"/>
                                </svg>
                            </button>
                            <a class="eventbrite-modal-add-to-calendar-menu-item eventbrite-modal-add-to-calendar-menu-item-google" href="javascript:;" target="_blank" @click="modalCalendarAdd = false">{{ translations.google_calendar_text }}</a>
                            <a class="eventbrite-modal-add-to-calendar-menu-item eventbrite-modal-add-to-calendar-menu-item-outlook" href="javascript:;" target="_blank" @click="modalCalendarAdd = false">{{ translations.outlook_calendar_text }}</a>
                            <a class="eventbrite-modal-add-to-calendar-menu-item eventbrite-modal-add-to-calendar-menu-item-office" href="javascript:;" target="_blank" @click="modalCalendarAdd = false">{{ translations.office365_calendar_text }}</a>
                            <a class="eventbrite-modal-add-to-calendar-menu-item eventbrite-modal-add-to-calendar-menu-item-yahoo" href="javascript:;" target="_blank" @click="modalCalendarAdd = false">{{ translations.yahoo_calendar_text }}</a>
                            <a class="eventbrite-modal-add-to-calendar-menu-item eventbrite-modal-add-to-calendar-menu-item-apple" href="javascript:;" @click="modalCalendarAdd = false">{{ translations.ics_calendar_text }}</a>
                        </div>
                    </div>
                </div>

                <div class="eventbrite-modal-title" v-show="!modalTickets">
                    
                    <!-- Date and time -->
                    <div class="eventbrite-modal-datetime" v-if="(modalEvent.vue) && (modalEvent.vue.start_full) && (modalEvent.vue.end_full)">
                        <time>
                            {{ modalEvent.vue.start_full }} - {{ modalEvent.vue.end_full }}
                        </time>
                    </div>

                    <!-- Title -->
                    <h3 v-if="modalEvent.post_title">{{ modalEvent.post_title }}</h3>

                    <!-- Location -->
                    <div class="eventbrite-modal-location" :class="{'eventbrite-modal-location-clickable': (modalEvent.venue.address) && (feed.calendar_button == true)}" v-if="(modalEvent.vue) && (modalEvent.vue.location)" @click="modalScrollToMap(modalEvent.vue.location)">
                        <svg version="1.1" class="eventbrite-modal-location-icon" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 48 64" style="enable-background:new 0 0 48 64;" xml:space="preserve" v-if="modalEvent.vue.location !== 'Online'">
                            <g>
                                <path d="M24,0C10.7,0,0,11.2,0,25.3c0,12,16.5,31.7,21.6,37.6c0.5,0.8,1.6,1.1,2.4,1.1c1.1,0,1.9-0.5,2.4-1.1 C31.5,57.1,48,37.1,48,25.3C48,11.2,37.3,0,24,0z M24,57.6C14.9,46.9,5.3,32.8,5.3,25.3c0-11.2,8.3-20,18.7-20s18.7,9.1,18.7,20 C42.7,32.8,33.1,46.9,24,57.6z"/>
                                <path d="M24,13.3c-5.9,0-10.7,4.8-10.7,10.7S18.1,34.7,24,34.7S34.7,29.9,34.7,24S29.9,13.3,24,13.3z M24,29.3 c-2.9,0-5.3-2.4-5.3-5.3s2.4-5.3,5.3-5.3s5.3,2.4,5.3,5.3S26.9,29.3,24,29.3z"/>
                            </g>
                        </svg>
                        <svg version="1.1" class="eventbrite-modal-location-icon-online" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 64.1 48" style="enable-background:new 0 0 64.1 48;" xml:space="preserve" v-if="modalEvent.vue.location == 'Online'">
                            <g>
                                <path d="M32,13.9c-3.5,0-6.4,2.9-6.4,6.1c0,2.4,1.6,4.5,3.7,5.6v19.7c0,1.6,1.1,2.7,2.7,2.7s2.7-1.1,2.7-2.7V25.9 c2.1-1.1,3.7-3.2,3.7-5.6C38.4,16.8,35.5,13.9,32,13.9z M32,19.2c0.5,0,1.1,0.5,1.1,0.8s-0.5,0.8-1.1,0.8c-0.5,0-1.1-0.5-1.1-0.8 S31.5,19.2,32,19.2z"/>
                                <path d="M24.5,10.9c-1.1-1.1-2.7-1.1-3.7,0c-2.4,2.4-3.5,5.3-3.5,8.5s1.3,6.4,3.5,8.8c0.5,0.5,1.3,0.8,1.9,0.8 c0.8,0,1.3-0.3,1.9-0.8c1.1-1.1,1.1-2.7,0-3.7c-1.3-1.3-2.1-2.9-2.1-4.8s0.8-3.5,2.1-4.8C25.6,13.6,25.6,12,24.5,10.9z"/>
                                <path d="M43.2,10.9c-1.1-1.1-2.7-1.1-3.7,0c-1.1,1.1-1.1,2.7,0,3.7c1.3,1.3,2.1,2.9,2.1,4.8s-0.8,3.5-2.1,4.8 c-1.1,1.1-1.1,2.7,0,3.7c0.5,0.5,1.3,0.8,1.9,0.8c0.8,0,1.3-0.3,1.9-0.8c2.4-2.4,3.5-5.3,3.5-8.8C46.9,16.3,45.6,13.1,43.2,10.9z"/>
                                <path d="M13.9,19.5c0-3.7,1.3-7.2,4-9.9c1.1-1.1,1.1-2.7,0-3.7c-1.1-1.1-2.7-1.1-3.7,0c-3.7,3.7-5.6,8.5-5.6,13.6s2.1,9.9,5.6,13.6 c0.5,0.5,1.1,0.8,1.9,0.8s1.3-0.3,1.9-0.8c1.1-1.1,1.1-2.7,0-3.7C15.5,26.7,13.9,23.2,13.9,19.5z"/>
                                <path d="M49.9,5.9c-1.1-1.1-2.7-1.1-3.7,0s-1.1,2.7,0,3.7c2.7,2.7,4,6.1,4,9.9s-1.3,7.2-4,9.9c-1.1,1.1-1.1,2.7,0,3.7 c0.5,0.5,1.3,0.8,1.9,0.8c0.8,0,1.3-0.3,1.9-0.8c3.7-3.7,5.6-8.5,5.6-13.6C55.5,14.4,53.3,9.3,49.9,5.9z"/>
                                <path d="M5.3,19.5c0-5.6,2.1-10.9,6.1-14.9c1.1-1.1,1.1-2.7,0-3.7s-2.7-1.1-3.7,0C2.7,5.9,0,12.3,0,19.5s2.7,13.6,7.7,18.7 C8.2,38.7,8.8,39,9.6,39c0.8,0,1.3-0.3,1.9-0.8c1.1-1.1,1.1-2.7,0-3.7C7.5,30.4,5.3,25.1,5.3,19.5z"/>
                                <path d="M56.3,0.8c-1.1-1.1-2.7-1.1-3.7,0s-1.1,2.7,0,3.7c4,4,6.1,9.3,6.1,14.9s-2.1,10.9-6.1,14.9c-1.1,1.1-1.1,2.7,0,3.7 c0.5,0.5,1.3,0.8,1.9,0.8c0.8,0,1.3-0.3,1.9-0.8c5.1-5.1,7.7-11.7,7.7-18.7S61.3,5.9,56.3,0.8z"/>
                            </g>
                        </svg>
                        <span>{{ modalEvent.vue.location }}</span>
                    </div>

                </div>

                <div class="eventbrite-modal-content">
                    
                    <div class="replace-content">

                        <!-- Event information screen -->
                        <div class="eventbrite-modal-event-info" v-show="!modalTickets">

                            <!-- Summary -->
                            <p class="eventbrite-modal-summary" v-if="(modalEvent.vue) && (modalEvent.post_content)">{{ modalEvent.post_content }}</p>

                            <!-- Description modules -->
                            <div v-for="description in modalDescription" class="eventbrite-modal-description-module" :class="{'eventbrite-modal-description-module-text': description.data.body, 'eventbrite-modal-description-module-img': description.data.image, 'eventbrite-modal-description-module-video': description.data.video}" v-html="moduleContent( description )" :key="description.ID"></div>

                            <div class="eventbrite-modal-description-module eventbrite-modal-description-module-text eventbrite-modal-description-error" v-if="modalError == true">
                                <p>{{ translations.modal_error_text }} <br><a :href="modalEvent.url" :target="(feed.link_target_blank == true) ? '_blank' : '_self'">{{ translations.modal_error_link }}</a></p>
                            </div>

                        </div>

                        <!-- Event checkout screen -->
                        <div class="eventbrite-modal-event-tickets" v-show="modalTickets">

                            <!-- Modal content spinner -->
                            <div class="eventbrite-modal-content-spinner-wrapper">
                                <div class="eventbrite-modal-spinner">
                                    <div class="double-bounce1"></div>
                                    <div class="double-bounce2"></div>
                                </div>
                            </div>

                            <!-- Container for Eventbrite checkout data -->
                            <div id="eventbrite-modal-event-tickets" style="position: relative; background-color: #fff; z-index: 1;"></div>

                        </div>

                    </div>

                </div>

                <!-- Map -->
                <div id="eventbrite-modal-map" class="eventbrite-modal-map" v-show="!modalTickets" v-if="(modalEvent.vue) && (modalEvent.venue.address) && (feed.google_map == true)">
                    <iframe width="100%" height="250" frameborder="0" scrolling="no" marginheight="0" marginwidth="0" :src="googleMapsSrc( this.modalEvent.vue.location )" loading="lazy"></iframe>
                </div>

                <!-- Organizer -->
                <div class="eventbrite-modal-organizer" v-show="!modalTickets" v-if="(modalEvent.organizer) && (modalEvent.organizer.name) && (feed.organizer_info == true)">
                    <div class="eventbrite-modal-organizer-row">
                        <div class="eventbrite-modal-organizer-image" v-if="(modalOrganizer) && (modalOrganizer.logo) && (modalOrganizer.logo.url)">
                            <img :src="modalOrganizer.logo.url" alt="modalOrganizer.name">
                        </div>
                        <div class="eventbrite-modal-organizer-info">
                            <div class="eventbrite-modal-organizer-name" v-if="modalEvent.organizer.name">
                                <span>{{ modalEvent.organizer.name }}</span>
                            </div>
                            <div class="eventbrite-modal-organizer-title">{{ translations.organizer_title }}</div>
                            <div class="eventbrite-modal-organizer-description" v-if="modalEvent.organizer.description" v-html="modalEvent.organizer.description.html"></div>
                            <div class="eventbrite-modal-organizer-social" v-if="modalOrganizer">
                                <a :href="'https://www.facebook.com/' + modalOrganizer.facebook" v-if="modalOrganizer.facebook" :target="(feed.link_target_blank == true) ? '_blank' : '_self'" class="eventbrite-modal-organizer-social-twitter" title="Facebook">
                                    <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" data-prefix="fab" data-icon="facebook-f" class="svg-inline--fa fa-facebook-f fa-w-10" role="img" viewBox="0 0 320 512"><path fill="currentColor" d="M279.14 288l14.22-92.66h-88.91v-60.13c0-25.35 12.42-50.06 52.24-50.06h40.42V6.26S260.43 0 225.36 0c-73.22 0-121.08 44.38-121.08 124.72v70.62H22.89V288h81.39v224h100.17V288z"/></svg>
                                </a>
                                <a :href="'https://www.twitter.com/' + modalOrganizer.twitter" v-if="modalOrganizer.twitter" :target="(feed.link_target_blank == true) ? '_blank' : '_self'" class="eventbrite-modal-organizer-social-website" title="Twitter">
                                    <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" data-prefix="fab" data-icon="twitter" class="svg-inline--fa fa-twitter fa-w-16" role="img" viewBox="0 0 512 512"><path fill="currentColor" d="M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z"/></svg>
                                </a>
                                <a :href="modalEvent.organizer.website" v-if="modalEvent.organizer.website" :target="(feed.link_target_blank == true) ? '_blank' : '_self'" class="eventbrite-modal-organizer-social-facebook" :title="translations.organizer_link">
                                    <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" data-prefix="far" data-icon="globe" class="svg-inline--fa fa-globe fa-w-16" role="img" viewBox="0 0 496 512"><path fill="currentColor" d="M248 8C111 8 0 119 0 256s111 248 248 248 248-111 248-248S385 8 248 8zm179.3 160h-67.2c-6.7-36.5-17.5-68.8-31.2-94.7 42.9 19 77.7 52.7 98.4 94.7zM248 56c18.6 0 48.6 41.2 63.2 112H184.8C199.4 97.2 229.4 56 248 56zM48 256c0-13.7 1.4-27.1 4-40h77.7c-1 13.1-1.7 26.3-1.7 40s.7 26.9 1.7 40H52c-2.6-12.9-4-26.3-4-40zm20.7 88h67.2c6.7 36.5 17.5 68.8 31.2 94.7-42.9-19-77.7-52.7-98.4-94.7zm67.2-176H68.7c20.7-42 55.5-75.7 98.4-94.7-13.7 25.9-24.5 58.2-31.2 94.7zM248 456c-18.6 0-48.6-41.2-63.2-112h126.5c-14.7 70.8-44.7 112-63.3 112zm70.1-160H177.9c-1.1-12.8-1.9-26-1.9-40s.8-27.2 1.9-40h140.3c1.1 12.8 1.9 26 1.9 40s-.9 27.2-2 40zm10.8 142.7c13.7-25.9 24.4-58.2 31.2-94.7h67.2c-20.7 42-55.5 75.7-98.4 94.7zM366.3 296c1-13.1 1.7-26.3 1.7-40s-.7-26.9-1.7-40H444c2.6 12.9 4 26.3 4 40s-1.4 27.1-4 40h-77.7z"/></svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <div class="noscroll-content" v-show="!modalTickets">
                <div class="eventbrite-modal-footer-left" v-if="feed.eventbrite_link == true">
                    <a class="eventbrite-modal-external-button" :href="modalEvent.url" :target="(feed.link_target_blank == true) ? '_blank' : '_self'"><span class="desktop">{{ translations.eventbrite_link }} </span><span class="mobile">Eventbrite</span></a>
                </div>
                <div class="eventbrite-modal-footer-right">
                    <div class="eventbrite-modal-price" v-if="(modalEvent.vue) && (modalEvent.vue.price) && feed.display_price" v-html="modalEvent.vue.price"></div>
                    <button class="eventbrite-modal-checkout-button" v-if="(modalEvent) && (modalEvent.event_sales_status.sales_status == 'on_sale')" @click="toOrder(modalEvent, modalEventIndex)">
                        {{ checkoutButtonText( modalEvent ) }}
                    </button>
                </div>
            </div>

        </Modal>
        
        <!-- Modal spinner -->
        <div :class="'eventbrite-modal-spinner-wrapper eventbrite-modal-spinner-wrapper-' + uid">
            <div class="eventbrite-modal-spinner">
                <div class="double-bounce1"></div>
                <div class="double-bounce2"></div>
            </div>
        </div>

    </div>

</template>
	
<script>

    // Vue Modal
    import VueModal from '@kouts/vue-modal';
    
    // Import V-scroll-lock
    import VScrollLock from 'v-scroll-lock';

    // Calendar links
    import { google, outlook, office365, yahoo, ics } from 'calendar-link';

    // Axios
    import axios from 'axios';

    var source;

	export default {
        name: 'Cards',
        props: ['initialUid', 'initialFeed', 'initialSettings', 'initialApi'],
        data: function() {
            return  {

                // Get user data from WP admin
                uid: this.initialUid,
                feed: this.initialFeed,
                settings: this.initialSettings,
                api: this.initialApi,
                admin: (EventFeedForEventbrite.admin === 'true'),
                license: (EventFeedForEventbrite.premium === 'true'),
                free: (EventFeedForEventbrite.free === 'true'),
                translations: EventFeedForEventbriteAppTranslations,
                edit_url: EventFeedForEventbrite.admin_url + 'post.php?post=' + this.initialFeed.ID + '&action=edit',

                // Prepare variable for events
                events: {},
                
                // If events data has been loaded from Eventbrite API
                feedError: false,
                feedLoaded: false,
                
                // Modal
                modalEventIndex: false,
                modalShow: false,
                modalImage: false,
                modalEvent: false,
                modalTickets: false,
                modalDescription: false,
                modalOrganizer: false,
                modalState: false,
                modalCheckoutLoaded: false,
                modalAddBackBtn: false,
                modalError: false,
                modalCalendarAdd: false,

                // Scroll
                windowTop: 0

            }
        },
        components: {
		    'Modal': VueModal,
            'VScrollLock': VScrollLock
	    },
        methods: {

            // Fires before modal is opened
            beforeOpen: function() {

                // Resets error variable
                this.modalError = false;

                // Sets data to waiting state and turn on modal
                this.modalState = 'waitingForEventData';

                if( source ) { source.cancel(); }
                const CancelToken = axios.CancelToken;
                source = CancelToken.source();
                
                if( ( this.feed.popup == false ) || ( this.license == false || this.free == true ) ) {

                    // Loads HQ image
                    if( this.modalEvent.vue.has_image == true ) {

                        axios.get( this.api + 'event-feed-for-eventbrite/v1/image/' + this.modalEvent.logo.id, { cancelToken: source.token } )
                        .then( response => {

                            this.modalImage = response.data.url;
                            this.$nextTick( function() {

                                // Check if user didn't close the modal before loading
                                if( this.modalShow == true ) {
                                    this.modalState = 'EventDataReady';	
                                } else {
                                    this.modalState = false;
                                }
                            })

                        })
                        .catch( errors => {
                            
                            // Request cancelled by user
                            if ( axios.isCancel(errors) ) {
                                this.manuallyCloseModal();

                            // Error
                            } else {
                                this.modalError = true;
                                this.modalImage = this.modalEvent.vue.image;

                                this.$nextTick( function() {

                                    // Check if user didn't close the modal before loading
                                    if( this.modalShow == true ) {
                                        this.modalState = 'EventDataReady';	
                                    } else {
                                        this.modalState = false;
                                    }
                                    
                                })
                            }

                        })

                    } else {

                        this.modalImage = this.modalEvent.vue.image;

                        // Check if user didn't close the modal before loading
                        if( this.modalShow == true ) {
                            this.modalState = 'EventDataReady';	
                        } else {
                            this.modalState = false;
                        }

                    }

                } else {

                    // Loads modal data
                    if( this.modalEvent.vue.has_image == true ) {

                        axios.get( this.api + 'event-feed-for-eventbrite/v1/details_image/' + this.modalEvent.ID + '/' + this.modalEvent.logo.id + '/' + this.modalEvent.organizer.id, { cancelToken: source.token } )
                        .then( response => {
                            this.modalImage = response.data.media;
                            this.modalDescription = response.data.description.modules;
                            this.modalOrganizer = response.data.organizer;
                            this.$nextTick( function() {
                                
                                // Check if user didn't close the modal before loading
                                if( this.modalShow == true ) {
                                    this.modalState = 'EventDataReady';	
                                } else {
                                    this.modalState = false;
                                }

                            })
                        })

                        // Catch errors
                        .catch( errors => {

                            // Request cancelled by user
                            if ( axios.isCancel(errors) ) {
                                this.manuallyCloseModal();

                            // Error
                            } else {
                                this.modalError = true;
                                this.modalImage = this.modalEvent.vue.image;
                                this.modalDescription = false;
                                this.modalOrganizer = false;

                                this.$nextTick( function() {

                                    // Check if user didn't close the modal before loading
                                    if( this.modalShow == true ) {
                                        this.modalState = 'EventDataReady';	
                                    } else {
                                        this.modalState = false;
                                    }
                                    
                                })
                            }
                        })

                        // Generate calendar links
                        if( this.feed.calendar_button == true && this.license == true && this.free != true ) {
                            this.generateAddToCalendarLinks( this.modalEvent );
                        }

                    } else {

                        axios.get( this.api + 'event-feed-for-eventbrite/v1/details/' + this.modalEvent.ID + '/' + this.modalEvent.organizer.id, { cancelToken: source.token } )
                        .then( response => {
                            this.modalImage = this.modalEvent.vue.image;
                            this.modalDescription = response.data.description.modules;
                            this.modalOrganizer = response.data.organizer;
                            this.$nextTick( function() {

                                // Check if user didn't close the modal before loading
                                if( this.modalShow == true ) {
                                    this.modalState = 'EventDataReady';
                                } else {
                                    this.modalState = false;
                                }

                            })
                        })

                        // Catch errors
                        .catch( errors => {

                            // Request cancelled by user
                            if ( axios.isCancel(errors) ) {
                                this.manuallyCloseModal();

                            // Error
                            } else {
                                this.modalError = true;
                                this.modalImage = this.modalEvent.vue.image;
                                this.modalDescription = false;
                                this.modalOrganizer = false;

                                this.$nextTick( function() {

                                    // Check if user didn't close the modal before loading
                                    if( this.modalShow == true ) {
                                        this.modalState = 'EventDataReady';	
                                    } else {
                                        this.modalState = false;
                                    }
                                    
                                })
                            }
                        })

                        // Generate calendar links
                        if( this.feed.calendar_button == true && this.license == true && this.free != true ) {
                            this.generateAddToCalendarLinks( this.modalEvent );
                        }

                    }

                }

                // Load Eventbrite checkout widget
                if( this.modalTickets == true ) {
                    this.loadEventbriteCheckout();
                }

            },

            // Fires after modal is opened
            afterOpen: function() {

                // Add modal and event ID to URL
                if( this.feed.ID && this.modalEvent && ( this.license == true && this.free != true ) ) {
                    const url = new URL(window.location.href);
                    url.searchParams.set('effe', this.feed.ID);
                    url.searchParams.set('effe-id', this.modalEvent.ID);
                    window.history.pushState({}, '', url.toString());
                }
                
            },

            // Fires before modal is closed
            beforeClose: function() {
                this.requestModal = false;

            },

            // Fires after modal is closed
            afterClose: function() {
                this.modalCleanUp();
                this.modalCalendarAdd = false;

                // Remove modal and event ID from URL
                const url = new URL(window.location.href);
                url.searchParams.delete('effe');
                url.searchParams.delete('effe-id');
                window.history.replaceState({}, '', url.toString());
            },
            
            // Runs after user advances to ticket order
            toOrder: function(event, eventIndex) {
                
                // Popup on website (and is not IE)
                if( this.feed.link_to == 'popup' ) {

                    this.toggleModalContent();
                    this.modalAddBackBtn = true;

                // Popup on Eventbrite
                } else {

                    if( this.feed.link_target_blank == true ) {
                        window.open( event.url + '#tickets' );
                    } else {
                        window.location.href = event.url + '#tickets';
                    }

                }

            },

            // Check clicks outside the modal before modal is fully loaded (will cancel opening of the modal)
            manuallyCloseModal: function( event ) {

                this.modalShow = false;
                this.requestModal = false;
                this.modalAddBackBtn = true;
                this.beforeClose();
                this.afterClose();
                
                var spinnerEl = document.querySelector('.eventbrite-modal-spinner-wrapper-' + this.uid);
                spinnerEl.style.display = 'none';

                source.cancel();

            },

            // When clicked on a event card link
            getEventDetails: function(event, eventIndex) {
                
                // If popup is set, open popup
                if( ( this.feed.popup == true ) && ( this.license == true && this.free != true ) ) {
                    this.modalShow = true;
                    this.modalEvent = event;
                    this.modalEventIndex = eventIndex;
                
                // Else go to event URL
                } else {

                    if( this.feed.link_target_blank == true ) {
                        window.open( event.url );
                    } else {
                        window.location.href = event.url;
                    }
                    
                }
                
            },

            // When clicked on checkout button from event card
            checkoutFromEventCard: function(event, eventIndex) {

                // Popup on website (and is not IE)
                if( this.feed.link_to == 'popup' ) {
                    this.modalShow = true;
                    this.modalEvent = event;
                    this.modalEventIndex = eventIndex;
                    this.toggleModalContent();

                // Popup on Eventbrite
                } else {
                    if( this.feed.link_target_blank == true ) {
                        window.open( event.url + '#tickets' );
                    } else {
                        window.location.href = event.url + '#tickets';
                    }
                }
            },

            // Toggles modal content after
            toggleModalContent: function() {
                this.modalTickets = !this.modalTickets;
                if( ( this.modalTickets == true ) && ( this.modalCheckoutLoaded == false ) ) {
                    this.modalState = 'waitingForCheckoutData';
                    this.loadEventbriteCheckout();
                }
            },

            // Set focus to modal element
            setFocusModal: function() {
                document.querySelector('.eventbrite-modal-wrapper').focus();
            },
            
            // Modal back button behavior
            backBtnClick: function() {
                this.toggleModalContent();
                this.$nextTick(this.modalScrollToTop);
                this.$nextTick(this.modalAddBackBtn = false);
            },

            // Clean-up after closing modal
            modalCleanUp: function() {
                this.modalShow = false;
                this.modalTickets = false;
                this.modalImage = false;
                this.modalDescription = false;
                this.modalState = false;
                this.modalCheckoutLoaded = false;
                this.modalAddBackBtn = false;
                this.modalCalendarAdd = false;

            },

            // Load Eventbrite checkout
            loadEventbriteCheckout: function() {
                let widgetAttributes = {
                    widgetType: "checkout",
                    eventId: this.modalEvent.ID,
                    iframeContainerId: "eventbrite-modal-event-tickets",
                };
                window.EBWidgets.createWidget(widgetAttributes);
            },

            // Add parameters to YouTube embed URL
            youtubeUrl: function( url ) {
                var videoUrl = new URL( url );
                videoUrl.searchParams.append( 'enablejsapi', '1' );
                videoUrl.searchParams.append( 'modestbranding', '1' );
                videoUrl.searchParams.append( 'rel', '0' );
                videoUrl.searchParams.append( 'showinfo', '0' );
                videoUrl.searchParams.append( 'showtitle', '0' );
                return videoUrl.toString();
            },

            // Add parameters to Vimeo embed URL
            vimeoUrl: function( url ) {
                var videoUrl = new URL( url );
                videoUrl.searchParams.append( 'title', '0' );
                videoUrl.searchParams.append( 'byline', '0' );
                videoUrl.searchParams.append( 'portrait', '0' );
                return videoUrl.toString();
            },

            // Get Google maps embed source URL
            googleMapsSrc: function( address ) {
                var url = 'https://maps.google.com/?q=' + address + '&output=embed';
                return url;
            },

            // Description modules
            moduleContent: function( description ) {

                // Text module
                if( description.data.body ) {
                    return description.data.body.text;

                // Image module
                } else if( description.data.image ) {
                    return '<img src="' + description.data.image.url + '" alt="" loading="lazy">';

                // Video module
                } else if( description.data.video ) {

                    // YouTube
                    if( description.data.video.embed_url.includes( 'youtube' ) ) {
                        return '<div class="iframe-container"><iframe width="640" height="360" src="' + this.youtubeUrl( description.data.video.embed_url ) + '" frameborder="0" loading="lazy"></iframe></div>';
                    
                    // Vimeo
                    } else if( description.data.video.embed_url.includes( 'vimeo' ) ) {
                        return '<div class="iframe-container"><iframe width="640" height="360" src="' + this.vimeoUrl( description.data.video.embed_url ) + '" frameborder="0" allow="autoplay; fullscreen; picture-in-picture" loading="lazy"></iframe></div>';
                    }
                }

            },

            // Scroll to modal map
            modalScrollToMap: function( address ) {
                var map = document.querySelector( '#eventbrite-modal-map' );
                if ( typeof(map) != 'undefined' && map != null ) {
                    map.scrollIntoView( { behavior: 'smooth' } );
                } else if( address !== 'Online' ) {
                    var googleMapUrl = 'https://www.google.com/maps/place/' + address;
                    if( this.feed.link_target_blank == true ) {
                        window.open( googleMapUrl );
                    } else {
                        window.location = googleMapUrl;
                    }
                }
            },

            // Scroll to top of the modal info screen
            modalScrollToTop: function() {
                var image = document.querySelector( '#eventbrite-modal-img' );
                if ( typeof(image) != 'undefined' && image != null ) {
                    image.scrollIntoView( { behavior: 'auto' } );
                }
            },
            
            // Toggle modal add to calendar menu
            toggleModalCalendarAdd: function() {
                this.modalCalendarAdd = !this.modalCalendarAdd;
                if( this.modalCalendarAdd == true ) {
                    window.addEventListener( 'click', this.autoCloseCalendarMenu );
                } else {
                    window.removeEventListener( 'click', this.autoCloseCalendarMenu );
                }
            },

            // Automatically close add to calendar menu on click outside the wrapper
            autoCloseCalendarMenu: function( event ) {
                if( ! event.target.closest('.eventbrite-modal-add-to-calendar-wrapper') ) {
                    this.modalCalendarAdd = false;
				}
            },

            // Generate add to calendar links
            generateAddToCalendarLinks: function( event ) {
                const AddToCalendarEvent = {
                    title: event.post_title,
                    description: event.post_content,
                    start: event.start.utc,
                    end: event.end.utc,
                    location: event.vue.location
                };
                document.querySelector( '.eventbrite-modal-add-to-calendar-menu-item-google' ).setAttribute( 'href', google( AddToCalendarEvent ) ) ;
                document.querySelector( '.eventbrite-modal-add-to-calendar-menu-item-outlook' ).setAttribute( 'href', outlook( AddToCalendarEvent ) ) ;
                document.querySelector( '.eventbrite-modal-add-to-calendar-menu-item-office' ).setAttribute( 'href', office365( AddToCalendarEvent ) ) ;
                document.querySelector( '.eventbrite-modal-add-to-calendar-menu-item-yahoo' ).setAttribute( 'href', yahoo( AddToCalendarEvent ) ) ;
                document.querySelector( '.eventbrite-modal-add-to-calendar-menu-item-apple' ).setAttribute( 'href', ics( AddToCalendarEvent ) ) ;
            },

            // Check top scroll position (for WP admin bar modal shift)
            onScroll: function( event ) {
                this.windowTop = window.top.scrollY;
                this.checkScrollPosition();
            },
            checkScrollPosition: function() {
                if( this.windowTop == 0 ) {
                    document.querySelector( 'body' ).classList.add('eventbrite-scroll-top');
                } else {
                    document.querySelector( 'body' ).classList.remove('eventbrite-scroll-top');
                }
            },

            // Remaining tickets text
            ticketsText: function( text ) {
                var textNew = '';
                if( text !== '' ) {
                    var textNew = ' ' + text;
                }
                return textNew;
            },

            // Checkout button text
            checkoutButtonText: function( event ) {
                var buttonText;
                if( event.is_free == true ) {
                    buttonText = this.feed.signup_button_text;
                } else {
                    buttonText = this.feed.tickets_button_text;
                }
                return buttonText;
            }
                        
        },
        watch: {

            // Handling modal states
            modalState: {
                handler: function(val, oldVal) {
                    
                    var modalEl = document.querySelector('#eventbrite-modal-container'); 
                    var contentEl = document.querySelector('.eventbrite-modal-wrapper');
                    var spinnerEl = document.querySelector('.eventbrite-modal-spinner-wrapper-' + this.uid);

                    // Modal is opened but waiting for data
                    if( val == 'waitingForEventData' ) {
                        if( contentEl ) { contentEl.style.display = 'none'; }
                        if( spinnerEl ) { spinnerEl.style.display = 'flex'; } 
                    
                    // Data ready, can show the modal window
                    } else if( val == 'EventDataReady' ) {
                        if( contentEl ) { contentEl.style.display = 'flex'; }
                        if( spinnerEl ) { spinnerEl.style.display = 'none'; }
                        this.setFocusModal();
                    
                    // Hide spinner
                    } else if( val == true ) {
                        if( contentEl ) { contentEl.style.display = 'none'; }
                        if( spinnerEl ) { spinnerEl.style.display = 'none'; }
                    }
                    
                    // Order screen
                    if( val == 'orderScreen' ) {
                        if( contentEl ) { modalEl.classList.add('modal-order'); }
                    } else {
                        if( contentEl ) { modalEl.classList.remove('modal-order'); }
                    }

                }
            },

            // Class toggling
            modalTickets: {
                handler: function(val, oldVal) {
                    
                    var modalEl = document.querySelector('#eventbrite-modal-container'); 

                    if( val == true ) {
                        modalEl.classList.add('modal-tickets');
                    } else {
                        modalEl.classList.remove('modal-tickets');
                    }

                }
            }

        },
        mounted() {

            // Get main Eventbrite data
            axios.get( this.api + 'event-feed-for-eventbrite/v1/feed/' + this.feed.ID )
            .then( response => {
                this.events = response.data.events;
                this.feedLoaded = true;
                this.checkScrollPosition();

                // Check if modal is opened from URL
                const url = new URL(window.location.href);
                if( url.searchParams.has('effe') && url.searchParams.has('effe-id') ) {
                    var modalId = url.searchParams.get('effe');
                    if( modalId == this.feed.ID ) {
                        var modalEventId = url.searchParams.get('effe-id');
                        var modalEventIndex = Array.isArray(this.events) ? this.events.findIndex(event => event.ID == modalEventId) : -1;
                        if( ( this.feed.popup == true ) && ( this.license == true && this.free != true ) && ( modalEventIndex !== -1 ) ) {
                            this.modalShow = true;
                            this.modalEvent = this.events[modalEventIndex];
                            this.modalEventIndex = modalEventIndex;
                        } else {
                            // Remove modal and event ID from URL
                            url.searchParams.delete('effe');
                            url.searchParams.delete('effe-id');
                            window.history.replaceState({}, '', url.toString());
                        }
                    }
                }
            })
            .catch( errors => {
                this.feedError = true;
            })

            this.$nextTick(function () {

                // Listen to Eventbrite widget events
                window.addEventListener('message', (event) => {

                    // Remove widget transitions and set height to 100% on any event
                    if ( event.origin == "https://www.eventbrite.com") {

                        var tickets = document.querySelector('#eventbrite-modal-event-tickets');

                        tickets.style.height = '100%';
                        tickets.style.transition = 'none';
                    }

                    // Widget rendered event
                    if( ( event.origin == "https://www.eventbrite.com" ) && ( event.data.messageName == ( 'widgetRenderComplete' ) ) ) {
                        
                        // Bug fix - rule will apply only for current modal
                        if( this.modalShow == true) {
                            this.modalCheckoutLoaded = true;
                        }
                    }
                    
                    // Tickets screen (if coming from event details and back from ticket order)
                    if ( ( event.origin == "https://www.eventbrite.com" ) && ( ( event.data.messageName == 'widgetRenderComplete' ) || ( event.data.messageName == 'backToTicketSelection' ) ) ) {

                        // Bug fix - rule will apply only for current modal
                        if( this.modalShow == true) {

                            // Count with the Eventbrite delay
                            if( event.data.messageName == 'backToTicketSelection' ) {

                                setTimeout(() => this.modalState = 'ticketsScreen', 810);

                                // Add back button
                                if( ( this.feed.popup == true ) && ( this.license == true && this.free != true ) ) {
                                    setTimeout(() => this.modalAddBackBtn = true, 810);
                                }

                            } else {

                                this.modalState = 'ticketsScreen';

                                // Add back button
                                if( ( this.feed.popup == true ) && ( this.license == true && this.free != true ) ) {
                                    this.modalAddBackBtn = true;
                                }

                            }

                        }

                    }

                    // Order start event - contact information and payment
                    else if( ( event.origin == "https://www.eventbrite.com" ) && (event.data.messageName == 'orderStart' ) ) {
                        
                        setTimeout(() => this.modalState = 'orderScreen', 810);

                        // Remove back button
                        setTimeout(() => this.modalAddBackBtn = false, 810);

                    }

                });

                // Add scroll event listener
                window.addEventListener( 'scroll', this.onScroll );

                // Add click event listener to spinner (when loading the feed)
                var spinner =  this.$el.querySelector('.eventbrite-modal-spinner-wrapper');
                spinner.addEventListener( 'click', this.manuallyCloseModal );

            });

        },
        beforeDestroy() {

            // Remove scroll event listener
            window.removeEventListener( 'scroll', this.onScroll );

            // Remove click event listener
            window.removeEventListener( 'click', this.manuallyCloseModal );

        },
	}

</script>