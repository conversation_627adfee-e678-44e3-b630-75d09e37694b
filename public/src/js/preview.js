( function( $ ) {
	'use strict';

    // Copy shortcode functionality
    if( $('.event-feed-for-eventbrite-tooltip').length > 0 ) {
        $('.event-feed-for-eventbrite-tooltip').each( function(index) {

            // Copy shortcode tooltip
            $(this).tooltipster({
                side: 'bottom',
                theme: 'tooltipster-borderless',
                trigger: 'custom',
                triggerOpen: {
                    mouseenter: true
                },
                triggerClose: {
                    mouseleave: true
                },
                content: EventFeedForEventbritePreviewTranslations.copy_text,
                delay: 0,
            });

            new ClipboardJS(this);

            // On click destroy original tooltip and create new with 'Copied' text -> open it
            $(this).on('click', function() {
                $(this).tooltipster('content', EventFeedForEventbritePreviewTranslations.copied_text);
            });

            // On mouse leave recreate original tooltip
            $(this).on('mouseleave', function() {
                $(this).tooltipster('content', EventFeedForEventbritePreviewTranslations.copy_text);
            });
        
        });
    }

})( jQuery );