{"name": "event-feed-for-eventbrite", "version": "1.0.0", "main": "index.js", "scripts": {"build": "webpack --mode production", "start": "webpack watch --mode development"}, "author": "Bohemia Plugins", "license": "MIT", "dependencies": {"@claviska/jquery-minicolors": "^2.3.5", "@kouts/vue-modal": "^2.0.10", "@vue/babel-preset-app": "^4.5.13", "calendar-link": "^2.2.0", "choices.js": "^9.0.1", "clipboard": "^2.0.8", "font-awesome": "^4.7.0", "jquery-confirm": "^3.3.4", "jquery.are-you-sure": "^1.9.0", "tooltipster": "^4.2.8", "v-scroll-lock": "^1.3.1", "vue": "^2.6.14"}, "dependenciesComments": {"@claviska/jquery-minicolors": "Color picker used in admin", "@kouts/vue-modal": "Modal solution for Vue app", "@vue/babel-preset-app": "The default Babel preset used in all Vue CLI projects", "calendar-link": "For generating export links to various calendar solutions", "choices.js": "Select input used in admin", "clipboard": "For easy copying shortcode to clipboard", "font-awesome": "Icon font used in admin", "jquery-confirm": "A multipurpose plugin for alert, confirm & dialog used in admin", "tooltipster": "Tooltips used in admin", "v-scroll-lock": "Vue plugin for disabling body scroll while modal is opened", "vue": "Vue.js framework used on front-end"}, "devDependencies": {"@babel/core": "^7.14.3", "@babel/preset-env": "^7.14.2", "autoprefixer": "^10.2.5", "babel-loader": "^8.2.2", "browser-sync": "^2.26.14", "browser-sync-webpack-plugin": "^2.3.0", "clean-webpack-plugin": "^3.0.0", "copy-webpack-plugin": "^8.1.1", "css-loader": "^5.2.5", "css-minimizer-webpack-plugin": "^3.0.0", "del": "^6.0.0", "file-loader": "^6.2.0", "gulp": "^4.0.2", "gulp-remove-code": "^3.0.4", "gulp-replace": "^1.1.3", "gulp-wp-pot": "^2.5.0", "html-webpack-plugin": "^5.3.1", "mini-css-extract-plugin": "^1.6.0", "postcss": "^8.3.0", "postcss-loader": "^5.3.0", "postcss-preset-env": "^6.7.0", "sass": "^1.33.0", "sass-loader": "^11.1.1", "style-loader": "^2.0.0", "terser-webpack-plugin": "^5.1.4", "vue-loader": "^15.9.7", "vue-template-compiler": "^2.6.12", "webpack": "^5.37.1", "webpack-cli": "^4.7.2", "webpack-dev-server": "^3.11.2", "webpack-livereload-plugin": "^3.0.1"}, "devDependenciesComments": {}, "browserslist": ["> 1%", "IE 11"]}