/**
 * General admin styles
 */
 .wrap.eventbrite_plugin_options,
 .event-feed-for-eventbrite-metabox-content {

    // Alert
    .alert {
        background-color: #fff;
        border: 1px solid #c3c4c7;
        border-left-color: #d63638;
        border-left-width: 4px;
        box-shadow: 0 1px 1px rgba(0, 0, 0, .04);
        margin: 15px 0 0;
        padding: 1px 12px;
        p {
            margin: .5em 0;
            padding: 2px;
        }
    }
 
    // Minicolors
    .minicolors {
        width: 100%;
        .color-field {
            padding-left: 35px;
        }
        .minicolors-input-swatch {
            box-sizing: border-box;
            top: 7px;
            left: 7px;
            height: 21px;
            width: 21px;
            border-radius: 2px;
        }
        .minicolors-swatch-color {
            cursor: pointer;
        }
    }

    // Choices JS
    .select-wrapper {
        display: block;
        max-width: 400px;
        margin: 0 1px;
        select {
            display: block;
            box-sizing: border-box;
            width: 100%;
            min-height: 35px;
            margin: 0;
        }
        .choices * {
            box-sizing: border-box;
        }
        .choices__list--dropdown.is-active {
            border-color: #2271b1;
            border-top-color: #b3b3b3;
            border-bottom: none;
            box-shadow: 0 1px 0 1px #2271b1;
        }
        .choices__inner {
            min-height: 35px;
            background-color: #fff;
            padding: 3px 6px;
            border: 1px solid #8c8f94;
            border-radius: 3px;
            &:focus {
                border-color: #2271b1;
                box-shadow: 0 0 0 1px #2271b1;
            }
        }
        .choices__list-single {
            display: inline-block;
            vertical-align: baseline;
            width: 100%;
            padding: 4px 16px 4px 4px;
            font-size: 0.875em;
        }
        .choices__list--dropdown .choices__item--selectable.is-selected {
            background-color: #0d99d5;
            color: #fff;
        }
        .choices {
            font-size: 16px;
            &.is-open {
                .choices__inner {
                    border-color: #2271b1;
                    box-shadow: 0 0 0 1px #2271b1;
                    border-bottom-left-radius: 0;
                    border-bottom-right-radius: 0;
                }
            }
            &.is-open.is-flipped {
                .choices__inner {
                    border-color: #2271b1;
                    box-shadow: 0 0 0 1px #2271b1;
                    border-top-left-radius: 0;
                    border-top-right-radius: 0;
                    border-bottom-left-radius: 3px;
                    border-bottom-right-radius: 3px;
                }
                .choices__list--dropdown {
                    border-top: none;
                    border-top-color: #b3b3b3;
                    border-bottom: 1px solid #8c8f94;
                    box-shadow: 0 -1px 0 1px #2271b1;
                }
            }
            &.is-flipped {
                top: auto;
                bottom: 100%;
                margin-top: 0;
                border-radius: .25rem .25rem 0 0;
            }
            &.is-disabled {
                opacity: .7;
                color: #32373c;
                .choices__inner {
                    background-color: #f6f7f7;
                }
                .choices__placeholder {
                    opacity: 1;
                }
                select, .choices__item, .choices__inner {
                    cursor: pointer;
                }
            }
        }
        .choices__list--dropdown {
            .choices__input {
                border-radius: 0;
                border-color: #b3b3b3;
                border-style: solid;
                border-bottom-width: 1px;
                border-top-width: 0;
                border-left-width: 0;
                border-right-width: 0;
                outline: none;
                box-shadow: none;
                box-sizing: border-box;
            }
        }
    }

    // True / False switch
    .event-feed-for-eventbrite-switch-input {
        opacity: 0;
        position: absolute;
        margin: 0;
    }
    .event-feed-for-eventbrite-switch {
        display: inline-block;
        border-radius: 5px;
        cursor: pointer;
        position: relative;
        background: #f5f5f5;
        height: 30px;
        vertical-align: middle;
        border: #7e8993 solid 1px;
        transition: background 0.25s ease;
        user-select: none;
        &:hover,
        &.-focus {
            border-color: #0071a1;
            background: #f3f5f6;
            color: #016087;
            .event-feed-for-eventbrite-slider {
                border-color: #0071a1;
            }
        }
        &.-on {
            background: #0d99d5;
            border-color: #007cba;
            .event-feed-for-eventbrite-slider {
                left: 50%;
                right: 2px;
                border-color: #007cba;
            }
            &:hover {
                background: #0d99d5;
                border-color: #007cba;
            }
        }
        span {
            float: left;
            text-align: center;
            font-size: 13px;
            line-height: 21px;
            padding: 4px 10px;
            min-width: 15px;
        }
        .event-feed-for-eventbrite-switch-on {
            color: #fff;
            text-shadow: #007cba 0 1px 0;
        }
        .event-feed-for-eventbrite-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            bottom: 2px;
            right: 50%;
            background: #fff;
            border-radius: 3px;
            border: #7e8993 solid 1px;
            transition: all 0.25s ease;
            transition-property: left, right;
        }
    }

    // Placeholder image
    .placeholder-background-image-wrapper {
        position: relative;
        width: 100%;
        max-width: 400px;
        height: auto;
        margin: 5px 0 10px;
        border-radius: 4px;
        overflow: hidden;
        .close {
            appearance: none;
            display: inline-block;
            border: none;
            padding: 0;
            margin: 0;
            text-decoration: none;
            font-size: 0;
            text-align: center;
            cursor: pointer;
            display: none;
            font-weight: 500;
            width: 24px;
            height: 24px;
            position: absolute;
            top: 10px;
            right: 10px;
            border-radius: 50%;
            background-color: #fff;
            box-shadow: 0 1px 5px rgba(0, 0, 0, .15);
            svg {
                transition: .2s ease;
                fill: #000;
                &:hover {
                    fill: #007cba;
                } 
            }
        }
        .dashicons {
            color: #fff;
        }
        img {
            width: 100%;
            height: auto;
            display: block;
        }
        &:hover {
            .close {
                display: block;
            }
        }
    }

    // Placeholder image
    .placeholder-image-upload {
        color: #0071a1;
        border-color: #0071a1;
        background: #f3f5f6;
        vertical-align: top;
        display: inline-block;
        text-decoration: none;
        font-size: 13px;
        line-height: 2.15384615;
        min-height: 35px;
        margin: 0;
        padding: 0 10px;
        cursor: pointer;
        border-width: 1px;
        border-style: solid;
        appearance: none;
        border-radius: 3px;
        white-space: nowrap;
        box-sizing: border-box;
        transition-property: border, background, color;
        transition-duration: .05s;
        transition-timing-function: ease-in-out;
    }
    
}

/**
 * Settings page
 */
.wrap.eventbrite_plugin_options {
    h1 {
        margin: 0;
        padding: 9px 0 4px 0;
    }
    .heading-row {
        padding: 20px 0 0;
        line-height: 1.3;
        h2 {
            color: #3c434a;
            font-size: 18px;
            font-weight: 700;
            margin: 0;
        }
        p:first-of-type {
            margin: 5px 0 0;
            font-size: 14px;
            color: #646970;
        }
    }
    .field-row {
        border-bottom: 1px solid #dcdcde;
        padding: 10px 0;
        font-size: 14px;
        line-height: 1.3;
    }
    h2 {
        &.nav-tab-wrapper {
            margin-top: 15px;
            margin-bottom: 20px;
        }
    }
    input[type=text],
    input[type=password] {
        width: 400px;
        min-height: 35px;
        max-width: 100%; 
    }
    .status-badge {
        display: inline-block;
        background-color: #82878C;
        border: 1px solid #7c7f82;
        text-shadow: #7c7f82 0 1px 0;
        color: #fff;
        font-size: 14px;
        border-radius: 4px;
        height: 32px;
        overflow: hidden;
        .status-badge-inner {
            display: flex;
            .dashicons::before {
                font-size: 24px;
            }
            span:first-child {
                height: 32px;
                padding: 0 6px;
                background: rgba(0, 0, 0, 0.15);
                display: flex;
                justify-content: center;
                align-items: center;
            }
            span:last-child {
                height: 32px;
                padding: 0 10px;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }
        &.active {
            background-color: #5cb85c;
            border: 1px solid #429c42;
            text-shadow: #429c42 0 1px 0;
        }
    }
    fieldset {
        input[type=checkbox],
        input[type=radio] {
            margin: -.15rem .25rem 0 7.5px;
            &:first-of-type {
                margin-left: 0;
            }
        }
    }
    .radio-inline {
        margin-top: 5px;
    }
    .description {
        max-width: 800px;
    }
    .event-feed-for-eventbrite-date-settings {
        .date-time-text {
            display: inline-block;
            min-width: 10em;
        }
        input.small-text {
            width: 56px;
            margin: -2px 0;
        }
    }

    // Address Format
    p.address-help {
        margin-top: 20px;
        & > span {
            display: flex;
            span {
                width: 250px;
                margin: .35em 0 .5em!important;
            }
        }
    }

    // Spinner
    .spinner {
        float: none;
        margin: -3px 3px 0;
    }

}

/**
 * Event Feed Metaboxes
 */
.event-feed-for-eventbrite-metabox-content {
    padding-top: 5px;
    span.description {
        color: #666;
        margin-top: 5px;
        display: block;
    }
    .inline-checkbox {
        margin: 2px 0 5px 0;
        display: flex;
        align-items: center;
        input[type=checkbox] {
            margin: 0 7.5px 0 0;
        }
        span.description {
            margin-top: 0;
        }
    }
    .hidden {
        display: none;
    }
    li {
        margin: 15px 0;
        display: flex;
        flex-wrap: wrap;
        & > span {
            box-sizing: border-box;
            display: inline-block;
        }
        & > span:first-of-type {
            width: 200px;
            padding-right: 15px;
            @media (max-width: 782px) {
                width: 100%;
            }
            label {
                font-weight: 600;
                vertical-align: top;
            }
        }
        & > span:last-of-type {
            width: 800px;
            max-width: calc(100% - 200px);
            @media (max-width: 782px) {
                width: 100%;
            }
        }
        input[type=text],
        input[type=number] {
            width: 400px;
            min-height: 35px;
            max-width: 100%; 
        }
    }
    fieldset {
        input[type=checkbox],
        input[type=radio] {
            margin: -.15rem .25rem 0 7.5px;
            &:first-of-type {
                margin-left: 0;
            }
        }
        label {
            vertical-align: top;
        }
        br + input[type=checkbox] {
            margin-left: 0;
        }
        br {
            display: block;
            content: "";
            margin-top: 5px;
        }
    }
    .layout-radio {
        margin-top: 0;
        margin-bottom: -20px;
        display: flex;
        flex-wrap: wrap;
        cursor: pointer;
        span {
            display: flex;
            flex-direction: column;
            margin-right: 30px;
            margin-bottom: 20px;
            margin-top: 2px;
            margin-left: 2px;
        }
        input {
            display: none;
            &:checked + label::before {
                box-shadow: 0 0 0 2px #0071a1;
            }
            &:disabled + label {
                position: relative;
            }
        }
        label {
            position: relative;
            font-size: 14px;
            font-weight: 500;
            display: inline-block;
            margin: 0;
            &::before {
                content: "";
                display: block;
                width: 155px;
                height: 180px;
                margin: 0 0 10px 0;
                border-radius: 3px;
                box-shadow: 0 0 0 2px #ccd0d4;
            }
            .upgrade-tag {
                opacity: 0;
                visibility: hidden;
                font-size: 14px;
                position: absolute;
                display: inline-block;
                padding: 15px 10px;
                border-radius: 3px;
                background-color: #fff;
                top: 85px;
                left: 17.5%;
                width: 65%;
                box-sizing: border-box;
                text-align: center;
                line-height: 1;
                text-decoration: none;
                color: #F56E28;
                box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .15);
                transition: all 0.3s ease;
            }
            &:hover {
                .upgrade-tag {
                    top: 65px;
                    opacity: 1;
                    visibility: visible;
                    &:hover {
                        text-decoration: underline;
                    }
                }
            }
            svg {
                position: relative;
                top: 4px;
                left: 1px;
            }
        }
        .demo-link {
            display: block;
            font-weight: 400;
            font-style: italic;
            text-decoration: none;
            &:hover {
                text-decoration: underline;
            }
            &:focus {
                outline: none;
                text-decoration: underline;
                box-shadow: none;
            }
        }
        label[for=event-feed-for-eventbrite-layout_type_grid] {
            background: url(../../assets/img/layout-grid.png) top left no-repeat;
        }
        label[for=event-feed-for-eventbrite-layout_type_cards] {
            background: url(../../assets/img/layout-cards.png) top left no-repeat;
        }
        label[for=event-feed-for-eventbrite-layout_type_list] {
            background: url(../../assets/img/layout-list.png) top left no-repeat;
        }
        label[for=event-feed-for-eventbrite-layout_type_widget] {
            background: url(../../assets/img/layout-widget.png) top left no-repeat;
        }
        label[for=event-feed-for-eventbrite-layout_type_calendar] {
            background: url(../../assets/img/layout-grid.png) top left no-repeat;
        }
        label[for=event-feed-for-eventbrite-layout_type_slider] {
            background: url(../../assets/img/layout-grid.png) top left no-repeat;
        }
        label[for=event-feed-for-eventbrite-layout_type_featured] {
            background: url(../../assets/img/layout-grid.png) top left no-repeat;
        }
    }

    // Sub label
    .sub-label {
        color: #666;
        font-weight: 400;
    }

}

/**
 * Metaboxes - new style
 */
#event_feed_design,
#event_feed_eventbrite,
#event_feed_card,
#event_feed_popup,
#event_feed_checkout,
#event_feed_advanced,
#event_feed_filters {
    .inside {
        margin: 0;
        padding: 0;
        .event-feed-for-eventbrite-metabox-content {
            padding-top: 0;
        }
    }
    .handle-order-higher, .handle-order-lower {
        display: none;
    }
}
.event-feed-for-eventbrite-metabox-message {
    background: #f9f9f9;
    .event-feed-for-eventbrite-metabox-input {
        width: 100%;
        padding: 15px 12px;
    }
    p {
        color: #646970;
        margin: 0;
    }
}
.event-feed-for-eventbrite-metabox-field {
    display: flex;
    border-top: 1px solid #eee;
    @media (max-width: 1199px) {
        display: block;
        border-top: 1px solid #c3c4c7;
    }
    &:first-child {
        border-top: none;
    }
}
.event-feed-for-eventbrite-metabox-message + .event-feed-for-eventbrite-metabox-field {
    border-top: 1px solid #e1e1e1;
}
.event-feed-for-eventbrite-metabox-label {
    padding: 15px 12px;
    width: 260px;
    box-sizing: border-box;
    background: #f9f9f9;
    border-color: #e1e1e1;
    border-style: solid;
    border-width: 0 1px 0 0;
    @media (max-width: 1199px) {
        width: 100%;
        border-width: 0;
    }
    label {
        position: relative;
        font-weight: bold;
        margin: 0 0 3px;
        padding: 0;
        .pro-badge-inline {
            position: relative;
            top: 4px;
            left: 4px;
        }
    }
    .device-label-icon {
        display: inline-block;
        position: absolute;
        right: -35px;
        top: -4px;
        width: 24px;
        height: 22px;
        box-shadow: rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 1px 3px 1px;
        border-radius: 3px;
        background-color: #fff;
        cursor: pointer;
        span:first-child {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

}
.event-feed-for-eventbrite-metabox-input {
    padding: 15px 12px 15px 15px;
    width: calc( 100% - 260px );
    box-sizing: border-box;
    @media (max-width: 1199px) {
        width: 100%;
    }
    &.no-padding {
        padding: 0;
    }
    input[type=text],
    input[type=number] {
        max-width: 400px;
        min-height: 35px;
        width: 100%; 
    }
}
.event-feed-for-eventbrite-metabox-cols {
    display: flex; 
    width: 748px; 
    max-width: 100%;
    @media (max-width: 1512px) {
        flex-wrap: wrap;
    }
}
.event-feed-for-eventbrite-metabox-col {
    padding: 15px 12px 15px 15px;
    width: 25%;
    border-right: 1px solid #eee;
    @media (max-width: 1512px) {
        width: calc(100% / 3 - 28px);
    }
    @media (max-width: 1300px) {
        width: calc(100% / 2 - 28px);
    }
    @media (max-width: 424px) {
        width: 100%;
    }
    &:nth-child(1) {
        @media (max-width: 1512px) {
            border-bottom: 1px solid #eee;
        }
    }
    &:nth-child(2) {
        @media (max-width: 1512px) {
            border-bottom: 1px solid #eee;
        }
        @media (max-width: 1300px) {
            border-right: none;
        }
    }
    &:nth-child(3) {
        @media (max-width: 1512px) {
            border-right: none;
            border-bottom: 1px solid #eee;
        }
        @media (max-width: 1300px) {
            border-right: 1px solid #eee;
            border-bottom: none;
        }
        @media (max-width: 424px) {
            border-right: none;
            border-bottom: 1px solid #eee;
        }
    }
    &:nth-child(4) {
        border-right: none;
        @media (max-width: 1512px) {
            border-right: 1px solid #eee;
        }
        @media (max-width: 1300px) {
            border-right: none;
        }
    }
    .description {
        margin-top: 0;
    }
}

// Pro badge
#event_feed_popup .postbox-header,
#event_feed_filters .postbox-header {
    position: relative;
    svg {
        position: relative;
        display: inline-block;
        margin-right: auto;
        margin-left: 10px;
        cursor: pointer;
        user-select: none;
        pointer-events: none;
        top: 1px;
    }
    .pro-badge {
        position: relative;
        display: inline-block;
        margin-right: auto;
        margin-left: 10px;
        cursor: pointer;
        user-select: none;
        pointer-events: none;
        top: 1px;
        background-color: #F56E28;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 5px;
        padding: 1px 7px 2px;
        span {
            font-size: 10px;
            color: #fff;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
            font-weight: 700;
        }
    }
}

// Disabled inputs
#event_feed_popup,
#event_feed_filters {
    select[readonly] {
        background: #fff;
        border-color: #8c8f94;
    }
    input[readonly], textarea[readonly] {
        background: #fff;
        border-color: rgba(#8c8f94,.75);
        opacity: .75;
        cursor: text;
        &:active,
        &:hover,
        &:focus {
            outline: none;
            box-shadow: none;
        }
    }
    input[type=checkbox][readonly] {
        cursor: pointer;
        &:active,
        &:hover,
        &:focus {
            outline: none;
            box-shadow: none;
        }
    }
    input[type=checkbox][readonly], input[type=checkbox][readonly]:checked:before, input[type=radio][readonly], input[type=radio][readonly]:checked:before {
        opacity: .75;
    }
    .event-feed-for-eventbrite-switch-input[readonly] {
        display: none;
    }
    .event-feed-for-eventbrite-switch-input[readonly] + .event-feed-for-eventbrite-switch {
        opacity: .75;
        &:hover,
        &.-focus {
            border-color: #7e8993;
            background: #f3f5f6;
            color: inherit;
            .event-feed-for-eventbrite-slider {
                border-color: #7e8993;
            }
        }
    }
    .event-feed-for-eventbrite-switch-input[readonly] + .event-feed-for-eventbrite-switch.-on {
        &:hover,
        &.-focus {
            border-color: #007cba;
            background: #0d99d5;
            color: inherit;
            .event-feed-for-eventbrite-slider {
                border-color: #007cba;
            }
        }
    }
}

// Narrow number inputs with checkboxes
#event-feed-for-eventbrite-events_limit,
#event-feed-for-eventbrite-events_limit_large_tablet,
#event-feed-for-eventbrite-events_limit_small_tablet,
#event-feed-for-eventbrite-events_limit_mobile,
#event-feed-for-eventbrite-excerpt_length,
#event-feed-for-eventbrite-popup_width,
#event-feed-for-eventbrite-title_length {
    width: 60px;
    &[readonly] {
        background: rgba(255,255,255,.5);
        border-color: rgba(220,220,222,.75);
        box-shadow: inset 0 1px 2px rgb(0 0 0 / 4%);
        color: rgba(44,51,56,.5);
        pointer-events: none;
    }
}
#event-feed-for-eventbrite-events_limit_no,
#event-feed-for-eventbrite-events_limit_no_large_tablet,
#event-feed-for-eventbrite-events_limit_no_small_tablet,
#event-feed-for-eventbrite-events_limit_no_mobile,
#event-feed-for-eventbrite-excerpt_length_full,
#event-feed-for-eventbrite-title_length_full {
    margin: .15rem .25rem 0 .5rem;
}

/**
 * Metabox - Feed Layout
 */
 .event-feed-for-eventbrite-design-metabox {
    li {
        span.description {
            margin-top: 5px;
            display: block;
        }
    }
}

/**
 * Metabox - Shortcode
 */
.event-feed-for-eventbrite-shortcode-metabox {
    li {
        input {
            width: 100%;
        }
        span {
            display: block;
        }
        .description {
            font-style: italic;
        }
    }
}

/**
 * Shortcode field (need to be without wrapper because used in the post list)
 */
.event-feed-for-eventbrite-shortcode-field {
    position: relative;
    input[type=text] {
        width: 100%;
        min-height: 35px;
        max-width: 100%;
        margin: 0;
        &[readonly] {
            background: #fff;
        }
    }   
    span {
        cursor: pointer;
        top: 0;
        right: 0;
        bottom: 0;
        width: 35px;
        border: 1px solid #8c8f94;
        border-left: none;
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
        position: absolute;
        display: flex!important;
        justify-content: center;
        align-items: center;
        background-color: #f6f7f7;
        &:hover,
        &:focus {
            outline: none;
            box-shadow: none;
            background-color: #f6f7f7;
        }
        svg {
            fill: #333;
        }
    }
}
.event-feed-for-eventbrite-shortcode-field-inline {
    display: inline-block;
    input[type=text] {
        width: 220px;
        min-height: 30px;
        max-width: 100%;
    }
    span {
        width: 30px;
        display: inline-block;
        background-color: #f6f7f7;
        &:hover,
        &:focus {
            outline: none;
            box-shadow: none;
            background-color: #f6f7f7;
        }
    }
}

/**
 * Upgrade popup
 */
.effe-pro-feature {
    color: inherit;
    &:active,
    &:hover,
    &:focus {
        color: inherit;
        outline: none;
        box-shadow: none;
    }
}
.btn-upgrade {
    color: #fff;
    background-color: hsl(20, 91%, 56%);
    &:hover {
        background-color: hsl(20, 91%, 52%);
    }
}
.jconfirm.jconfirm-modern .jconfirm-box div.jconfirm-title-c {
    color: #444444;
    display: block;
    line-height: 30px;
    padding-bottom: 0;
    margin-bottom: 20px;
}
.jconfirm.jconfirm-modern .jconfirm-box div.jconfirm-content {
    color: #777777;
    font-size: 18px;
    padding: 0 20px;
    line-height: 24px;
    overflow: initial;
    display: block;
    margin-bottom: 15px;
}
.jconfirm.jconfirm-modern .jconfirm-box div.jconfirm-title-c .jconfirm-icon-c {
    font-size: 44px;
    transition: none;
    transform: none;
}
.jconfirm.jconfirm-modern .jconfirm-box div.jconfirm-closeIcon {
    top: 0;
    right: 0;
    padding: 5px;
    height: 25px;
    width: 25px;
    i {
        color: #aaa;
    }
}

/**
 * Admin warning
 */
.event-feed-for-eventbrite-warning {
    position: relative;
    display: inline-block;
    padding: 10px 15px;
    background-color: #f9f9f9;
    margin-top: 15px;
    border-left: 3px solid #DC3232;
    & > div {
        display: flex;
        align-items: center;
    }
}

/**
* Gettings started page
*/
#event-feed-for-eventbrite-getting-started {
    color: #555;
    padding-top: 50px;
    .container {
        margin: 0 auto;
        max-width: 720px;
        padding: 0;
    }
    .progress {
        display: flex;
        justify-content: center;
        margin-bottom: 80px;
        ul {
            margin: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            counter-reset: progress;
            overflow: hidden;
            li {
                margin: 0;
                position: relative;
                counter-increment: progress;
                & > a,
                & > span {
                    color: #2271b1;
                    min-width: 87px;
                    padding: 0 15px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    flex-direction: column;
                    position: relative;
                    text-decoration: none;
                    &:hover,
                    &:active,
                    &:focus {
                        box-shadow: none;
                        outline: none;
                        span {
                            text-decoration: underline;
                        }
                    }
                    &::before {
                        pointer-events: none;
                        content: counter(progress);
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        position: relative;
                        width: 28px;    
                        height: 28px;
                        border-radius: 50%;
                        // border: 1px solid rgb(0 0 0 / 10%);
                        background-color: #fff;
                        color: #334ECD;
                        font-size: 14px;
                        font-weight: 600;
                        text-decoration: none;
                    }
                    &::after {
                        z-index: -1;
                        content: "";
                        display: block;
                        position: absolute;
                        left: 50%;
                        top: 13px;
                        height: 3px;
                        width: 300%;
                        background-color: #fff;
                    }
                    span {
                        margin-top: 5px;
                        font-size: 14px;
                    }
                }
                & > span {
                    color: #555;
                    cursor: unset;
                    &:hover,
                    &:active,
                    &:focus {
                        box-shadow: none;
                        outline: none;
                        span {
                            text-decoration: none;
                        }
                    }
                }
                &:last-child {
                    & > a, 
                    & > span {
                        &::after {
                            background-color: #f0f0f1;
                        }
                    }
                }
                &.current {
                    & > a, 
                    & > span {
                        &::before {
                            background-color: #334ECD;
                            color: #fff;
                        }
                    }
                }
                &.filled {
                    & > a,
                    & > span {
                        &::before {
                            color: #334ECD;
                            content: "\f147";
                            font: normal 18px/1 'dashicons';
                        }
                    }
                }
                &.future {
                    & > a, 
                    & > span {
                        &:active,
                        &:focus {
                            box-shadow: none;
                            outline: none;
                            span {
                                text-decoration: none;
                            }
                        }
                    }
                }
            }
        }
    }
    .intro {
        text-align: center;
        background-color: #fff;
        box-shadow: 0 1px 1px 0 rgb(0 0 0 / 10%);
        margin-bottom: 30px;
        position: relative;
        padding-top: 40px;
    }
    .logo { 
        background-color: #fff;
        box-shadow: rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 1px 3px 1px;
        border-radius: 15%;
        height: 90px;
        width: 90px;
        padding: 0;
        position: absolute;
        top: -45px;
        left: 50%;
        margin-left: -45px;
        overflow: hidden;
        img {
            max-width: 100%;
            height: auto;
        }
    }
    .block {
        padding: 35px 40px 40px;
        h1 {
            color: #222;
            font-size: 24px;
            line-height: 1.3;
            text-align: center;
            margin: 0 0 12px 0;
        }
        hr {
            margin: 25px 0;
            border-top: none;
            border-bottom: 1px solid #dcdcde;
        }
        h6 {
            position: relative;
            max-width: 550px;
            margin: 0 auto;
            font-size: 16px;
            font-weight: 400;
            line-height: 1.6;
            text-align: center;
        }
        p {
            max-width: 440px;
            position: relative;
            font-size: 14px;
            color: #3c434a;
            margin: 10px auto;
        }
        input[type=password] {
            width: 220px;
            max-width: 100%;
            min-height: 35px;
        }
        input[type=submit] {
            height: 35px;
            font-size: 14px;
            line-height: 31px;
        }
    }
    .api-key {
        margin-top: 20px;
    }
    .btn-row {
        margin-top: 25px;
        .button {
            height: 35px;
            font-size: 14px;
            line-height: 31px;
            padding: 0 12px;
            @media (max-width: 782px) {
                line-height: 36px;
            }
        }
        .button + .button {
            margin-left: 3px;
        }
    }
    small {
        color: #888;
        display: block;
        margin-top: 5px;
    }
    .skip-wizard {
        text-align: center;
        a {
            text-decoration: none;
            &:hover {
                text-decoration: underline;
            }
        }
    }
    .iframe-container {
        margin: 25px -40px 35px;
        position: relative;
        overflow: hidden;
        width: calc( 100% + 80px) ;
        padding-top: 56.25%;
    }
    .responsive-iframe {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        width: 100%;
        height: 100%;
    }
    .event-feed-for-eventbrite-api-link-wrapper {
        display: flex;
        justify-content: center;
    }
    .event-feed-for-eventbrite-api-link {
        display: flex;
        align-items: center;
        justify-content: center;
        &:focus {
            svg {
                fill: #043959
            }
        }
        &:hover {
            svg {
                fill: #135e96;
            }
        }
        svg {
            margin-top: 1px;
            width: 18px;
            height: 18px;
            margin-left: 3px;
            fill: #2271b1;
        }
    }
}