/**
 * Using the WordPress Media Uploader in the plugin options page.
 */
jQuery(document).ready(function($) {

    var mediaUploader;
    
    var placeholder_url = $('input.placeholder-background-image').val();

    // Show image if user already saved placeholder image
    if( placeholder_url !== '' ) {

        var mediaUploaderContainer = $('input.placeholder-background-image').parent();
        if( mediaUploaderContainer.children('img').length > 0 ) {
            mediaUploaderContainer.children('img').remove();
        }

        mediaUploaderContainer.prepend('<div class="placeholder-background-image-wrapper"><img src="' + placeholder_url + '"><button type="button" class="close"><svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" role="img" aria-hidden="true" focusable="false"><path d="M13 11.9l3.3-3.4-1.1-1-3.2 3.3-3.2-3.3-1.1 1 3.3 3.4-3.5 3.6 1 1L12 13l3.5 3.5 1-1z"></path></svg></button></div>');
    }

    // Show image after choosing/uploading new placeholder image
    $('input.placeholder-image-upload').on( 'click', function(e) {

        e.preventDefault();
        if (mediaUploader) {
            mediaUploader.open();
            return;
        }

        mediaUploader = wp.media.frames.file_frame = wp.media({
            title: 'Choose Image',
            button: {
            text: 'Choose Image'
        }, multiple: false });

        mediaUploader.on('select', function() {

            var attachment = mediaUploader.state().get('selection').first().toJSON();
            $('input.placeholder-background-image').val(attachment.url);
            $('input.placeholder-background-id').val(attachment.id);
            var mediaUploaderContainer = $('input.placeholder-background-image').parent();

            // Remove existing image
            if( mediaUploaderContainer.children('.placeholder-background-image-wrapper').length > 0 ) {
                mediaUploaderContainer.children('.placeholder-background-image-wrapper').remove();
            }

            var image_preview;
            if( typeof attachment.sizes.large !== 'undefined' ) {
                image_preview = attachment.sizes.large.url;
            } else if( typeof attachment.sizes.medium !== 'undefined' ) {
                image_preview = attachment.sizes.medium.url;
            } else {
                image_preview = attachment.url;
            }

            mediaUploaderContainer.prepend('<div class="placeholder-background-image-wrapper"><img src="' + image_preview + '"><button type="button" class="close"><svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" role="img" aria-hidden="true" focusable="false"><path d="M13 11.9l3.3-3.4-1.1-1-3.2 3.3-3.2-3.3-1.1 1 3.3 3.4-3.5 3.6 1 1L12 13l3.5 3.5 1-1z"></path></svg></button></div>');

        });

        mediaUploader.open();

    });

    // Allow user to remove image after click on delete button
    $(document).on('click', '.placeholder-background-image-wrapper .close', function(e) {

        e.preventDefault();
        var wrapper = $(this).parent('.placeholder-background-image-wrapper');
        if( wrapper.length > 0) {
            wrapper.remove();
            $('input.placeholder-background-image').val('');
            $('input.placeholder-background-id').val('');
        }

    });
    
});