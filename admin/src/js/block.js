(function () {
    
    const { createElement } = wp.element;
    const { SelectControl } = wp.components;

    // Get all event feeds
    const feeds = EventFeedForEventbriteBlock.feeds;
    var feedsArr = [];
    for ( var key in feeds ) {
        if ( feeds.hasOwnProperty( key ) ) {
            feedsArr.push( { label: feeds[key].name + ' (ID: ' + feeds[key].ID + ')', value: feeds[key].ID } );
        }
    }

    // BLock icon
    const blockIcon = createElement( 'svg', { width: 24, height: 24, viewBox: '0 0 24 24', className: 'dashicon' },
        createElement( 'path', {
            fill: 'currentColor',
            d: 'M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm.5 16c0 .3-.2.5-.5.5H5c-.3 0-.5-.2-.5-.5V7h15v12zM9 10H7v2h2v-2zm0 4H7v2h2v-2zm4-4h-2v2h2v-2zm4 0h-2v2h2v-2zm-4 4h-2v2h2v-2zm4 0h-2v2h2v-2z',
        } )
    );

    // External link icon
    const linkIcon = createElement( 'svg', { width: 24, height: 24, viewBox: '0 0 24 24', className: 'dashicon' },
        createElement( 'path', {
            fill: 'currentColor',
            d: 'M18.2 17c0 .7-.6 1.2-1.2 1.2H7c-.7 0-1.2-.6-1.2-1.2V7c0-.7.6-1.2 1.2-1.2h3.2V4.2H7C5.5 4.2 4.2 5.5 4.2 7v10c0 1.5 1.2 2.8 2.8 2.8h10c1.5 0 2.8-1.2 2.8-2.8v-3.6h-1.5V17zM14.9 3v1.5h3.7l-6.4 6.4 1.1 1.1 6.4-6.4v3.7h1.5V3h-6.3z',
        } )
    );

    // Get WP site URL
    function getHomeUrl() {
        var href = window.location.href;
        var index = href.indexOf('/wp-admin');
        var homeUrl = href.substring(0, index);
        return homeUrl;
    }

    // BLock config
    const blockConfig = {
        title: EventFeedForEventbriteBlock.block_name,
        description: EventFeedForEventbriteBlock.block_description,
        icon: blockIcon,
        category: 'widgets',
        attributes: {
            feedId: {
                type: 'string',
                default: feeds[0].ID
            }
        },
        edit( props ) {
            
            return wp.element.createElement(
                'div',
                {
                    className: 'event-feed-for-eventbrite-block-container'
                },
                [
                    wp.element.createElement(
                        'div',
                        {
                            className: 'event-feed-for-eventbrite-block-heading'
                        },
                        [
                            blockIcon,
                            wp.element.createElement(
                                'div',
                                null,
                                EventFeedForEventbriteBlock.block_name
                            )
                        ]
                    ),
                    wp.element.createElement(
                        SelectControl,
                        {
                            label: EventFeedForEventbriteBlock.select_label,
                            value: props.attributes.feedId,
                            className: 'event-feed-for-eventbrite-block-feed-select',
                            options: feedsArr,
                            onChange: function(value) {
                                props.setAttributes({
                                    feedId: value,
                                });
                            },
                        },
                        null
                    ),
                    wp.element.createElement(
                        'div',
                        {
                            className: 'event-feed-for-eventbrite-block-p'
                        },
                        wp.element.createElement(
                            'a',
                            {   
                                className: 'event-feed-for-eventbrite-block-edit-link',
                                href: getHomeUrl() + '/wp-admin/post.php?post=' + props.attributes.feedId + '&action=edit',
                                target: '_blank'
                            },
                            [
                                linkIcon,
                                wp.element.createElement(
                                    'span',
                                    null,
                                    EventFeedForEventbriteBlock.edit_link_text
                                )
                            ]
                        )  
                    )
                ]
            );
        },
        save( props ) {
            return wp.element.createElement(
                'div',
                null,
                '[event-feed id="' + props.attributes.feedId + '"]'
            );
        }
    };

    // Register the block
    wp.blocks.registerBlockType( 'event-feed-for-eventbrite/block', blockConfig );

})();