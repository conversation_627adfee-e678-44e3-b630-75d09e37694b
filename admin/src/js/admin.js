( function( $ ) {
	'use strict';

	$( function() {

		// Add Color Picker to all inputs that have 'color-field' class
		$( 'input.color-field' ).minicolors();

		// Conditional field
		$('[data-condition-element]').each( function() {

			// Vars
			var toggled_element = $(this);
			var condition_element = $('[name="' + $(this).data('condition-element') + '"]');
			var condition_type = $(this).data('condition-type');

			if( ! Array.isArray( $(this).data('condition-element') ) ) {

				// Radio with device switch type
				if( 'radio-device' == condition_type ) {
					var condition_value = $(this).data('condition-value');
					$(condition_element).on('change', function() {
						if( condition_value.includes( $(this).val() ) ) {
							toggled_element.removeClass('hidden');
						} else {
							toggled_element.addClass('hidden');
						}
					})

				// Radio type
				} else if( 'radio' == condition_type ) {
					var condition_value = $(this).data('condition-value');
					$(condition_element).on('change', function() {
						if( condition_value.includes( $(this).val() ) ) {
							toggled_element.removeClass('hidden');
						} else {
							toggled_element.addClass('hidden');
						}
					})
				}

				// Checkbox type
				else if( 'checkbox' == condition_type ) {
					$(condition_element).on('change', function() {
						if( $(this).prop('checked') ) {
							toggled_element.removeClass('hidden');
						} else {
							toggled_element.addClass('hidden');
						}
					})
				}

			} else {

				// Checkbox multiple conditions
				if( 'checkbox' == condition_type ) {

					var totalCount = $(this).data('condition-element').length;
					var count = 0;

					// Count number of checked elements on the page load
					$(this).data('condition-element').forEach( function( el_name ) {
						var el = $('[name="' + el_name + '"]');
						if( el.prop('checked') === true ) {
							count++;
						}
					});

					// On change on any conditional element check number of checked elements and compare to their total number
					$(this).data('condition-element').forEach( function( el_name ) {
						var el = $('[name="' + el_name + '"]');
						el.on('change', function() {
							if( el.prop('checked') === true ) {
								count++;
							} else {
								count--;
							}

							// Rendering logic
							if( count === totalCount ) {
								toggled_element.removeClass('hidden');
							} else {
								toggled_element.addClass('hidden');
							}

						})	

					});


				}

			}

			
		})

		// Events limit checkbox
		if( $('[name^="event-feed-for-eventbrite[events_limit_no"]').length > 0 ) {
			$('[name^="event-feed-for-eventbrite[events_limit_no"]').on('click', function(e) {
				var number_input = $(this).prev('[name^="event-feed-for-eventbrite[events_limit"]');
				if( $(this).prop( 'checked' ) ) {
					number_input.attr( 'readonly', true );
				} else {
					number_input.attr( 'readonly', false );
				}
			})
		}

		// Description length checkbox
		if( $('[name="event-feed-for-eventbrite[excerpt_length_full]"]').length > 0 ) {
			$('[name="event-feed-for-eventbrite[excerpt_length_full]"]').on('click', function(e) {
				var number_input = $(this).prev('[name="event-feed-for-eventbrite[excerpt_length]"]');
				if( $(this).prop( 'checked' ) ) {
					number_input.attr( 'readonly', true );
				} else {
					number_input.attr( 'readonly', false );
				}
			})
		}

		// Description length checkbox
		if( $('[name="event-feed-for-eventbrite[title_length_full]"]').length > 0 ) {
			$('[name="event-feed-for-eventbrite[title_length_full]"]').on('click', function(e) {
				var number_input = $(this).prev('[name="event-feed-for-eventbrite[title_length]"]');
				if( $(this).prop( 'checked' ) ) {
					number_input.attr( 'readonly', true );
				} else {
					number_input.attr( 'readonly', false );
				}
			})
		}

		// Number of grid rows select
		if( $('.depend-on-device-desktop .choices-grid-rows').length > 0 ) {
			const grid_rows_el = $('.depend-on-device-desktop .choices-grid-rows')[0];
			const grid_rows_select = new Choices(grid_rows_el, {
				searchEnabled: false,
				searchChoices: false,
				shouldSort: false,
				shouldSortItems: false,
				itemSelectText: '',
			});
		}
		if( $('.depend-on-device-large-tablet .choices-grid-rows').length > 0 ) {
			const grid_rows_el = $('.depend-on-device-large-tablet .choices-grid-rows')[0];
			const grid_rows_select = new Choices(grid_rows_el, {
				searchEnabled: false,
				searchChoices: false,
				shouldSort: false,
				shouldSortItems: false,
				itemSelectText: '',
			});
		}
		if( $('.depend-on-device-small-tablet .choices-grid-rows').length > 0 ) {
			const grid_rows_el = $('.depend-on-device-small-tablet .choices-grid-rows')[0];
			const grid_rows_select = new Choices(grid_rows_el, {
				searchEnabled: false,
				searchChoices: false,
				shouldSort: false,
				shouldSortItems: false,
				itemSelectText: '',
			});
		}
		if( $('.depend-on-device-mobile .choices-grid-rows').length > 0 ) {
			const grid_rows_el = $('.depend-on-device-mobile .choices-grid-rows')[0];
			const grid_rows_select = new Choices(grid_rows_el, {
				searchEnabled: false,
				searchChoices: false,
				shouldSort: false,
				shouldSortItems: false,
				itemSelectText: '',
			});
		}

		// Organization
		if( $('.choices-organization').length > 0 ) {
			const organization_el = $('.choices-organization')[0];
			const organization_select = new Choices(organization_el, {
				searchEnabled: false,
				searchChoices: false,
				shouldSort: false,
				shouldSortItems: false,
			});
		}

		// Time filter
		if( $('.choices-time-filter').length > 0 ) {
			const time_el = $('.choices-time-filter')[0];
			const time_select = new Choices(time_el, {
				searchEnabled: false,
				searchChoices: false,
				shouldSort: false,
				shouldSortItems: false,
			});
		}

		// Link to select
		if( $('.choices-linkto').length > 0 ) {
			const linkto_el = $('.choices-linkto')[0];
			const linkto_select = new Choices(linkto_el, {
				searchEnabled: false,
				searchChoices: false,
				shouldSort: false,
				shouldSortItems: false,
			});
		}

		// Cache duration select
		if( $('.choices-cache-duration').length > 0 ) {
			const cache_el = $('.choices-cache-duration')[0];
			const cache_select = new Choices(cache_el, {
				searchEnabled: false,
				searchChoices: false,
				shouldSort: false,
				shouldSortItems: false,
			});
		}

		// Google fonts family
		if( $('.choices-google-fonts-family').length > 0 ) {
			const google_el = $('.choices-google-fonts-family')[0];
			const google_select = new Choices(google_el, {
				searchEnabled: true,
    			searchChoices: true,
				searchPlaceholderValue: "Search Font Family",
				searchResultLimit: 20,
				shouldSort: false,
				shouldSortItems: false,
			});
		}

		// Copy shortcode functionality
		if( $('.event-feed-for-eventbrite-tooltip').length > 0 ) {
			$('.event-feed-for-eventbrite-tooltip').each( function(index) {

				// Copy shortcode tooltip
				$(this).tooltipster({
					side: 'bottom',
					theme: 'tooltipster-borderless',
					trigger: 'custom',
					triggerOpen: {
						mouseenter: true
					},
					triggerClose: {
						mouseleave: true
					},
					content: EventFeedForEventbriteAdminTranslations.copy_text,
					delay: 0,
				});

				new ClipboardJS(this);

				// On click destroy original tooltip and create new with 'Copied' text -> open it
				$(this).on('click', function() {
					$(this).tooltipster('content', EventFeedForEventbriteAdminTranslations.copied_text);
				});

				// On mouse leave recreate original tooltip
				$(this).on('mouseleave', function() {
					$(this).tooltipster('content', EventFeedForEventbriteAdminTranslations.copy_text);
				});
			
			});
		}

		// True / False switch
		if( $('.event-feed-for-eventbrite-switch').length > 0 ) {

			$('.event-feed-for-eventbrite-switch-label').on('click', function() {

				var input = $( '#' + $(this).attr('for') + ':not([disabled]):not([readonly])' );
				if( input.length > 0 ) {

					var input = $( '#' + $(this).attr('for') );
					var switch_el = input.next('.event-feed-for-eventbrite-switch');
					switch_el.toggleClass('-on');
					if( switch_el.hasClass('-on') ) {
						input.prop('checked', true);
						input.trigger('change');
					} else {
						input.prop('checked', false);
						input.trigger('change');	
					}

				}

			});

			$('.event-feed-for-eventbrite-switch').on('click', function() {

				var input = $(this).prev('input:not([disabled]):not([readonly])');
				if( input.length > 0 ) {

					$(this).toggleClass('-on');
					var input = $(this).prev('input');
					if( $(this).hasClass('-on') ) {
						input.prop('checked', true);
						input.trigger('change');
					} else {
						input.prop('checked', false);
						input.trigger('change');	
					}

				}

			});

		}

		// Date format
		if( $('input[name="event-feed-for-eventbrite-formats[date_format]"]').length > 0 ) {

			$('input[name="event-feed-for-eventbrite-formats[date_format]"]').on('change', function() {
				
				// Input fill
				var format = $(this).val();
				var custom_input = $('#event-feed-for-eventbrite-formats-date_format_custom');
				if( ! $(this).hasClass( 'date-format-custom' ) ) {
					custom_input.val(format);
				}

				// Preview
				var value = $(this).next('.date-time-text').text();
				var preview = $('.event-feed-for-eventbrite-fulldate-settings .example');
				if( ! $(this).hasClass( 'date-format-custom' ) ) {
					preview.text(value);
				}

			});
			
			// Choose custom option after focus on custom input
			$('#event-feed-for-eventbrite-formats-date_format_custom').on('focus', function() {
				$('.date-format-custom').prop('checked', true);
			});

			// Get custom date after custom input change
			$('#event-feed-for-eventbrite-formats-date_format_custom').on('input', function() {
				var format = $(this).val();
				var ajaxdata = {
					action: 'get_dateformat',
					format: format
				};
				jQuery.post(ajaxurl, ajaxdata, function(res) { 
					var preview = $('.event-feed-for-eventbrite-fulldate-settings .example');
					var spinner = $('.event-feed-for-eventbrite-fulldate-settings .spinner');
					spinner.css('visibility', 'visible');
					setTimeout(function() {
						spinner.css('visibility', 'hidden');
						preview.text(res);
					}, 300)
				});
			});

		}

		// Pro badge
		if( $('#event_feed_popup .hndle').length ) {
			var text = $('#event_feed_popup .hndle').text();
			$('#event_feed_popup .hndle').html('<span>' + text + '</span>');
			$('<span class="pro-badge"><span>' + EventFeedForEventbriteAdminTranslations.pro_feature + '</span></span>').insertAfter( $('#event_feed_popup .hndle span') );

		}
		if( $('#event_feed_filters .hndle').length ) {
			var text = $('#event_feed_filters .hndle').text();
			$('#event_feed_filters .hndle').html('<span>' + text + '</span>');
			$('<span class="pro-badge"><span>' + EventFeedForEventbriteAdminTranslations.pro_feature + '</span></span>').insertAfter( $('#event_feed_filters .hndle span') );
		}

		// Upgrade popup
		var premium = ( EventFeedForEventbrite.premium === 'true' );
		var free = ( EventFeedForEventbrite.free === 'true' );
		if( ( premium == false ) || ( free == true ) ) {
			$('.effe-pro-feature').on( 'click', function(el) {
				el.preventDefault();
				var service = $(this).data('service');
				var upgrade_link = $(this).data('upgrade-link');
				$.confirm({
					title: service + ' ' + EventFeedForEventbriteAdminTranslations.pro_feature_notice_title,
					content: EventFeedForEventbriteAdminTranslations.pro_feature_notice + ' ' + service + ' ' + EventFeedForEventbriteAdminTranslations.pro_feature_notice_next,
					theme: 'modern',
					closeIcon: true,
					closeIconClass: 'fa fa-close',
					icon: 'fa fa-lock',
					draggable: true,
					boxWidth: '490px',
					useBootstrap: false,
					escapeKey: true,
					backgroundDismiss: true,
					buttons: {
						upgrade: {
							btnClass: 'btn btn-upgrade',
							text: EventFeedForEventbriteAdminTranslations.pro_feature_notice_btn,
							action: function (upgrade) {
								if ( $('form[action="post.php"]').hasClass('dirty') ) {
									window.open(upgrade_link, '_blank').focus();
    							} else {
									window.location = upgrade_link;
								}
							}
						}
					}
				});	

			})
		}

		// Trigger question about unsaved changes on event feed editing screen
		if( $('form[action="post.php"]') ) {
			$('form[action="post.php"]').areYouSure();
		} 

	});

})( jQuery );
