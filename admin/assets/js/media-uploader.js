jQuery(document).ready((function(e){var a,r=e("input.placeholder-background-image").val();if(""!==r){var l=e("input.placeholder-background-image").parent();l.children("img").length>0&&l.children("img").remove(),l.prepend('<div class="placeholder-background-image-wrapper"><img src="'+r+'"><button type="button" class="close"><svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" role="img" aria-hidden="true" focusable="false"><path d="M13 11.9l3.3-3.4-1.1-1-3.2 3.3-3.2-3.3-1.1 1 3.3 3.4-3.5 3.6 1 1L12 13l3.5 3.5 1-1z"></path></svg></button></div>')}e("input.placeholder-image-upload").on("click",(function(r){r.preventDefault(),a||(a=wp.media.frames.file_frame=wp.media({title:"Choose Image",button:{text:"Choose Image"},multiple:!1})).on("select",(function(){var r=a.state().get("selection").first().toJSON();e("input.placeholder-background-image").val(r.url),e("input.placeholder-background-id").val(r.id);var l,i=e("input.placeholder-background-image").parent();i.children(".placeholder-background-image-wrapper").length>0&&i.children(".placeholder-background-image-wrapper").remove(),l=void 0!==r.sizes.large?r.sizes.large.url:void 0!==r.sizes.medium?r.sizes.medium.url:r.url,i.prepend('<div class="placeholder-background-image-wrapper"><img src="'+l+'"><button type="button" class="close"><svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" role="img" aria-hidden="true" focusable="false"><path d="M13 11.9l3.3-3.4-1.1-1-3.2 3.3-3.2-3.3-1.1 1 3.3 3.4-3.5 3.6 1 1L12 13l3.5 3.5 1-1z"></path></svg></button></div>')})),a.open()})),e(document).on("click",".placeholder-background-image-wrapper .close",(function(a){a.preventDefault();var r=e(this).parent(".placeholder-background-image-wrapper");r.length>0&&(r.remove(),e("input.placeholder-background-image").val(""),e("input.placeholder-background-id").val(""))}))}));