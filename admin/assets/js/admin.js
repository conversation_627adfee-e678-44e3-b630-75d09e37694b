!function(e){"use strict";e((function(){if(e("input.color-field").minicolors(),e("[data-condition-element]").each((function(){var t=e(this),n=e('[name="'+e(this).data("condition-element")+'"]'),o=e(this).data("condition-type");if(Array.isArray(e(this).data("condition-element"))){if("checkbox"==o){var i=e(this).data("condition-element").length,r=0;e(this).data("condition-element").forEach((function(t){!0===e('[name="'+t+'"]').prop("checked")&&r++})),e(this).data("condition-element").forEach((function(n){var o=e('[name="'+n+'"]');o.on("change",(function(){!0===o.prop("checked")?r++:r--,r===i?t.removeClass("hidden"):t.addClass("hidden")}))}))}}else if("radio-device"==o){var s=e(this).data("condition-value");e(n).on("change",(function(){s.includes(e(this).val())?t.removeClass("hidden"):t.addClass("hidden")}))}else"radio"==o?(s=e(this).data("condition-value"),e(n).on("change",(function(){s.includes(e(this).val())?t.removeClass("hidden"):t.addClass("hidden")}))):"checkbox"==o&&e(n).on("change",(function(){e(this).prop("checked")?t.removeClass("hidden"):t.addClass("hidden")}))})),e('[name^="event-feed-for-eventbrite[events_limit_no"]').length>0&&e('[name^="event-feed-for-eventbrite[events_limit_no"]').on("click",(function(t){var n=e(this).prev('[name^="event-feed-for-eventbrite[events_limit"]');e(this).prop("checked")?n.attr("readonly",!0):n.attr("readonly",!1)})),e('[name="event-feed-for-eventbrite[excerpt_length_full]"]').length>0&&e('[name="event-feed-for-eventbrite[excerpt_length_full]"]').on("click",(function(t){var n=e(this).prev('[name="event-feed-for-eventbrite[excerpt_length]"]');e(this).prop("checked")?n.attr("readonly",!0):n.attr("readonly",!1)})),e('[name="event-feed-for-eventbrite[title_length_full]"]').length>0&&e('[name="event-feed-for-eventbrite[title_length_full]"]').on("click",(function(t){var n=e(this).prev('[name="event-feed-for-eventbrite[title_length]"]');e(this).prop("checked")?n.attr("readonly",!0):n.attr("readonly",!1)})),e(".depend-on-device-desktop .choices-grid-rows").length>0){var t=e(".depend-on-device-desktop .choices-grid-rows")[0];new Choices(t,{searchEnabled:!1,searchChoices:!1,shouldSort:!1,shouldSortItems:!1,itemSelectText:""})}if(e(".depend-on-device-large-tablet .choices-grid-rows").length>0){var n=e(".depend-on-device-large-tablet .choices-grid-rows")[0];new Choices(n,{searchEnabled:!1,searchChoices:!1,shouldSort:!1,shouldSortItems:!1,itemSelectText:""})}if(e(".depend-on-device-small-tablet .choices-grid-rows").length>0){var o=e(".depend-on-device-small-tablet .choices-grid-rows")[0];new Choices(o,{searchEnabled:!1,searchChoices:!1,shouldSort:!1,shouldSortItems:!1,itemSelectText:""})}if(e(".depend-on-device-mobile .choices-grid-rows").length>0){var i=e(".depend-on-device-mobile .choices-grid-rows")[0];new Choices(i,{searchEnabled:!1,searchChoices:!1,shouldSort:!1,shouldSortItems:!1,itemSelectText:""})}if(e(".choices-organization").length>0){var r=e(".choices-organization")[0];new Choices(r,{searchEnabled:!1,searchChoices:!1,shouldSort:!1,shouldSortItems:!1})}if(e(".choices-time-filter").length>0){var s=e(".choices-time-filter")[0];new Choices(s,{searchEnabled:!1,searchChoices:!1,shouldSort:!1,shouldSortItems:!1})}if(e(".choices-linkto").length>0){var a=e(".choices-linkto")[0];new Choices(a,{searchEnabled:!1,searchChoices:!1,shouldSort:!1,shouldSortItems:!1})}if(e(".choices-cache-duration").length>0){var c=e(".choices-cache-duration")[0];new Choices(c,{searchEnabled:!1,searchChoices:!1,shouldSort:!1,shouldSortItems:!1})}if(e(".choices-google-fonts-family").length>0){var d=e(".choices-google-fonts-family")[0];new Choices(d,{searchEnabled:!0,searchChoices:!0,searchPlaceholderValue:"Search Font Family",searchResultLimit:20,shouldSort:!1,shouldSortItems:!1})}if(e(".event-feed-for-eventbrite-tooltip").length>0&&e(".event-feed-for-eventbrite-tooltip").each((function(t){e(this).tooltipster({side:"bottom",theme:"tooltipster-borderless",trigger:"custom",triggerOpen:{mouseenter:!0},triggerClose:{mouseleave:!0},content:EventFeedForEventbriteAdminTranslations.copy_text,delay:0}),new ClipboardJS(this),e(this).on("click",(function(){e(this).tooltipster("content",EventFeedForEventbriteAdminTranslations.copied_text)})),e(this).on("mouseleave",(function(){e(this).tooltipster("content",EventFeedForEventbriteAdminTranslations.copy_text)}))})),e(".event-feed-for-eventbrite-switch").length>0&&(e(".event-feed-for-eventbrite-switch-label").on("click",(function(){if((t=e("#"+e(this).attr("for")+":not([disabled]):not([readonly])")).length>0){var t,n=(t=e("#"+e(this).attr("for"))).next(".event-feed-for-eventbrite-switch");n.toggleClass("-on"),n.hasClass("-on")?(t.prop("checked",!0),t.trigger("change")):(t.prop("checked",!1),t.trigger("change"))}})),e(".event-feed-for-eventbrite-switch").on("click",(function(){if((t=e(this).prev("input:not([disabled]):not([readonly])")).length>0){e(this).toggleClass("-on");var t=e(this).prev("input");e(this).hasClass("-on")?(t.prop("checked",!0),t.trigger("change")):(t.prop("checked",!1),t.trigger("change"))}}))),e('input[name="event-feed-for-eventbrite-formats[date_format]"]').length>0&&(e('input[name="event-feed-for-eventbrite-formats[date_format]"]').on("change",(function(){var t=e(this).val(),n=e("#event-feed-for-eventbrite-formats-date_format_custom");e(this).hasClass("date-format-custom")||n.val(t);var o=e(this).next(".date-time-text").text(),i=e(".event-feed-for-eventbrite-fulldate-settings .example");e(this).hasClass("date-format-custom")||i.text(o)})),e("#event-feed-for-eventbrite-formats-date_format_custom").on("focus",(function(){e(".date-format-custom").prop("checked",!0)})),e("#event-feed-for-eventbrite-formats-date_format_custom").on("input",(function(){var t={action:"get_dateformat",format:e(this).val()};jQuery.post(ajaxurl,t,(function(t){var n=e(".event-feed-for-eventbrite-fulldate-settings .example"),o=e(".event-feed-for-eventbrite-fulldate-settings .spinner");o.css("visibility","visible"),setTimeout((function(){o.css("visibility","hidden"),n.text(t)}),300)}))}))),e("#event_feed_popup .hndle").length){var l=e("#event_feed_popup .hndle").text();e("#event_feed_popup .hndle").html("<span>"+l+"</span>"),e('<span class="pro-badge"><span>'+EventFeedForEventbriteAdminTranslations.pro_feature+"</span></span>").insertAfter(e("#event_feed_popup .hndle span"))}e("#event_feed_filters .hndle").length&&(l=e("#event_feed_filters .hndle").text(),e("#event_feed_filters .hndle").html("<span>"+l+"</span>"),e('<span class="pro-badge"><span>'+EventFeedForEventbriteAdminTranslations.pro_feature+"</span></span>").insertAfter(e("#event_feed_filters .hndle span")));var h="true"===EventFeedForEventbrite.premium,f="true"===EventFeedForEventbrite.free;0!=h&&1!=f||e(".effe-pro-feature").on("click",(function(t){t.preventDefault();var n=e(this).data("service"),o=e(this).data("upgrade-link");e.confirm({title:n+" "+EventFeedForEventbriteAdminTranslations.pro_feature_notice_title,content:EventFeedForEventbriteAdminTranslations.pro_feature_notice+" "+n+" "+EventFeedForEventbriteAdminTranslations.pro_feature_notice_next,theme:"modern",closeIcon:!0,closeIconClass:"fa fa-close",icon:"fa fa-lock",draggable:!0,boxWidth:"490px",useBootstrap:!1,escapeKey:!0,backgroundDismiss:!0,buttons:{upgrade:{btnClass:"btn btn-upgrade",text:EventFeedForEventbriteAdminTranslations.pro_feature_notice_btn,action:function(t){e('form[action="post.php"]').hasClass("dirty")?window.open(o,"_blank").focus():window.location=o}}}})})),e('form[action="post.php"]')&&e('form[action="post.php"]').areYouSure()}))}(jQuery);