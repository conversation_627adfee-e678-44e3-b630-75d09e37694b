/*! For license information please see jquery.are-you-sure.js.LICENSE.txt */
!function(e){e.fn.areYouSure=function(i){var t=e.extend({message:"You have unsaved changes!",dirtyClass:"dirty",change:null,silent:!1,addRemoveFieldsMarksDirty:!1,fieldEvents:"change keyup propertychange input",fieldSelector:":input:not(input[type=submit]):not(input[type=button])"},i),n=function(i){if(i.hasClass("ays-ignore")||i.hasClass("aysIgnore")||i.attr("data-ays-ignore")||void 0===i.attr("name"))return null;if(i.is(":disabled"))return"ays-disabled";var t,n=i.attr("type");switch(i.is("select")&&(n="select"),n){case"checkbox":case"radio":t=i.is(":checked");break;case"select":t="",i.find("option").each((function(i){var n=e(this);n.is(":selected")&&(t+=n.val())}));break;default:t=i.val()}return t},r=function(e){e.data("ays-orig",n(e))},a=function(i){var r=function(e){var i=e.data("ays-orig");return void 0!==i&&n(e)!=i},a=e(this).is("form")?e(this):e(this).parents("form");if(r(e(i.target)))o(a,!0);else{if($fields=a.find(t.fieldSelector),t.addRemoveFieldsMarksDirty)if(a.data("ays-orig-field-count")!=$fields.length)return void o(a,!0);var s=!1;$fields.each((function(){if($field=e(this),r($field))return s=!0,!1})),o(a,s)}},s=function(i){var n=i.find(t.fieldSelector);e(n).each((function(){r(e(this))})),e(n).unbind(t.fieldEvents,a),e(n).bind(t.fieldEvents,a),i.data("ays-orig-field-count",e(n).length),o(i,!1)},o=function(e,i){var n=i!=e.hasClass(t.dirtyClass);e.toggleClass(t.dirtyClass,i),n&&(t.change&&t.change.call(e,e),i&&e.trigger("dirty.areYouSure",[e]),i||e.trigger("clean.areYouSure",[e]),e.trigger("change.areYouSure",[e]))},d=function(){var i=e(this),n=i.find(t.fieldSelector);e(n).each((function(){var i=e(this);i.data("ays-orig")||(r(i),i.bind(t.fieldEvents,a))})),i.trigger("checkform.areYouSure")},u=function(){s(e(this))};return t.silent||window.aysUnloadSet||(window.aysUnloadSet=!0,e(window).bind("beforeunload",(function(){if($dirtyForms=e("form").filter("."+t.dirtyClass),0!=$dirtyForms.length){if(navigator.userAgent.toLowerCase().match(/msie|chrome/)){if(window.aysHasPrompted)return;window.aysHasPrompted=!0,window.setTimeout((function(){window.aysHasPrompted=!1}),900)}return t.message}}))),this.each((function(i){if(e(this).is("form")){var n=e(this);n.submit((function(){n.removeClass(t.dirtyClass)})),n.bind("reset",(function(){o(n,!1)})),n.bind("rescan.areYouSure",d),n.bind("reinitialize.areYouSure",u),n.bind("checkform.areYouSure",a),s(n)}}))}}(jQuery);