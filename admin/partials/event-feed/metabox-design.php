<?php 

// Get the event feed post meta
$layout_type                   = get_post_meta( $post->ID, 'layout_type', true );
$theme                         = get_post_meta( $post->ID, 'theme', true );
$rows                          = get_post_meta( $post->ID, 'rows', true );
$rows_large_tablet             = get_post_meta( $post->ID, 'rows_large_tablet', true );
$rows_small_tablet             = get_post_meta( $post->ID, 'rows_small_tablet', true );
$rows_mobile                   = get_post_meta( $post->ID, 'rows_mobile', true );
$events_limit                  = get_post_meta( $post->ID, 'events_limit', true );
$events_limit_no               = get_post_meta( $post->ID, 'events_limit_no', true );
$events_limit_large_tablet     = get_post_meta( $post->ID, 'events_limit_large_tablet', true );
$events_limit_no_large_tablet  = get_post_meta( $post->ID, 'events_limit_no_large_tablet', true );
$events_limit_small_tablet     = get_post_meta( $post->ID, 'events_limit_small_tablet', true );
$events_limit_no_small_tablet  = get_post_meta( $post->ID, 'events_limit_no_small_tablet', true );
$events_limit_mobile           = get_post_meta( $post->ID, 'events_limit_mobile', true );
$events_limit_no_mobile        = get_post_meta( $post->ID, 'events_limit_no_mobile', true );
$theme_color                   = get_post_meta( $post->ID, 'theme_color', true );

$layouts_array = ['grid','cards'];

?>

<div class="event-feed-for-eventbrite-metabox-content event-feed-for-eventbrite-design-metabox">

    <!-- Layout Type -->
    <div class="event-feed-for-eventbrite-metabox-field">
        <div class="event-feed-for-eventbrite-metabox-label">
            <label for="<?php echo esc_attr( $this->plugin_name . '-layout_type' ); ?>">
                <?php esc_html_e( 'Layout', 'event-feed-for-eventbrite' ); ?>
            </label>
            <p class="description"><?php esc_html_e( 'Choose the layout for your event feed.', 'event-feed-for-eventbrite'); ?></p>
        </div>
        <div class="event-feed-for-eventbrite-metabox-input">
            <fieldset class="layout-radio"> 

                <span>
                    <input type="radio" id="<?php echo esc_attr( $this->plugin_name . '-layout_type_list' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[layout_type]' ); ?>" value="list" <?php if( $layout_type == 'list' || $layout_type == '' ) { echo 'checked'; } ?>>
                    <label for="<?php echo esc_attr( $this->plugin_name . '-layout_type_list' ); ?>">
                        <?php esc_html_e( 'List', 'event-feed-for-eventbrite' ); ?>
                    </label>
                    <a class="demo-link" href="https://www.eventfeed.click/demo/list/" target="_blank"><?php esc_html_e( 'Demo', 'event-feed-for-eventbrite' ); ?></a>
                </span>

                <span>
                    <input type="radio" id="<?php echo esc_attr( $this->plugin_name . '-layout_type_widget' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[layout_type]' ); ?>" value="widget" <?php if( $layout_type == 'widget' ) { echo 'checked'; } ?>>
                    <label for="<?php echo esc_attr( $this->plugin_name . '-layout_type_widget' ); ?>">
                        <?php esc_html_e( 'Widget', 'event-feed-for-eventbrite' ); ?>
                    </label>
                    <a class="demo-link" href="https://www.eventfeed.click/demo/widget/" target="_blank"><?php esc_html_e( 'Demo', 'event-feed-for-eventbrite' ); ?></a>
                </span>
                
                <span>
                    <input type="radio" id="<?php echo esc_attr( $this->plugin_name . '-layout_type_grid' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[layout_type]' ); ?>" value="grid" <?php if( $layout_type == 'grid' ) { echo 'checked'; } ?> <?php if( ! $this->freemius->can_use_premium_code() || $this->freemius->is_free_plan() ) { echo 'readonly'; } ?>>
                    <label for="<?php echo esc_attr( $this->plugin_name . '-layout_type_grid' ); ?>" class="effe-pro-feature" data-service="<?php esc_attr_e( 'Event Grid Layout', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo $this->freemius->get_upgrade_url(); ?>">
                        <?php esc_html_e( 'Grid', 'event-feed-for-eventbrite' ); ?>
                        <svg class="pro-badge-inline" xmlns="http://www.w3.org/2000/svg" width="33" height="17"><rect width="33" height="17" rx="5" fill="#F56E28"/><text transform="translate(6.815 12.363)" fill="#fff" font-size="10" font-family="-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif" font-weight="700"><tspan x="0" y="0">PRO</tspan></text></svg>
                    </label>
                    <a class="demo-link" href="https://www.eventfeed.click/demo/grid/" target="_blank"><?php esc_html_e( 'Demo', 'event-feed-for-eventbrite' ); ?></a>
                </span>

                <span>
                    <input type="radio" id="<?php echo esc_attr( $this->plugin_name . '-layout_type_cards' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[layout_type]' ); ?>" value="cards" <?php if( $layout_type == 'cards' ) { echo 'checked'; } ?> <?php if( ! $this->freemius->can_use_premium_code() || $this->freemius->is_free_plan() ) { echo 'readonly'; } ?>>
                    <label for="<?php echo esc_attr( $this->plugin_name . '-layout_type_cards' ); ?>" class="effe-pro-feature" data-service="<?php esc_attr_e( 'Event Cards Layout', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo $this->freemius->get_upgrade_url(); ?>">
                        <?php esc_html_e( 'Cards', 'event-feed-for-eventbrite' ); ?>
                        <svg class="pro-badge-inline" xmlns="http://www.w3.org/2000/svg" width="33" height="17"><rect width="33" height="17" rx="5" fill="#F56E28"/><text transform="translate(6.815 12.363)" fill="#fff" font-size="10" font-family="-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif" font-weight="700"><tspan x="0" y="0">PRO</tspan></text></svg>
                    </label>
                    <a class="demo-link" href="https://www.eventfeed.click/demo/cards/" target="_blank"><?php esc_html_e( 'Demo', 'event-feed-for-eventbrite' ); ?></a>
                </span>

            </fieldset>
            
            <?php if( ( ! $this->freemius->can_use_premium_code() || $this->freemius->is_free_plan() ) && ! in_array( $layout_type, array( '', 'list', 'widget' ) ) ) { ?>
            <div class="event-feed-for-eventbrite-warning">
                <div>
                    <div><?php
                    printf(
                        '%s <strong>%s</strong>. <br>%s <a href="' . esc_url( $this->freemius->get_upgrade_url() ) . '">' . esc_html__( 'renew your license', 'event-feed-for-eventbrite' ) . '</a>.',
                        esc_html__( 'The selected layout is not applied because', 'event-feed-for-eventbrite' ) ,
                        esc_html__( 'your premium license is not active', 'event-feed-for-eventbrite' ),
                        esc_html__( 'To continue using this feature, please', 'event-feed-for-eventbrite' )
                    );
                    ?></div>
                </div>
            </div>
            <?php } ?>
            
        </div>
    </div>

    <!-- Number of columns -->
    <div class="event-feed-for-eventbrite-metabox-field <?php if( ! in_array( $layout_type, $layouts_array) ) { echo 'hidden'; } ?>" data-condition-element="<?php echo esc_attr( $this->plugin_name . '[layout_type]' ); ?>" data-condition-type="radio-device" data-condition-value="<?php echo esc_attr( htmlspecialchars( json_encode( $layouts_array ) ) ); ?>">
        <div class="event-feed-for-eventbrite-metabox-label">
            <label for="<?php echo esc_attr( $this->plugin_name . '-rows' ); ?>">
                <?php esc_html_e( 'Number of columns', 'event-feed-for-eventbrite' ); ?>
            </label>
            <p class="description"><?php esc_html_e( 'The number of columns in the grid.', 'event-feed-for-eventbrite'); ?></p>
        </div>
        <div class="event-feed-for-eventbrite-metabox-input no-padding">
            <div class="event-feed-for-eventbrite-metabox-cols">
                <div class="event-feed-for-eventbrite-metabox-col">
                    <p class="description">
                        <?php esc_html_e( 'Desktop computers', 'event-feed-for-eventbrite' ); ?>
                    </p>
                    <div class="select-wrapper">
                        <select id="<?php echo esc_attr( $this->plugin_name . '-rows' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[rows]' ); ?>" class="small-text choices-grid-rows">
                            <option value="1" <?php if( $rows == '1' ) { echo 'selected'; } ?>>1</option>
                            <option value="2" <?php if( $rows == '2' ) { echo 'selected'; } ?>>2</option>
                            <option value="3" <?php if( $rows == '3' || $rows == '' ) { echo 'selected'; } ?>>3</option>
                            <option value="4" <?php if( $rows == '4' ) { echo 'selected'; } ?>>4</option>
                        </select>
                    </div>
                </div>
                <div class="event-feed-for-eventbrite-metabox-col">
                    <p class="description">
                        <?php esc_html_e( 'Large tablet devices', 'event-feed-for-eventbrite' ); ?>
                    </p>
                    <div class="select-wrapper">
                        <select id="<?php echo esc_attr( $this->plugin_name . '-rows_large_tablet' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[rows_large_tablet]' ); ?>" class="small-text choices-grid-rows">
                            <option value="1" <?php if( $rows_large_tablet == '1' ) { echo 'selected'; } ?>>1</option>
                            <option value="2" <?php if( $rows_large_tablet == '2' ) { echo 'selected'; } ?>>2</option>
                            <option value="3" <?php if( $rows_large_tablet == '3' || $rows_large_tablet == '' ) { echo 'selected'; } ?>>3</option>
                            <option value="4" <?php if( $rows_large_tablet == '4' ) { echo 'selected'; } ?>>4</option>
                        </select>
                    </div>
                </div>
                <div class="event-feed-for-eventbrite-metabox-col">
                    <p class="description">
                        <?php esc_html_e( 'Small tablet devices', 'event-feed-for-eventbrite' ); ?>
                    </p>
                    <div class="select-wrapper">
                        <select id="<?php echo esc_attr( $this->plugin_name . '-rows_small_tablet' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[rows_small_tablet]' ); ?>" class="small-text choices-grid-rows">
                            <option value="1" <?php if( $rows_small_tablet == '1' ) { echo 'selected'; } ?>>1</option>
                            <option value="2" <?php if( $rows_small_tablet == '2' || $rows_small_tablet == '' ) { echo 'selected'; } ?>>2</option>
                            <option value="3" <?php if( $rows_small_tablet == '3' ) { echo 'selected'; } ?>>3</option>
                            <option value="4" <?php if( $rows_small_tablet == '4' ) { echo 'selected'; } ?>>4</option>
                        </select>
                    </div>
                </div>
                <div class="event-feed-for-eventbrite-metabox-col">
                    <p class="description">
                        <?php esc_html_e( 'Mobile devices', 'event-feed-for-eventbrite' ); ?>
                    </p>
                    <div class="select-wrapper">
                        <select id="<?php echo esc_attr( $this->plugin_name . '-rows_mobile' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[rows_mobile]' ); ?>" class="small-text choices-grid-rows">
                            <option value="1" <?php if( $rows_mobile == '1' || $rows_mobile == '' ) { echo 'selected'; } ?>>1</option>
                            <option value="2" <?php if( $rows_mobile == '2' ) { echo 'selected'; } ?>>2</option>
                            <option value="3" <?php if( $rows_mobile == '3' ) { echo 'selected'; } ?>>3</option>
                            <option value="4" <?php if( $rows_mobile == '4' ) { echo 'selected'; } ?>>4</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Number of events displayed -->
    <div class="event-feed-for-eventbrite-metabox-field">
        <div class="event-feed-for-eventbrite-metabox-label">
            <label for="<?php echo esc_attr( $this->plugin_name . '-events_limit' ); ?>">
                <?php esc_html_e( 'Displayed events', 'event-feed-for-eventbrite' ); ?>
            </label>
            <p class="description"><?php esc_html_e( 'Limit the number of displayed events.', 'event-feed-for-eventbrite'); ?></p>
        </div>
        <div class="event-feed-for-eventbrite-metabox-input no-padding">
            <div class="event-feed-for-eventbrite-metabox-cols">
                <div class="event-feed-for-eventbrite-metabox-col">
                    <p class="description">
                        <?php esc_html_e( 'Desktop computers', 'event-feed-for-eventbrite' ); ?>
                    </p>
                    <input type="number" min="1" id="<?php echo esc_attr( $this->plugin_name . '-events_limit' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[events_limit]' ); ?>" value="<?php if( $events_limit ) { echo $events_limit; } else { echo 6; } ?>" <?php if( $events_limit_no == true ) { echo 'readonly'; } ?>>
                    <input type="checkbox" id="<?php echo esc_attr( $this->plugin_name . '-events_limit_no' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[events_limit_no]' ); ?>" value="1" <?php if( $events_limit_no == true ) { echo 'checked'; } ?>>
                    <label for="<?php echo esc_attr( $this->plugin_name . '-events_limit_no' ); ?>"><?php esc_html_e( 'No limit', 'event-feed-for-eventbrite' ); ?></label>
                </div>
                <div class="event-feed-for-eventbrite-metabox-col">
                    <p class="description">
                        <?php esc_html_e( 'Large tablet devices', 'event-feed-for-eventbrite' ); ?>
                    </p>
                    <input type="number" min="1" id="<?php echo esc_attr( $this->plugin_name . '-events_limit_large_tablet' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[events_limit_large_tablet]' ); ?>" value="<?php if( $events_limit_large_tablet ) { echo $events_limit_large_tablet; } else { echo 6; } ?>" <?php if( $events_limit_no_large_tablet == true ) { echo 'readonly'; } ?>>
                    <input type="checkbox" id="<?php echo esc_attr( $this->plugin_name . '-events_limit_no_large_tablet' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[events_limit_no_large_tablet]' ); ?>" value="1" <?php if( $events_limit_no_large_tablet == true ) { echo 'checked'; } ?>>
                    <label for="<?php echo esc_attr( $this->plugin_name . '-events_limit_no_large_tablet' ); ?>"><?php esc_html_e( 'No limit', 'event-feed-for-eventbrite' ); ?></label>
                </div>
                <div class="event-feed-for-eventbrite-metabox-col">
                    <p class="description">
                        <?php esc_html_e( 'Small tablet devices', 'event-feed-for-eventbrite' ); ?>
                    </p>
                    <input type="number" min="1" id="<?php echo esc_attr( $this->plugin_name . '-events_limit_small_tablet' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[events_limit_small_tablet]' ); ?>" value="<?php if( $events_limit_small_tablet ) { echo $events_limit_small_tablet; } else { echo 6; } ?>" <?php if( $events_limit_no_small_tablet == true ) { echo 'readonly'; } ?>>
                    <input type="checkbox" id="<?php echo esc_attr( $this->plugin_name . '-events_limit_no_small_tablet' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[events_limit_no_small_tablet]' ); ?>" value="1" <?php if( $events_limit_no_small_tablet == true ) { echo 'checked'; } ?>>
                    <label for="<?php echo esc_attr( $this->plugin_name . '-events_limit_no_small_tablet' ); ?>"><?php esc_html_e( 'No limit', 'event-feed-for-eventbrite' ); ?></label>
                </div>
                <div class="event-feed-for-eventbrite-metabox-col">
                    <p class="description">
                        <?php esc_html_e( 'Mobile devices', 'event-feed-for-eventbrite' ); ?>
                    </p>
                    <input type="number" min="1" id="<?php echo esc_attr( $this->plugin_name . '-events_limit_mobile' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[events_limit_mobile]' ); ?>" value="<?php if( $events_limit_mobile ) { echo $events_limit_mobile; } else { echo 6; } ?>" <?php if( $events_limit_no_mobile == true ) { echo 'readonly'; } ?>>
                    <input type="checkbox" id="<?php echo esc_attr( $this->plugin_name . '-events_limit_no_mobile' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[events_limit_no_mobile]' ); ?>" value="1" <?php if( $events_limit_no_mobile == true ) { echo 'checked'; } ?>>
                    <label for="<?php echo esc_attr( $this->plugin_name . '-events_limit_no_mobile' ); ?>"><?php esc_html_e( 'No limit', 'event-feed-for-eventbrite' ); ?></label>
                </div>
            </div>
        </div>
    </div>

    <!-- Theme color -->
    <div class="event-feed-for-eventbrite-metabox-field">
        <div class="event-feed-for-eventbrite-metabox-label">
            <label for="<?php echo esc_attr( $this->plugin_name . '-theme_color' ); ?>">
                <?php esc_html_e( 'Theme Color', 'event-feed-for-eventbrite'); ?>
            </label>
            <p class="description"><?php esc_html_e( 'It is used to highlight elements such as event dates, buttons, and links.', 'event-feed-for-eventbrite' ); ?></p>
        </div>
        <div class="event-feed-for-eventbrite-metabox-input">
            <input type="text" class="color-field small-text" id="<?php echo esc_attr( $this->plugin_name . '-theme_color' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[theme_color]' ); ?>" value="<?php if( $theme_color ) { echo sanitize_hex_color( $theme_color ); } else { echo '#334ECD'; } ?>">
        </div>
    </div>

</div>