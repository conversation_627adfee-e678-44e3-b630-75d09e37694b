<?php

// Get the event feed post meta
$popup              = get_post_meta( $post->ID, 'popup', true );
$calendar_button    = get_post_meta( $post->ID, 'calendar_button', true );
$google_map         = get_post_meta( $post->ID, 'google_map', true );
$organizer_info     = get_post_meta( $post->ID, 'organizer_info', true );
$eventbrite_link    = get_post_meta( $post->ID, 'eventbrite_link', true );

?>

<div class="event-feed-for-eventbrite-metabox-content event-feed-for-eventbrite-popup-metabox">

    <!-- Popup -->
    <div class="event-feed-for-eventbrite-metabox-field">
        <div class="event-feed-for-eventbrite-metabox-label">
            <label for="<?php echo esc_attr( $this->plugin_name . '-popup' ); ?>" class="event-feed-for-eventbrite-switch-label effe-pro-feature" data-service="<?php esc_attr_e( 'Event Details Popup', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>">
                <?php esc_html_e( 'Show event details in a popup', 'event-feed-for-eventbrite' ); ?>
            </label>
            <p class="description"><?php esc_html_e( 'Show popup with all necessary event details after clicking on the title, image, or view details button.', 'event-feed-for-eventbrite' ); ?></p>
        </div>
        <div class="event-feed-for-eventbrite-metabox-input">
            <input class="event-feed-for-eventbrite-switch-input" type="checkbox" id="<?php echo esc_attr( $this->plugin_name . '-popup' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[popup]' ); ?>" <?php if( $popup == true || ( get_post_status ( $post->ID ) === 'auto-draft' && ( $this->freemius->can_use_premium_code() && ! $this->freemius->is_free_plan() ) ) ) echo 'checked'; ?> <?php if( ! $this->freemius->can_use_premium_code() || $this->freemius->is_free_plan() ) { echo 'readonly'; } ?>>
            <div class="event-feed-for-eventbrite-switch <?php if( $popup == true || ( get_post_status ( $post->ID ) === 'auto-draft' && ( $this->freemius->can_use_premium_code() && ! $this->freemius->is_free_plan() ) ) ) echo '-on'; ?> effe-pro-feature" data-service="<?php esc_attr_e( 'Event Details Popup', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>">
                <span class="event-feed-for-eventbrite-switch-on"><?php esc_html_e( 'Yes', 'event-feed-for-eventbrite' ); ?></span>
                <span class="event-feed-for-eventbrite-switch-off"><?php esc_html_e( 'No', 'event-feed-for-eventbrite' ); ?></span>
                <div class="event-feed-for-eventbrite-slider"></div>
            </div>
            <p class="description"><?php esc_html_e( 'It allows your visitors a seamless experience without leaving the site. If not checked, after clicking on the event card, visitors will be redirected to Eventbrite.', 'event-feed-for-eventbrite' ); ?></p>
        
            <?php if( ( ! $this->freemius->can_use_premium_code() || $this->freemius->is_free_plan() ) && $popup == true ) { ?>
            <div class="event-feed-for-eventbrite-warning">
                <div>
                    <div><?php
                    printf(
                        '%s <strong>%s</strong>. <br>%s <a href="' . esc_url( $this->freemius->get_upgrade_url() ) . '">' . esc_html__( 'renew your license', 'event-feed-for-eventbrite' ) . '</a>.',
                        esc_html__( 'Event details popup is not displaying, because', 'event-feed-for-eventbrite' ) ,
                        esc_html__( 'your premium license is not active', 'event-feed-for-eventbrite' ),
                        esc_html__( 'To continue using this feature, please', 'event-feed-for-eventbrite' )
                    );
                    ?></div>
                </div>
            </div>
            <?php } ?>
            
        </div>
    </div>

    <!-- Displayed information -->
    <div class="event-feed-for-eventbrite-metabox-field">
        <div class="event-feed-for-eventbrite-metabox-label">
            <label class="effe-pro-feature" data-service="<?php esc_attr_e( 'Event Details Popup', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>">
                <?php esc_html_e( 'Popup features', 'event-feed-for-eventbrite' ); ?>
            </label>
            <p class="description"><?php esc_html_e( 'Check which extra features you want to display in the popup.', 'event-feed-for-eventbrite' ); ?></p>    
        </div>
        <div class="event-feed-for-eventbrite-metabox-input">
            <fieldset>

                <!-- Add to calendar button -->
                <input type="checkbox" class="effe-pro-feature" id="<?php echo esc_attr( $this->plugin_name . '-calendar_button' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[calendar_button]' ); ?>" value="1" <?php if( $calendar_button == 1 || get_post_status ( $post->ID ) === 'auto-draft' ) { echo 'checked'; } ?> <?php if( ! $this->freemius->can_use_premium_code() || $this->freemius->is_free_plan() ) { echo 'readonly'; } ?> data-service="<?php esc_attr_e( 'Add to Calendar', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>">
                <label for="<?php echo $this->plugin_name . '-calendar_button'; ?>" class="effe-pro-feature" data-service="<?php _e( 'Add to Calendar', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>"><?php esc_html_e( 'Add to Calendar', 'event-feed-for-eventbrite' ); ?></label><br>

                <!-- Google map -->
                <input type="checkbox" class="effe-pro-feature" id="<?php echo esc_attr( $this->plugin_name . '-google_map' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[google_map]' ); ?>" value="1" <?php if( $google_map == 1 || get_post_status ( $post->ID ) === 'auto-draft' ) { echo 'checked'; } ?> <?php if( ! $this->freemius->can_use_premium_code() || $this->freemius->is_free_plan() ) { echo 'readonly'; } ?> data-service="<?php esc_attr_e( 'Google map', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>">
                <label for="<?php echo $this->plugin_name . '-google_map'; ?>" class="effe-pro-feature" data-service="<?php _e( 'Google map', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>"><?php esc_html_e( 'Google map', 'event-feed-for-eventbrite' ); ?></label><br>

                <!-- Event Organizer information -->
                <input type="checkbox" class="effe-pro-feature" id="<?php echo esc_attr( $this->plugin_name . '-organizer_info' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[organizer_info]' ); ?>" value="1" <?php if( $organizer_info == 1 || get_post_status ( $post->ID ) === 'auto-draft' ) { echo 'checked'; } ?> <?php if( ! $this->freemius->can_use_premium_code() || $this->freemius->is_free_plan() ) { echo 'readonly'; } ?> data-service="<?php esc_attr_e( 'Event Organizer Information', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>">
                <label for="<?php echo $this->plugin_name . '-organizer_info'; ?>" class="effe-pro-feature" data-service="<?php _e( 'Event Organizer Information', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>"><?php esc_html_e( 'Event Organizer Information', 'event-feed-for-eventbrite' ); ?></label><br>

                <!-- Link to Eventbrite -->
                <input type="checkbox" class="effe-pro-feature" id="<?php echo esc_attr( $this->plugin_name . '-eventbrite_link' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[eventbrite_link]' ); ?>" value="1" <?php if( $eventbrite_link == 1 ) { echo 'checked'; } ?> <?php if( ! $this->freemius->can_use_premium_code() || $this->freemius->is_free_plan() ) { echo 'readonly'; } ?> data-service="<?php esc_attr_e( 'Link to Eventbrite', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>">
                <label for="<?php echo $this->plugin_name . '-eventbrite_link'; ?>" class="effe-pro-feature" data-service="<?php _e( 'Link to Eventbrite', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>"><?php esc_html_e( 'Link to Eventbrite', 'event-feed-for-eventbrite' ); ?></label><br>

            </fieldset>
        </div>
    </div>

</div>