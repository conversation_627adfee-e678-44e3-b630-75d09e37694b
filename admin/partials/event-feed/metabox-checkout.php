<?php

// Get the event feed post meta
$link_to = get_post_meta( $post->ID, 'link_to', true );

?>

<div class="event-feed-for-eventbrite-metabox-content event-feed-for-eventbrite-checkout-metabox">

    <!-- Sign up behaviour -->
    <div class="event-feed-for-eventbrite-metabox-field">
        <div class="event-feed-for-eventbrite-metabox-label">
            <label for="<?php echo esc_attr( $this->plugin_name . '-link_to' ); ?>">
                <?php esc_html_e( 'Checkout behavior', 'event-feed-for-eventbrite' ); ?>
            </label>
            <p class="description"><?php esc_html_e('Choose what happens after the user clicks the Register/Buy ticket button.', 'event-feed-for-eventbrite'); ?></p>
        </div>
        <div class="event-feed-for-eventbrite-metabox-input">
            <div class="select-wrapper">
                <select id="<?php echo esc_attr( $this->plugin_name . '-link_to' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[link_to]' ); ?>" class="small-text choices-linkto">
                    <option value="popup" <?php if( $link_to == 'popup' ) { echo 'selected'; } ?>>
                        <?php esc_html_e( 'Open ticket popup (stay on website)', 'event-feed-for-eventbrite'); ?>
                    </option>
                    <option value="popup_eventbrite" <?php if( $link_to == 'popup_eventbrite' ) { echo 'selected'; } ?>>
                        <?php esc_html_e( 'Link to Eventbrite website (open popup there)', 'event-feed-for-eventbrite'); ?>
                    </option>
                </select>
            </div>
        </div>
    </div>

</div>