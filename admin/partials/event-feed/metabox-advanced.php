<?php

// Get the event feed post meta
$link_target_blank = get_post_meta( $post->ID, 'link_target_blank', true );
$css_id            = get_post_meta( $post->ID, 'css_id', true );
$css_classes       = get_post_meta( $post->ID, 'css_classes', true );

?>

<div class="event-feed-for-eventbrite-metabox-content event-feed-for-eventbrite-advanced-metabox">

    <!-- External links -->
    <div class="event-feed-for-eventbrite-metabox-field">
        <div class="event-feed-for-eventbrite-metabox-label">
            <label for="<?php echo esc_attr( $this->plugin_name . '-link_target_blank' ); ?>" class="event-feed-for-eventbrite-switch-label">
                <?php esc_html_e( 'Open links in a new tab', 'event-feed-for-eventbrite' ); ?>
            </label>
            <p class="description"><?php esc_html_e( 'Set external links to be opened in a new browser tab.', 'event-feed-for-eventbrite' ); ?></p>
        </div>
        <div class="event-feed-for-eventbrite-metabox-input">
            <input class="event-feed-for-eventbrite-switch-input" type="checkbox" id="<?php echo esc_attr( $this->plugin_name . '-link_target_blank' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[link_target_blank]' ); ?>" <?php if( $link_target_blank == true || get_post_status ( $post->ID ) === 'auto-draft' ) echo 'checked'; ?>>
            <div class="event-feed-for-eventbrite-switch <?php if( $link_target_blank == true || get_post_status ( $post->ID ) === 'auto-draft' ) echo '-on'; ?>">
                <span class="event-feed-for-eventbrite-switch-on"><?php esc_html_e( 'Yes', 'event-feed-for-eventbrite' ); ?></span>
                <span class="event-feed-for-eventbrite-switch-off"><?php esc_html_e( 'No', 'event-feed-for-eventbrite' ); ?></span>
                <div class="event-feed-for-eventbrite-slider"></div>
            </div>
        </div>
    </div>
    
    <!-- CSS ID -->
    <div class="event-feed-for-eventbrite-metabox-field">
        <div class="event-feed-for-eventbrite-metabox-label">
            <label for="<?php echo esc_attr( $this->plugin_name . '-css_id' ); ?>">
                <?php esc_html_e( 'CSS ID', 'event-feed-for-eventbrite' ); ?>
            </label>
            <p class="description"><?php esc_html_e( 'Add ID to apply custom CSS styling.', 'event-feed-for-eventbrite' ); ?></p>
        </div>
        <div class="event-feed-for-eventbrite-metabox-input">
            <input type="text" id="<?php echo esc_attr( $this->plugin_name . '-css_id' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[css_id]' ); ?>" value="<?php echo esc_attr( $css_id ); ?>">
        </div>
    </div>

    <!-- CSS classes -->
    <div class="event-feed-for-eventbrite-metabox-field">
        <div class="event-feed-for-eventbrite-metabox-label">
            <label for="<?php echo esc_attr( $this->plugin_name . '-css_classes' ); ?>">
                <?php esc_html_e( 'CSS Classes', 'event-feed-for-eventbrite' ); ?>
            </label>
            <p class="description"><?php esc_html_e( 'Add classes to apply custom CSS styling.', 'event-feed-for-eventbrite' ); ?></p>
        </div>
        <div class="event-feed-for-eventbrite-metabox-input">
            <input type="text" id="<?php echo esc_attr( $this->plugin_name . '-css_classes' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[css_classes]' ); ?>" value="<?php echo esc_attr( $css_classes ); ?>">
            <p class="description"><?php esc_html_e( 'You can separate multiple classes with a space.', 'event-feed-for-eventbrite' ); ?></p>
        </div>
    </div>

</div>