<?php

// Get the event feed post meta
$display_image               = get_post_meta( $post->ID, 'display_image', true );
$display_price               = get_post_meta( $post->ID, 'display_price', true );
$display_tickets             = get_post_meta( $post->ID, 'display_tickets', true );
$display_short_datetime      = get_post_meta( $post->ID, 'display_short_datetime', true );
$display_datetime            = get_post_meta( $post->ID, 'display_datetime', true );
$display_location            = get_post_meta( $post->ID, 'display_location', true );
$display_description         = get_post_meta( $post->ID, 'display_description', true );
$title_length                = get_post_meta( $post->ID, 'title_length', true );
$price_overlay               = get_post_meta( $post->ID, 'price_overlay', true );
$title_length_full           = get_post_meta( $post->ID, 'title_length_full', true );
$excerpt_length              = get_post_meta( $post->ID, 'excerpt_length', true );
$excerpt_length_full         = get_post_meta( $post->ID, 'excerpt_length_full', true );
$display_signup_button       = get_post_meta( $post->ID, 'display_signup_button', true );
$display_more_button         = get_post_meta( $post->ID, 'display_more_button', true );
$tickets_text                = get_post_meta( $post->ID, 'tickets_text', true );
$signup_button_text          = get_post_meta( $post->ID, 'signup_button_text', true );
$tickets_button_text         = get_post_meta( $post->ID, 'tickets_button_text', true );
$more_button_text            = get_post_meta( $post->ID, 'more_button_text', true );

// Conditionals
$layout_type                 = get_post_meta( $post->ID, 'layout_type', true );
$shortdate_conditionals      = [ 'list', 'grid', 'cards'];

?>

<div class="event-feed-for-eventbrite-metabox-content event-feed-for-eventbrite-details-metabox">

    <!-- Displayed information -->
    <div class="event-feed-for-eventbrite-metabox-field">
        <div class="event-feed-for-eventbrite-metabox-label">
            <label>
                <?php esc_html_e( 'Displayed information', 'event-feed-for-eventbrite' ); ?>
            </label>
            <p class="description"><?php esc_html_e( 'Check what event information you want to display on the event card.', 'event-feed-for-eventbrite' ); ?></p>    
        </div>
        <div class="event-feed-for-eventbrite-metabox-input">
            <fieldset> 

                <!-- Image -->
                <input type="checkbox" id="<?php echo esc_attr( $this->plugin_name . '-display_image' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[display_image]' ); ?>" value="1" <?php if( $display_image == 1 || get_post_status ( $post->ID ) === 'auto-draft' ) { echo 'checked'; } ?>>
                <label for="<?php echo $this->plugin_name . '-display_image'; ?>"><?php esc_html_e( 'Image', 'event-feed-for-eventbrite' ); ?></label><br>

                <!-- Price -->
                <input type="checkbox" id="<?php echo esc_attr( $this->plugin_name . '-display_price' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[display_price]' ); ?>" value="1" <?php if( $display_price == 1 || get_post_status ( $post->ID ) === 'auto-draft' ) { echo 'checked'; } ?>>
                <label for="<?php echo $this->plugin_name . '-display_price'; ?>"><?php esc_html_e( 'Price', 'event-feed-for-eventbrite' ); ?></label><br>
                
                <!-- Remaining tickets -->
                <input type="checkbox" id="<?php echo esc_attr( $this->plugin_name . '-display_tickets' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[display_tickets]' ); ?>" value="1" <?php if( $display_tickets == 1 || get_post_status ( $post->ID ) === 'auto-draft' ) { echo 'checked'; } ?>>
                <label for="<?php echo $this->plugin_name . '-display_tickets'; ?>"><?php esc_html_e( 'Remaining tickets', 'event-feed-for-eventbrite' ); ?></label><br>

                <!-- Short date -->
                <span class="<?php if( ( ! in_array( $layout_type, $shortdate_conditionals ) ) && $layout_type !== '' ) { echo 'hidden'; } ?>" data-condition-element="<?php echo esc_attr( $this->plugin_name . '[layout_type]' ); ?>" data-condition-type="radio" data-condition-value="<?php echo esc_attr( htmlspecialchars( json_encode( $shortdate_conditionals ) ) ); ?>">
                    <input type="checkbox" id="<?php echo esc_attr( $this->plugin_name . '-display_short_datetime' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[display_short_datetime]' ); ?>" value="1" <?php if( $display_short_datetime == 1 || get_post_status ( $post->ID ) === 'auto-draft' ) { echo 'checked'; } ?>>
                    <label for="<?php echo esc_attr( $this->plugin_name . '-display_short_datetime' ); ?>"><?php esc_html_e( 'Short date', 'event-feed-for-eventbrite' ); ?></label>
                </span><br>

                <!-- Full date and time -->
                <input type="checkbox" id="<?php echo esc_attr( $this->plugin_name . '-display_datetime' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[display_datetime]' ); ?>" value="1" <?php if( $display_datetime == 1 || get_post_status ( $post->ID ) === 'auto-draft' ) { echo 'checked'; } ?>>
                <label for="<?php echo $this->plugin_name . '-display_datetime'; ?>"><?php esc_html_e( 'Full date and time', 'event-feed-for-eventbrite' ); ?></label><br>

                <!-- Location -->
                <input type="checkbox" id="<?php echo esc_attr( $this->plugin_name . '-display_location' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[display_location]' ); ?>" value="1" <?php if( $display_location == 1 || get_post_status ( $post->ID ) === 'auto-draft' ) { echo 'checked'; } ?>>
                <label for="<?php echo $this->plugin_name . '-display_location'; ?>"><?php esc_html_e( 'Location', 'event-feed-for-eventbrite' ); ?></label><br>

                <!-- Description -->
                <input type="checkbox" id="<?php echo esc_attr( $this->plugin_name . '-display_description' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[display_description]' ); ?>" value="1" <?php if( $display_description == 1 || get_post_status ( $post->ID ) === 'auto-draft' ) { echo 'checked'; } ?>>
                <label for="<?php echo $this->plugin_name . '-display_description'; ?>"><?php esc_html_e( 'Description', 'event-feed-for-eventbrite' ); ?></label><br>

                <!-- Sign up button -->
                <input type="checkbox" id="<?php echo esc_attr( $this->plugin_name . '-display_signup_button' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[display_signup_button]' ); ?>" value="1" <?php if( $display_signup_button == 1 || get_post_status ( $post->ID ) === 'auto-draft' ) { echo 'checked'; } ?>>
                <label for="<?php echo $this->plugin_name . '-display_signup_button'; ?>"><?php esc_html_e( 'Register/Buy tickets button', 'event-feed-for-eventbrite' ); ?></label><br>

                <!-- View details button -->
                <input type="checkbox" id="<?php echo esc_attr( $this->plugin_name . '-display_more_button' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[display_more_button]' ); ?>" value="1" <?php if( $display_more_button == 1 || get_post_status ( $post->ID ) === 'auto-draft' ) { echo 'checked'; } ?>>
                <label for="<?php echo $this->plugin_name . '-display_more_button'; ?>"><?php esc_html_e( 'View details button', 'event-feed-for-eventbrite' ); ?></label>

            </fieldset>
        </div>
    </div>
    
    <!-- Tags overlay -->
    <div class="event-feed-for-eventbrite-metabox-field <?php if( ( $display_image != true ) && get_post_status ( $post->ID ) !== 'auto-draft' ) { echo 'hidden'; } ?>">
        <div class="event-feed-for-eventbrite-metabox-label">
            <label for="<?php echo esc_attr( $this->plugin_name . '-price_overlay' ); ?>" class="event-feed-for-eventbrite-switch-label">
                <?php esc_html_e( 'Display tags as an image overlay', 'event-feed-for-eventbrite' ); ?>
            </label>
            <p class="description"><?php esc_html_e( 'Display the price and remaining tickets on top of the event image.' , 'event-feed-for-eventbrite' ); ?></p>
        </div>
        <div class="event-feed-for-eventbrite-metabox-input">
            <input class="event-feed-for-eventbrite-switch-input" type="checkbox" id="<?php echo esc_attr( $this->plugin_name . '-price_overlay' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[price_overlay]' ); ?>" <?php if( $price_overlay == true ) echo 'checked'; ?>>
            <div class="event-feed-for-eventbrite-switch <?php if( $price_overlay == true ) echo '-on'; ?>">
                <span class="event-feed-for-eventbrite-switch-on"><?php esc_html_e( 'Yes', 'event-feed-for-eventbrite' ); ?></span>
                <span class="event-feed-for-eventbrite-switch-off"><?php esc_html_e( 'No', 'event-feed-for-eventbrite' ); ?></span>
                <div class="event-feed-for-eventbrite-slider"></div>
            </div>
        </div>
    </div>

    <!-- Title length -->
    <div class="event-feed-for-eventbrite-metabox-field">
        <div class="event-feed-for-eventbrite-metabox-label">
            <label for="<?php echo esc_attr( $this->plugin_name . '-title_length' ); ?>">
                <?php esc_html_e( 'Title length', 'event-feed-for-eventbrite' ); ?>
            </label>
            <p class="description"><?php esc_html_e( 'A maximum number of characters.' , 'event-feed-for-eventbrite' ); ?></p>
        </div>
        <div class="event-feed-for-eventbrite-metabox-input">
            <input type="number" id="<?php echo esc_attr( $this->plugin_name . '-title_length' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[title_length]' ); ?>" value="<?php if( $title_length ) { echo esc_attr( $title_length ); } else { echo 55; } ?>" <?php if( $title_length_full == true ) { echo 'readonly'; } ?>>
            <input type="checkbox" id="<?php echo esc_attr( $this->plugin_name . '-title_length_full' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[title_length_full]' ); ?>" value="1" <?php if( $title_length_full == true ) { echo 'checked'; } ?>>
            <label for="<?php echo esc_attr( $this->plugin_name . '-title_length_full' ); ?>"><?php esc_html_e( 'Show full title', 'event-feed-for-eventbrite' ); ?></label>
        </div>
    </div>

    <!-- Description length -->
    <div class="event-feed-for-eventbrite-metabox-field <?php if( $display_description != true && get_post_status ( $post->ID ) !== 'auto-draft' ) { echo 'hidden'; } ?>" data-condition-element="<?php echo esc_attr( $this->plugin_name . '[display_description]' ); ?>" data-condition-type="checkbox">
        <div class="event-feed-for-eventbrite-metabox-label">
            <label for="<?php echo esc_attr( $this->plugin_name . '-excerpt_length' ); ?>">
                <?php esc_html_e( 'Description length', 'event-feed-for-eventbrite' ); ?>
            </label>
            <p class="description"><?php esc_html_e( 'A maximum number of characters.' , 'event-feed-for-eventbrite' ); ?></p>
        </div>
        <div class="event-feed-for-eventbrite-metabox-input">
            <input type="number" id="<?php echo esc_attr( $this->plugin_name . '-excerpt_length' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[excerpt_length]' ); ?>" value="<?php if( $excerpt_length ) { echo $excerpt_length; } else { echo 120; } ?>" <?php if( $excerpt_length_full == true ) { echo 'readonly'; } ?>>
            <input type="checkbox" id="<?php echo esc_attr( $this->plugin_name . '-excerpt_length_full' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[excerpt_length_full]' ); ?>" value="1" <?php if( $excerpt_length_full == true ) { echo 'checked'; } ?>>
            <label for="<?php echo esc_attr( $this->plugin_name . '-excerpt_length_full' ); ?>"><?php esc_html_e( 'Show full description', 'event-feed-for-eventbrite' ); ?></label>
        </div>
    </div>

    <!-- Remaining tickets text -->
    <div class="event-feed-for-eventbrite-metabox-field <?php if( $display_tickets != true && get_post_status ( $post->ID ) !== 'auto-draft' ) { echo 'hidden'; } ?>" data-condition-element="<?php echo esc_attr( $this->plugin_name . '[display_tickets]' ); ?>" data-condition-type="checkbox">
        <div class="event-feed-for-eventbrite-metabox-label">
            <label for="<?php echo esc_attr( $this->plugin_name . '-tickets_text' ); ?>">
                <?php esc_html_e( 'Remaining tickets Text', 'event-feed-for-eventbrite' ); ?>
            </label>
            <p class="description"><?php esc_html_e( 'Appended to the number of tickets.', 'event-feed-for-eventbrite' ); ?></p>
        </div>
        <div class="event-feed-for-eventbrite-metabox-input">
            <input type="text" id="<?php echo esc_attr( $this->plugin_name . '-tickets_text' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[tickets_text]' ); ?>" value="<?php if( $tickets_text ) { echo $tickets_text; } else { esc_html_e( 'Tickets left', 'event-feed-for-eventbrite' ); } ?>">
        </div>
    </div>

    <!-- Register button text -->
    <div class="event-feed-for-eventbrite-metabox-field <?php if( $display_signup_button != true && get_post_status ( $post->ID ) !== 'auto-draft' ) { echo 'hidden'; } ?>" data-condition-element="<?php echo esc_attr( $this->plugin_name . '[display_signup_button]' ); ?>" data-condition-type="checkbox">
        <div class="event-feed-for-eventbrite-metabox-label">
            <label for="<?php echo esc_attr( $this->plugin_name . '-signup_button_text' ); ?>">
                <?php esc_html_e( 'Register Button Text', 'event-feed-for-eventbrite' ); ?>
            </label>
            <p class="description"><?php esc_html_e( 'Used on free events.', 'event-feed-for-eventbrite' ); ?></p>
        </div>
        <div class="event-feed-for-eventbrite-metabox-input">
            <input type="text" id="<?php echo esc_attr( $this->plugin_name . '-signup_button_text' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[signup_button_text]' ); ?>" value="<?php if( $signup_button_text ) { echo $signup_button_text; } else { esc_html_e( 'Register', 'event-feed-for-eventbrite' ); } ?>">
        </div>
    </div>

    <!-- Get tickets button text -->
    <div class="event-feed-for-eventbrite-metabox-field <?php if( $display_signup_button != true && get_post_status ( $post->ID ) !== 'auto-draft' ) { echo 'hidden'; } ?>" data-condition-element="<?php echo esc_attr( $this->plugin_name . '[display_signup_button]' ); ?>" data-condition-type="checkbox">
        <div class="event-feed-for-eventbrite-metabox-label">
            <label for="<?php echo esc_attr( $this->plugin_name . '-tickets_button_text' ); ?>">
                <?php esc_html_e( 'Buy tickets Button Text', 'event-feed-for-eventbrite' ); ?>
            </label>
            <p class="description"><?php esc_html_e( 'Used on paid/donation events.', 'event-feed-for-eventbrite' ); ?></p>
        </div>
        <div class="event-feed-for-eventbrite-metabox-input">
            <input type="text" id="<?php echo esc_attr( $this->plugin_name . '-tickets_button_text' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[tickets_button_text]' ); ?>" value="<?php if( $tickets_button_text ) { echo $tickets_button_text; } else { esc_html_e( 'Buy tickets', 'event-feed-for-eventbrite' ); } ?>">
        </div>
    </div>

    <!-- Read more link text -->
    <div class="event-feed-for-eventbrite-metabox-field <?php if( $display_more_button != true && get_post_status ( $post->ID ) !== 'auto-draft' ) { echo 'hidden'; } ?>" data-condition-element="<?php echo esc_attr( $this->plugin_name . '[display_more_button]' ); ?>" data-condition-type="checkbox">
        <div class="event-feed-for-eventbrite-metabox-label">
            <label for="<?php echo esc_attr( $this->plugin_name . '-more_button_text' ); ?>">
                <?php esc_html_e( 'View details Button Text', 'event-feed-for-eventbrite' ); ?>
            </label>
        </div>
        <div class="event-feed-for-eventbrite-metabox-input">
            <input type="text" id="<?php echo esc_attr( $this->plugin_name . '-more_button_text' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[more_button_text]' ); ?>" value="<?php if( $more_button_text ) { echo $more_button_text; } else { esc_html_e( 'View details', 'event-feed-for-eventbrite' ); } ?>">
        </div>
    </div>

</div>