<?php

// Get Eventbrite organizations available with the user's API key
$safe_user_organizations = [];
if( isset( $_GET['organizations'] ) ) {
    foreach ( $_GET['organizations'] as $user_organization ) {
        $safe_user_organization_id = intval( $user_organization->id );
        $safe_user_organization_name = sanitize_text_field( $user_organization->name );
        $safe_object = (object) [ 'id' => $safe_user_organization_id, 'name' => $safe_user_organization_name ];
        array_push( $safe_user_organizations, $safe_object );
    }
}
$user_organizations = $safe_user_organizations;

// Get the Eventbrite organization that was set for this event feed
$organization = ( false !== get_post_meta( $post->ID, 'organization', true ) ) ? json_decode( get_post_meta( $post->ID, 'organization', true ) ) : '';

?>

<div class="event-feed-for-eventbrite-metabox-content event-feed-for-eventbrite-display-metabox">

    <!-- Organization -->
    <div class="event-feed-for-eventbrite-metabox-field">
        <div class="event-feed-for-eventbrite-metabox-label">
            <label for="<?php echo esc_attr( $this->plugin_name . '-organization' ); ?>">
                <?php esc_html_e( 'Organization', 'event-feed-for-eventbrite' ); ?>
            </label>
            <p class="description"><?php esc_html_e( 'Choose from which organization you want to show events in this feed.', 'event-feed-for-eventbrite' ); ?></p>
        </div>
        <div class="event-feed-for-eventbrite-metabox-input">

            <?php
            $user_organizations_ids = [];

            if( $user_organizations ) {

                // Get IDs of all organizations available with the current Eventbrite API key
                foreach( $user_organizations as $user_organization ) {
                    array_push( $user_organizations_ids, $user_organization->id );
                }

                // Empty organization ID (new feed)
                if( $organization == '' ) {
                    ?>
                    <div class="select-wrapper">
                        <select id="<?php echo esc_attr( $this->plugin_name . '-organization' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[organization]' ); ?>" class="small-text choices-organization">
                        <?php
                        $i = 0;
                        foreach( $user_organizations as $user_organization ) {
                            $i++;
                            ?>
                                <option value="<?php echo esc_attr( $user_organization->id ); ?>" <?php if( $i == 1 ) { echo 'selected'; } ?>>
                                <?php
                                if( $user_organization->name ) { 
                                    echo esc_html( $user_organization->name );
                                } else {
                                    esc_html_e( 'Default organization', 'event-feed-for-eventbrite' );
                                }
                                echo ' ' . '(' . esc_html( $user_organization->id ) . ')';
                                ?>
                            </option>
                            <?php
                        }
                        ?>
                        </select>
                    </div>
                    <?php
                    
                // The chosen organization is available with the current API key
                } elseif( in_array( $organization, $user_organizations_ids ) ) {
                    ?>
                    <div class="select-wrapper">
                        <select id="<?php echo esc_attr( $this->plugin_name . '-organization' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[organization]' ); ?>" class="small-text choices-organization">
                        <?php
                        foreach( $user_organizations as $user_organization ) {
                            ?>
                            <option value="<?php echo esc_attr( $user_organization->id ); ?>" <?php if( $organization == $user_organization->id ) { echo 'selected'; } ?>>
                                <?php
                                if( $user_organization->name ) { 
                                    echo esc_html( $user_organization->name );
                                } else {
                                    esc_html_e( 'Default organization', 'event-feed-for-eventbrite' );
                                }
                                echo ' ' . '(' . esc_html( $user_organization->id ) . ')';
                                ?>
                            </option>
                            <?php
                        }
                        ?>
                        </select>
                    </div>
                    <?php

                // The chosen organization is NOT available with current API key
                } else {
                    ?>
                    <div class="select-wrapper">
                        <select id="<?php echo esc_attr( $this->plugin_name . '-organization' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[organization]' ); ?>" class="small-text choices-organization">
                            <option value="<?php echo esc_attr( $organization ); ?>" selected>
                                <?php esc_html_e( 'Choose your Eventbrite organization', 'event-feed-for-eventbrite' ); ?>
                            </option>
                            <?php
                            foreach( $user_organizations as $user_organization ) {
                                ?>
                                <option value="<?php echo esc_attr( $user_organization->id ); ?>" <?php if( $organization == $user_organization->id ) { echo 'selected'; } ?>>
                                    <?php
                                    if( $user_organization->name ) { 
                                        echo esc_html( $user_organization->name );
                                    } else {
                                        esc_html_e( 'Default organization', 'event-feed-for-eventbrite' );
                                    }
                                    echo ' ' . '(' . esc_html( $user_organization->id ) . ')';
                                    ?>
                                </option>
                                <?php
                            }
                            ?>
                        </select>
                    </div>
                    <div class="event-feed-for-eventbrite-warning">
                        <div>
                            <div><?php
                            printf(
                                '%s <strong>%s</strong>.',
                                esc_html__( 'Your Eventbrite API key has changed.', 'event-feed-for-eventbrite' ) ,
                                esc_html__( 'Please review the selected organization', 'event-feed-for-eventbrite' )
                            );
                            ?></div>
                        </div>
                    </div>
                    <?php  
                }
            
            // Eventbrite API key is not set in the plugin's settings
            } else {
                ?>
                <div class="select-wrapper">
                    <select id="<?php echo esc_attr( $this->plugin_name . '-organization' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[organization]' ); ?>" class="small-text choices-organization" disabled>
                        <option value="">
                            <?php esc_html_e( 'Organization name (ID)', 'event-feed-for-eventbrite' ); ?>
                        </option>
                    </select>
                </div>
                <div class="event-feed-for-eventbrite-warning">
                    <div>
                        <div><?php
                        printf(
                            '%s <a href="%s">%s</a>. %s.',
                            esc_html__( 'Please', 'event-feed-for-eventbrite' ),
                            esc_url( admin_url( 'edit.php?post_type=event_feed&page=' . $this->plugin_name . '-settings' ) ),
                            esc_html__( 'enter your Eventbrite API key on the plugin settings page', 'event-feed-for-eventbrite' ),
                            esc_html__( "Without this step, the plugin can't get any information from Eventbrite", 'event-feed-for-eventbrite' )
                        );
                        ?></div>
                    </div>
                </div>
            <?php
            }
            ?>

        </div>
    </div>
    
</div>