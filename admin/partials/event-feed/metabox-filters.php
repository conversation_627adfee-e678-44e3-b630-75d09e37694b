<?php

// Get Eventbrite organizers available with the user's API key
$safe_user_organizers = [];
if( isset( $_GET['organizers'] ) && is_array( $_GET['organizers'] ) ) {
    foreach ( $_GET['organizers'] as $user_organizer ) {
        $safe_user_organizer_id = intval( $user_organizer->id );
        $safe_user_organizer_name = sanitize_text_field( $user_organizer->name );
        $safe_object = (object) [ 'id' => $safe_user_organizer_id, 'name' => $safe_user_organizer_name ];
        array_push( $safe_user_organizers, $safe_object );
    }
}
$user_organizers = $safe_user_organizers;

// Get the event feed post meta
$display_public             = get_post_meta( $post->ID, 'display_public', true );
$display_private            = get_post_meta( $post->ID, 'display_private', true );
$display_venue              = get_post_meta( $post->ID, 'display_venue', true );
$display_online             = get_post_meta( $post->ID, 'display_online', true );
$time_filter                = get_post_meta( $post->ID, 'time_filter', true );
$name_filter                = get_post_meta( $post->ID, 'name_filter', true );
$location_filter            = get_post_meta( $post->ID, 'location_filter', true );
$description_filter         = get_post_meta( $post->ID, 'description_filter', true );
$organizer_filter           = ( false !== get_post_meta( $post->ID, 'organizer_filter', true ) ) ? get_post_meta( $post->ID, 'organizer_filter', true ) : '';

?>

<div class="event-feed-for-eventbrite-metabox-content event-feed-for-eventbrite-filters-metabox">

    <!-- Time Filter -->
    <div class="event-feed-for-eventbrite-metabox-field">
        <div class="event-feed-for-eventbrite-metabox-label">
            <label class="effe-pro-feature" data-service="<?php esc_attr_e( 'Time Filter', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>">
                <?php esc_html_e( 'Time Filter', 'event-feed-for-eventbrite' ); ?>
            </label>
            <p class="description"><?php esc_html_e( 'Choose if you want to display current, past, or all events in this feed.', 'event-feed-for-eventbrite' ); ?></p>
        </div>

        <div class="event-feed-for-eventbrite-metabox-input">

            <div class="select-wrapper effe-pro-feature" data-service="<?php esc_attr_e( 'Time Filter', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>">
                <select id="<?php echo esc_attr( $this->plugin_name . '-time_filter' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[time_filter]' ); ?>" class="small-text choices-time-filter" <?php if( ! $this->freemius->can_use_premium_code() || $this->freemius->is_free_plan() ) { echo 'disabled'; } ?>>
                    <option value="current_future" <?php if( $time_filter == 'current_future' ) { echo 'selected'; } ?>>
                        <?php esc_html_e( 'Current and future events', 'event-feed-for-eventbrite'); ?>
                    </option>
                    <option value="past" <?php if( $time_filter == 'past' ) { echo 'selected'; } ?>>
                        <?php esc_html_e( 'Past events', 'event-feed-for-eventbrite'); ?>
                    </option>
                    <option value="all" <?php if( $time_filter == 'all' ) { echo 'selected'; } ?>>
                        <?php esc_html_e( 'All events', 'event-feed-for-eventbrite'); ?>
                    </option>
                </select>
            </div>

            <?php if( ( ! $this->freemius->can_use_premium_code() || $this->freemius->is_free_plan() ) && ( get_post_status ( $post->ID ) !== 'auto-draft' ) && ( $time_filter == 'past' || $time_filter == 'all' ) ) { ?>
            <div class="event-feed-for-eventbrite-warning">
                <div>
                    <div><?php
                    printf(
                        '%s <strong>%s</strong>. <br>%s <a href="' . esc_url( $this->freemius->get_upgrade_url() ) . '">' . esc_html__( 'renew your license', 'event-feed-for-eventbrite' ) . '</a>.',
                        esc_html__( 'Time filter can\'t be applied, because', 'event-feed-for-eventbrite' ) ,
                        esc_html__( 'your premium license is not active', 'event-feed-for-eventbrite' ),
                        esc_html__( 'To continue using this feature, please', 'event-feed-for-eventbrite' )
                    );
                    ?></div>
                </div>
            </div>
            <?php } ?>

        </div>  
    </div>

    <!-- Privacy Filter -->
    <div class="event-feed-for-eventbrite-metabox-field">
        <div class="event-feed-for-eventbrite-metabox-label">
            <label>
                <?php esc_html_e( 'Privacy Filter', 'event-feed-for-eventbrite' ); ?>
            </label>
            <p class="description"><?php esc_html_e( 'Choose if you want to display public or/and private events.', 'event-feed-for-eventbrite' ); ?></p>
        </div>
        <div class="event-feed-for-eventbrite-metabox-input">

            <fieldset>

                <!-- Public Events -->
                <input type="checkbox" class="effe-pro-feature" id="<?php echo esc_attr( $this->plugin_name . '-display_public' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[display_public]' ); ?>" value="1" <?php if( $display_public == 1 || get_post_status ( $post->ID ) === 'auto-draft' ) { echo 'checked'; } ?> <?php if( ! $this->freemius->can_use_premium_code() || $this->freemius->is_free_plan() ) { echo 'readonly'; } ?> data-service="<?php esc_attr_e( 'Privacy Filter', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>">
                <label for="<?php echo esc_attr( $this->plugin_name . '-display_public' ); ?>" class="effe-pro-feature" data-service="<?php esc_attr_e( 'Privacy Filter', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>"><?php esc_html_e( 'Public Events', 'event-feed-for-eventbrite' ); ?></label><br>

                <!-- Private Events -->
                <input type="checkbox" class="effe-pro-feature" id="<?php echo esc_attr( $this->plugin_name . '-display_private' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[display_private]' ); ?>" value="1" <?php if( $display_private == 1 || get_post_status ( $post->ID ) === 'auto-draft' ) { echo 'checked'; } ?> <?php if( ! $this->freemius->can_use_premium_code() || $this->freemius->is_free_plan() ) { echo 'readonly'; } ?> data-service="<?php esc_attr_e( 'Privacy Filter', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>">
                <label for="<?php echo esc_attr( $this->plugin_name . '-display_private' ); ?>" class="effe-pro-feature" data-service="<?php esc_attr_e( 'Privacy Filter', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>"><?php esc_html_e( 'Private Events', 'event-feed-for-eventbrite' ); ?></label><br>

            </fieldset>

            <?php if( ( ! $this->freemius->can_use_premium_code() || $this->freemius->is_free_plan() ) && ( get_post_status ( $post->ID ) !== 'auto-draft' ) && ( $display_public == 0 || $display_private == 0 ) ) { ?>
            <div class="event-feed-for-eventbrite-warning">
                <div>
                    <div><?php
                    printf(
                        '%s <strong>%s</strong>. <br>%s <a href="' . esc_url( $this->freemius->get_upgrade_url() ) . '">' . esc_html__( 'renew your license', 'event-feed-for-eventbrite' ) . '</a>.',
                        esc_html__( 'Privacy filter can\'t be applied, because', 'event-feed-for-eventbrite' ) ,
                        esc_html__( 'your premium license is not active', 'event-feed-for-eventbrite' ),
                        esc_html__( 'To continue using this feature, please', 'event-feed-for-eventbrite' )
                    );
                    ?></div>
                </div>
            </div>
            <?php } ?>

        </div>
    </div>

    <!-- Venue Filter -->
    <div class="event-feed-for-eventbrite-metabox-field">
        <div class="event-feed-for-eventbrite-metabox-label">
            <label class="effe-pro-feature" data-service="<?php esc_attr_e( 'Venue Filter', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>">
                <?php esc_html_e( 'Venue Filter', 'event-feed-for-eventbrite' ); ?>
            </label>
            <p class="description"><?php esc_html_e( 'Choose if you want to display events with a venue or/and online events.', 'event-feed-for-eventbrite' ); ?></p>
        </div>
        <div class="event-feed-for-eventbrite-metabox-input">

            <fieldset>

                <!-- Events with venue -->
                <input type="checkbox" class="effe-pro-feature" id="<?php echo esc_attr( $this->plugin_name . '-display_venue' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[display_venue]' ); ?>" value="1" <?php if( $display_venue == 1 || get_post_status ( $post->ID ) === 'auto-draft' ) { echo 'checked'; } ?> <?php if( ! $this->freemius->can_use_premium_code() || $this->freemius->is_free_plan() ) { echo 'readonly'; } ?> data-service="<?php esc_attr_e( 'Venue Filter', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>">
                <label for="<?php echo esc_attr( $this->plugin_name . '-display_venue' ); ?>" class="effe-pro-feature" data-service="<?php esc_attr_e( 'Venue Filter', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>"><?php esc_html_e( 'Events with Venue', 'event-feed-for-eventbrite' ); ?></label><br>

                <!-- Online events -->
                <input type="checkbox" class="effe-pro-feature" id="<?php echo esc_attr( $this->plugin_name . '-display_online' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[display_online]' ); ?>" value="1" <?php if( $display_online == 1 || get_post_status ( $post->ID ) === 'auto-draft' ) { echo 'checked'; } ?> <?php if( ! $this->freemius->can_use_premium_code() || $this->freemius->is_free_plan() ) { echo 'readonly'; } ?> data-service="<?php esc_attr_e( 'Venue Filter', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>">
                <label for="<?php echo esc_attr( $this->plugin_name . '-display_online' ); ?>" class="effe-pro-feature" data-service="<?php esc_attr_e( 'Venue Filter', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>"><?php esc_html_e( 'Online Events', 'event-feed-for-eventbrite' ); ?></label><br>

            </fieldset>

            <?php if( ( ! $this->freemius->can_use_premium_code() || $this->freemius->is_free_plan() ) && ( get_post_status ( $post->ID ) !== 'auto-draft' ) && ( $display_venue == 0 || $display_online == 0 ) ) { ?>
            <div class="event-feed-for-eventbrite-warning">
                <div>
                    <div><?php
                    printf(
                        '%s <strong>%s</strong>. <br>%s <a href="' . esc_url( $this->freemius->get_upgrade_url() ) . '">' . esc_html__( 'renew your license', 'event-feed-for-eventbrite' ) . '</a>.',
                        esc_html__( 'Venue filter can\'t be applied, because', 'event-feed-for-eventbrite' ) ,
                        esc_html__( 'your premium license is not active', 'event-feed-for-eventbrite' ),
                        esc_html__( 'To continue using this feature, please', 'event-feed-for-eventbrite' )
                    );
                    ?></div>
                </div>
            </div>
            <?php } ?>
            
        </div>        
    </div>

    <!-- Name Filter -->
    <div class="event-feed-for-eventbrite-metabox-field">
        <div class="event-feed-for-eventbrite-metabox-label">
            <label for="<?php echo esc_attr( $this->plugin_name . '-name_filter' ); ?>" class="effe-pro-feature" data-service="<?php esc_attr_e( 'Name Filter', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>">
                <?php esc_html_e( 'Name Filter', 'event-feed-for-eventbrite' ); ?>
            </label>
            <p class="description"><?php esc_html_e( 'Show only events whose name contains specific text.', 'event-feed-for-eventbrite' ); ?></p>
        </div>
        <div class="event-feed-for-eventbrite-metabox-input">
            <div>
                <input type="text" class="effe-pro-feature" id="<?php echo esc_attr( $this->plugin_name . '-name_filter' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[name_filter]' ); ?>" value="<?php echo esc_attr( $name_filter ); ?>" <?php if( ! $this->freemius->can_use_premium_code() || $this->freemius->is_free_plan() ) { echo 'readonly'; } ?> data-service="<?php esc_attr_e( 'Name Filter', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>">
                <p class="description"><?php esc_html_e( 'To match multiple terms, use | for OR logic (e.g., workshop|seminar) and + for AND logic (e.g., workshop+seminar).', 'event-feed-for-eventbrite' ); ?></p>
            </div>

            <?php if( ( ! $this->freemius->can_use_premium_code() || $this->freemius->is_free_plan() ) && $name_filter !== '' ) { ?>
            <div class="event-feed-for-eventbrite-warning">
                <div>
                    <div><?php
                    printf(
                        '%s <strong>%s</strong>. <br>%s <a href="' . esc_url( $this->freemius->get_upgrade_url() ) . '">' . esc_html__( 'renew your license', 'event-feed-for-eventbrite' ) . '</a>.',
                        esc_html__( 'Name filter can\'t be applied, because', 'event-feed-for-eventbrite' ) ,
                        esc_html__( 'your premium license is not active', 'event-feed-for-eventbrite' ),
                        esc_html__( 'To continue using this feature, please', 'event-feed-for-eventbrite' )
                    );
                    ?></div>
                </div>
            </div>
            <?php } ?>

        </div>     
    </div>

    <!-- Location Filter -->
    <div class="event-feed-for-eventbrite-metabox-field">
        <div class="event-feed-for-eventbrite-metabox-label">
            <label for="<?php echo esc_attr( $this->plugin_name . '-location_filter' ); ?>" class="effe-pro-feature" data-service="<?php esc_attr_e( 'Location Filter', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>">
                <?php esc_html_e( 'Location Filter', 'event-feed-for-eventbrite' ); ?>
            </label>
            <p class="description"><?php esc_html_e( 'Show only events whose location contains specific text.', 'event-feed-for-eventbrite' ); ?></p>
        </div>
        <div class="event-feed-for-eventbrite-metabox-input">
            <div>
                <input type="text" class="effe-pro-feature" id="<?php echo esc_attr( $this->plugin_name . '-location_filter' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[location_filter]' ); ?>" value="<?php echo esc_attr( $location_filter ); ?>" <?php if( ! $this->freemius->can_use_premium_code() || $this->freemius->is_free_plan() ) { echo 'readonly'; } ?> data-service="<?php esc_attr_e( 'Location Filter', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>">
                <p class="description"><?php esc_html_e( 'To match multiple terms, use | for OR logic (e.g., New York|Street 123) and + for AND logic (e.g., New York|Street 123).', 'event-feed-for-eventbrite' ); ?></p>
            </div>

            <?php if( ( ! $this->freemius->can_use_premium_code() || $this->freemius->is_free_plan() ) && $location_filter !== '' ) { ?>
            <div class="event-feed-for-eventbrite-warning">
                <div>
                    <div><?php
                    printf(
                        '%s <strong>%s</strong>. <br>%s <a href="' . esc_url( $this->freemius->get_upgrade_url() ) . '">' . esc_html__( 'renew your license', 'event-feed-for-eventbrite' ) . '</a>.',
                        esc_html__( 'Location filter can\'t be applied, because', 'event-feed-for-eventbrite' ) ,
                        esc_html__( 'your premium license is not active', 'event-feed-for-eventbrite' ),
                        esc_html__( 'To continue using this feature, please', 'event-feed-for-eventbrite' )
                    );
                    ?></div>
                </div>
            </div>
            <?php } ?>

        </div>     
    </div>

    <!-- Description Filter -->
    <div class="event-feed-for-eventbrite-metabox-field">
        <div class="event-feed-for-eventbrite-metabox-label">
            <label for="<?php echo esc_attr( $this->plugin_name . '-description_filter' ); ?>" class="effe-pro-feature" data-service="<?php esc_attr_e( 'Description Filter', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>">
                <?php esc_html_e( 'Description Filter', 'event-feed-for-eventbrite' ); ?>
            </label>
            <p class="description"><?php esc_html_e( 'Show only events whose description contains specific text.', 'event-feed-for-eventbrite' ); ?></p>
        </div>
        <div class="event-feed-for-eventbrite-metabox-input">
            <div>
                <input type="text" class="effe-pro-feature" id="<?php echo esc_attr( $this->plugin_name . '-description_filter' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[description_filter]' ); ?>" value="<?php echo esc_attr( $description_filter ); ?>" <?php if( ! $this->freemius->can_use_premium_code() || $this->freemius->is_free_plan() ) { echo 'readonly'; } ?> data-service="<?php esc_attr_e( 'Description Filter', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>">
                <p class="description"><?php esc_html_e( 'To match multiple terms, use | for OR logic (e.g., conference|meetup) and + for AND logic (e.g., conference+meetup).', 'event-feed-for-eventbrite' ); ?></p>
            </div>

            <?php if( ( ! $this->freemius->can_use_premium_code() || $this->freemius->is_free_plan() ) && $description_filter !== '' ) { ?>
            <div class="event-feed-for-eventbrite-warning">
                <div>
                    <div><?php
                    printf(
                        '%s <strong>%s</strong>. <br>%s <a href="' . esc_url( $this->freemius->get_upgrade_url() ) . '">' . esc_html__( 'renew your license', 'event-feed-for-eventbrite' ) . '</a>.',
                        esc_html__( 'Description filter can\'t be applied, because', 'event-feed-for-eventbrite' ) ,
                        esc_html__( 'your premium license is not active', 'event-feed-for-eventbrite' ),
                        esc_html__( 'To continue using this feature, please', 'event-feed-for-eventbrite' )
                    );
                    ?></div>
                </div>
            </div>
            <?php } ?>

        </div>     
    </div>

    <!-- Organizer Filter -->
    <div class="event-feed-for-eventbrite-metabox-field">
        <div class="event-feed-for-eventbrite-metabox-label">
            <label for="<?php echo esc_attr( $this->plugin_name . '-organizer_filter' ); ?>" class="effe-pro-feature" data-service="<?php esc_attr_e( 'Organizer Filter', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>">
                <?php esc_html_e( 'Organizer Filter (beta)', 'event-feed-for-eventbrite' ); ?>
            </label>
            <p class="description"><?php esc_html_e( 'Show only events organized by a specific organizer.', 'event-feed-for-eventbrite' ); ?></p>
        </div>

        <div class="event-feed-for-eventbrite-metabox-input">
            <div>
                <input type="text" class="effe-pro-feature" id="<?php echo esc_attr( $this->plugin_name . '-organizer_filter' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[organizer_filter]' ); ?>" value="<?php echo esc_attr( $organizer_filter ); ?>" <?php if( ! $this->freemius->can_use_premium_code() || $this->freemius->is_free_plan() ) { echo 'readonly'; } ?> data-service="<?php esc_attr_e( 'Organizer Filter', 'event-feed-for-eventbrite' ); ?>" data-upgrade-link="<?php echo esc_url( $this->freemius->get_upgrade_url() ); ?>">
                <p class="description"><?php esc_html_e( 'Enter organizer\'s ID.', 'event-feed-for-eventbrite' ); ?> <a href="https://eventfeed.click/docs/event-filters/#organizer-filter" target="_blank"><?php esc_html_e( 'How to get the ID?', 'event-feed-for-eventbrite' ); ?></a><br><?php esc_html_e( 'To match multiple terms, use | for OR logic (e.g., 123456789|987654321).', 'event-feed-for-eventbrite' ); ?></p>
            </div>

            <?php if( ( ! $this->freemius->can_use_premium_code() || $this->freemius->is_free_plan() ) && $organizer_filter !== null && $organizer_filter !== '' ) { ?>
            <div class="event-feed-for-eventbrite-warning">
                <div>
                    <div><?php
                    printf(
                        '%s <strong>%s</strong>. <br>%s <a href="' . esc_url( $this->freemius->get_upgrade_url() ) . '">' . esc_html__( 'renew your license', 'event-feed-for-eventbrite' ) . '</a>.',
                        esc_html__( 'Organizer filter can\'t be applied, because', 'event-feed-for-eventbrite' ) ,
                        esc_html__( 'your premium license is not active', 'event-feed-for-eventbrite' ),
                        esc_html__( 'To continue using this feature, please', 'event-feed-for-eventbrite' )
                    );
                    ?></div>
                </div>
            </div>
            <?php } ?>

        </div>
    </div>     
    
</div>