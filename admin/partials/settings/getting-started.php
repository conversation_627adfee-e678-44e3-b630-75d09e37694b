<?php

/**
 * Provide welcome page that is shown after plugin activation
 *
 * This file is used to markup the welcome page of the plugin.
 *
 * @link       https://www.bohemiaplugins.com/
 * @since      1.0.0
 *
 * @package    Event_Feed_For_Eventbrite
 * @subpackage Event_Feed_For_Eventbrite/admin/partials
 */

// Get options
$options = get_option( $this->plugin_name );

// Check if user already filled API key
$user_is_back = false;
if( isset( $options['api_key'] ) && $options['api_key'] !== '' ) {
    $user_is_back = true;
}

// Check existing event feeds
$event_feed_num = wp_count_posts( 'event_feed' )->publish;

if( $step === null ) {

?>

<div id="event-feed-for-eventbrite-getting-started">
    <div class="container"> 
        <nav class="progress">
            <ul>
                <?php if( $user_is_back === false ) { ?>
                    <li class="current"><span><span><?php esc_html_e( 'API key', 'event-feed-for-eventbrite' ); ?></span></span></li>
                    <li class="future"><span><span><?php esc_html_e( 'Start creating', 'event-feed-for-eventbrite' ); ?></span></span></li>
                <?php } else { ?>
                    <li class="filled"><span><span><?php esc_html_e( 'Welcome back', 'event-feed-for-eventbrite' ); ?></span></span></li>
                <?php } ?>
            </ul>
        </nav>
        <div class="intro">
            <div class="logo">
                <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 27 27" enable-background="new 0 0 27 27" xml:space="preserve">
                    <g id="Logo_image" transform="translate(-78 -27)">
                        <rect id="bg" x="78" y="27" fill="#EAEEFC" width="27" height="27"/>
                        <g enable-background="new"> 
                            <path fill="#334ECD" d="M91.1,36.8l-0.5,2.6h4.1l-0.4,2.3h-4.1l-0.5,2.8h4.7L93.9,47h-7.7l2.2-12.6h7.7l-0.4,2.4H91.1z"/>
                        </g>
                    </g>
                </svg>
            </div>
            <div class="block">
                <h1><img draggable="false" role="img" class="emoji" alt="🎉" src="https://s.w.org/images/core/emoji/13.1.0/svg/1f389.svg"> <?php esc_html_e( 'Welcome to Event Feed for Eventbrite!', 'event-feed-for-eventbrite' ); ?></h1>
                <h6><?php esc_html_e( 'Thank you for choosing Event Feed for Eventbrite - the most powerful and easy-to-use WordPress plugin for Eventbrite events.', 'event-feed-for-eventbrite' ); ?></h6>
                <hr>

                <?php if( isset( $_GET['notice'] ) && $_GET['notice'] == 'error' ) { ?>
                    <div class="event-feed-for-eventbrite-warning">
                        <div>
                            <div><?php
                            printf(
                                '<strong>%s</strong>. %s',
                                esc_html__( 'Invalid API key', 'event-feed-for-eventbrite' ),
                                esc_html__( 'Please enter a valid key. You can get it after clicking on the link below.', 'event-feed-for-eventbrite' )
                            );
                            ?></div>
                        </div>
                    </div>
                <?php } ?>

                <?php if( $user_is_back === false ) { ?>
                    <p><?php esc_html_e( 'Fill in your Eventbrite API key before continuing. This step is necessary for the plugin to have access to information about your events.', 'event-feed-for-eventbrite' ); ?></p>
                    <p class="event-feed-for-eventbrite-api-link-wrapper">
                        <a class="event-feed-for-eventbrite-api-link" href="https://www.eventbrite.com/platform/api-keys" target="_blank">
                            <?php esc_html_e( 'You can get your private token here', 'event-feed-for-eventbrite' ) ?><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" class="components-external-link__icon css-bqq7t3 etxm6pv0" role="img" aria-hidden="true" focusable="false"><path d="M18.2 17c0 .7-.6 1.2-1.2 1.2H7c-.7 0-1.2-.6-1.2-1.2V7c0-.7.6-1.2 1.2-1.2h3.2V4.2H7C5.5 4.2 4.2 5.5 4.2 7v10c0 1.5 1.2 2.8 2.8 2.8h10c1.5 0 2.8-1.2 2.8-2.8v-3.6h-1.5V17zM14.9 3v1.5h3.7l-6.4 6.4 1.1 1.1 6.4-6.4v3.7h1.5V3h-6.3z"></path></svg>
                        </a>
                    </p>
                    
                    <div class="api-key">
                        <form action="<?php echo esc_url( admin_url( 'admin-post.php' ) ); ?>" method="post" id="getting_started_save_api_key" autocomplete="off">

                            <?php

                            // Nonce
                            $getting_started_nonce = wp_create_nonce( 'effe_getting_started_nonce' ); 

                            // Cleanup
                            if( $options !== false ) {
                                $api_key = isset( $options['api_key'] ) ? $options['api_key'] : '';
                            } else {
                                $api_key = '';
                            }   

                            ?>
                            <input type="hidden" name="action" value="effe_getting_started_save_api_key_response">
                            <input type="hidden" name="effe_getting_started_nonce" value="<?php echo esc_attr( $getting_started_nonce ); ?>">
                            <input type="password" id="api_key" name="api_key" value="<?php if( ! empty( $api_key ) ) echo esc_attr( $api_key ); ?>" placeholder="<?php esc_attr_e( 'Your Eventbrite private token', 'event-feed-for-eventbrite' ); ?>" required>
                            <input type="submit" name="submit" id="submit" class="button button-primary" value="<?php esc_attr_e( 'Save token', 'event-feed-for-eventbrite' ); ?>">

                        </form>
                    </div>
                    <small><?php esc_html_e( 'Your private token is never exposed to your website visitors.', 'event-feed-for-eventbrite' ); ?></small>
                <?php } else { ?>
                    <p><?php
                    printf(
                        '%s <a href="https://www.eventfeed.click/docs/" target="_blank">%s</a>.',
                        esc_html__( 'You are connected to Eventbrite, so there is no action required. If you need some help, be sure to', 'event-feed-for-eventbrite' ),
                        esc_html__( 'check out our guide', 'event-feed-for-eventbrite' )
                    );
                    ?></p>
                    <div class="btn-row">
                        <?php  
                        if( $event_feed_num > 0 ) { 
                        ?>
                        <a class="button button-primary" href="<?php echo esc_url( admin_url( 'edit.php?post_type=event_feed' ) ); ?>"><?php esc_html_e( 'See Your Event Feeds', 'event-feed-for-eventbrite' ); ?></a>
                        <?php } else { ?>
                        <a class="button button-primary" href="<?php echo esc_url( admin_url( 'post-new.php?post_type=event_feed' ) ); ?>"><?php esc_html_e( 'Create Your First Event Feed', 'event-feed-for-eventbrite' ); ?></a>
                        <?php } ?>
                        <a class="button button-secondary" href="https://www.eventfeed.click/docs/" target="_blank"><?php esc_html_e( 'Read the Full Guide', 'event-feed-for-eventbrite' ); ?></a>
                    </div>
                <?php } ?>

            </div>
        </div>

        <div class="skip-wizard">
            <a href="<?php echo esc_url( admin_url( 'edit.php?post_type=event_feed&page=' . $this->plugin_name . '-settings' ) ); ?>">
                <?php if( $user_is_back === false ) { ?>
                <?php esc_html_e( 'I am an experienced user. Take me to the settings.', 'event-feed-for-eventbrite' ); ?>
                <?php } else { ?>
                <?php esc_html_e( 'Take me to the settings', 'event-feed-for-eventbrite' ); ?>
                <?php } ?>
            </a>
        </div>

    </div>
</div>

<?php 

} elseif( $step === 'start-creating' ) { 

?>

<div id="event-feed-for-eventbrite-getting-started">
    <div class="container">
        <nav class="progress">
            <ul>
                <li class="filled"><span><span><?php esc_html_e( 'API key', 'event-feed-for-eventbrite' ); ?></span></span></li>
                <li class="current"><span><span><?php esc_html_e( 'Start creating', 'event-feed-for-eventbrite' ); ?></span></span></li>
            </ul>
        </nav>
        <div class="intro">
            <div class="logo">
                <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 27 27" enable-background="new 0 0 27 27" xml:space="preserve">
                    <g id="Logo_image" transform="translate(-78 -27)">
                        <rect id="bg" x="78" y="27" fill="#EAEEFC" width="27" height="27"/>
                        <g enable-background="new"> 
                            <path fill="#334ECD" d="M91.1,36.8l-0.5,2.6h4.1l-0.4,2.3h-4.1l-0.5,2.8h4.7L93.9,47h-7.7l2.2-12.6h7.7l-0.4,2.4H91.1z"/>
                        </g>
                    </g>
                </svg>
            </div>
            <div class="block">
                <h1><img draggable="false" role="img" class="emoji" alt="🚀" src="https://s.w.org/images/core/emoji/13.1.0/svg/1f680.svg"> <?php
                printf(
                    '%s <br>%s',
                    esc_html__( 'Successfully connected to Eventbrite.', 'event-feed-for-eventbrite' ),
                    esc_html__( 'Time to start creating!', 'event-feed-for-eventbrite' )

                );
                ?></h1>
                <h6><?php
                printf(
                    'To get started you can <a href="https://www.eventfeed.click/docs/" target="_blank">%s</a> on how to create your first event feed or just jump right to it and experiment.',
                    esc_html__( 'read our guide', 'event-feed-for-eventbrite' )
                );
                ?></h6>
                <div class="btn-row">
                    <?php  
                    if( $event_feed_num > 0 ) { 
                    ?>
                    <a class="button button-primary" href="<?php echo esc_url( admin_url( 'edit.php?post_type=event_feed' ) ); ?>"><?php esc_html_e( 'See Your Event Feeds', 'event-feed-for-eventbrite' ); ?></a>
                    <?php } else { ?>
                    <a class="button button-primary" href="<?php echo esc_url( admin_url( 'post-new.php?post_type=event_feed' ) ); ?>"><?php esc_html_e( 'Create Your First Event Feed', 'event-feed-for-eventbrite' ); ?></a>
                    <?php } ?>
                    <a class="button button-secondary" href="https://www.eventfeed.click/docs/" target="_blank"><?php esc_html_e( 'Read the Full Guide', 'event-feed-for-eventbrite' ); ?></a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php } ?>