<?php

/**
 * Provide a admin area view for the plugin
 *
 * This file is used to markup the admin-facing aspects of the plugin.
 *
 * @link       https://www.bohemiaplugins.com/
 * @since      1.0.0
 *
 * @package    Event_Feed_For_Eventbrite
 * @subpackage Event_Feed_For_Eventbrite/admin/partials
 */

?>

<div class="wrap eventbrite_plugin_options">

    <h1><?php esc_html_e( 'Eventbrite Events Settings', 'event-feed-for-eventbrite' ); ?></h1>

    <?php settings_errors(); ?>

    <!-- Tabs -->
    <h2 class="nav-tab-wrapper">
        <a href="edit.php?post_type=event_feed&page=<?php echo $this->plugin_name . '-settings'; ?>" class="nav-tab <?php if( $tab === null ) { ?>nav-tab-active<?php } ?>">
            <?php esc_html_e( 'Eventbrite', 'event-feed-for-eventbrite' ); ?>
        </a>
        <a href="edit.php?post_type=event_feed&page=<?php echo $this->plugin_name . '-settings'; ?>&amp;tab=appearance" class="nav-tab <?php if( $tab === 'appearance' ) { ?>nav-tab-active<?php } ?>">
            <?php esc_html_e( 'Appearance', 'event-feed-for-eventbrite' ); ?>
        </a>
        <a href="edit.php?post_type=event_feed&page=<?php echo $this->plugin_name . '-settings'; ?>&amp;tab=formats" class="nav-tab <?php if( $tab === 'formats' ) { ?>nav-tab-active<?php } ?>">
            <?php esc_html_e( 'Date & address', 'event-feed-for-eventbrite' ); ?>
        </a>
        <a href="edit.php?post_type=event_feed&page=<?php echo $this->plugin_name . '-settings'; ?>&amp;tab=advanced" class="nav-tab <?php if( $tab === 'advanced' ) { ?>nav-tab-active<?php } ?>">
            <?php esc_html_e( 'Misc', 'event-feed-for-eventbrite' ); ?>
        </a>
    </h2>

    <form method="post" name="event-feed-for-eventbrite_options" action="options.php" autocomplete="off">

        <?php

        // General tab
        if( $tab === null ) {

            // Get options
            $options_default = 'a:2:{s:7:"api_key";s:20:"";s:14:"cache_duration";i:86400;}';
            $options = get_option( $this->plugin_name, $options_default );

            // Cleanup
            if( $options !== false ) {
                $api_key = isset( $options['api_key'] ) ? sanitize_text_field( $options['api_key'] ) : '';
                $cache_duration = isset( $options['cache_duration'] ) ? intval( $options['cache_duration'] ) : '';
            } else {
                $api_key = '';
                $cache_duration = '';
            }

            settings_fields( $this->plugin_name );
            do_settings_sections( $this->plugin_name );

            ?>
            <div>

                <!-- Eventbrite API key -->
                <div class="heading-row">
                    <h2><?php esc_html_e( 'Eventbrite API key', 'event-feed-for-eventbrite' ); ?></h2>
                    <p><?php esc_html_e( 'The Eventbrite API key allows the plugin to connect to Eventbrite and retrieve data about your events.', 'event-feed-for-eventbrite' ); ?></p>
                </div>

                <div class="field-row">
                    <table class="form-table" cellpadding="0" cellspacing="0">
                        <tbody>
                            <tr valign="top">
                                <th scope="row">
                                    <?php esc_html_e( 'Eventbrite Status', 'event-feed-for-eventbrite' ); ?>
                                </th>
                                <td>
                                    <?php 
                                    if( ! empty( $api_key ) ) { 
                                    ?>
                                        <span class="status-badge active">
                                            <span class="status-badge-inner">
                                                <span class="dashicons dashicons-yes"></span>
                                                <span><?php esc_html_e( 'Connected', 'event-feed-for-eventbrite' ); ?></span>
                                            </span>
                                        </span>
                                    <?php } else { ?>
                                        <span class="status-badge">
                                            <span class="status-badge-inner">
                                                <span class="dashicons dashicons-no"></span>
                                                <span><?php esc_html_e( 'Not connected', 'event-feed-for-eventbrite' ); ?></span>
                                            </span>
                                        </span>
                                    <?php } ?>
                                </td>
                            </tr>
                            <tr valign="top">
                                <th scope="row">
                                    <?php esc_html_e( 'Private Token', 'event-feed-for-eventbrite' ); ?>
                                </th>
                                <td>
                                    <input type="password" id="<?php echo esc_attr( $this->plugin_name . '-api_key' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[api_key]' ); ?>" value="<?php if( ! empty( $api_key ) ) echo esc_attr( $api_key );?>" placeholder="<?php esc_attr_e( 'Your Eventbrite API key', 'event-feed-for-eventbrite' ); ?>">
                                    <p class="description">
                                        <?php esc_html_e( 'The private token is required to connect the plugin to Eventbrite', 'event-feed-for-eventbrite' ); ?> - <a href="https://www.eventbrite.com/platform/api-keys" target="_blank"><?php esc_html_e( 'get the token here', 'event-feed-for-eventbrite' ); ?></a>
                                    </p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- Cache Settings  -->
                <div class="heading-row">
                    <h2><?php esc_html_e( 'Cache', 'event-feed-for-eventbrite' ); ?></h2>
                    <p><?php esc_html_e( 'Set the refresh period of the event information.', 'event-feed-for-eventbrite' ); ?></p>
                </div>

                <div class="field-row">
                    <table class="form-table">

                        <tbody>
                            <tr valign="top">
                                <th scope="row"><?php esc_html_e( 'Cache Duration', 'event-feed-for-eventbrite'); ?></th>
                                <td>
                                    <span class="select-wrapper">
                                        <select id="<?php echo esc_attr( $this->plugin_name . '-cache_duration' ); ?>" name="<?php echo esc_attr( $this->plugin_name . '[cache_duration]' ); ?>" class="small-text choices-cache-duration">
                                            <option value="3600" <?php if( $cache_duration == '60' || $cache_duration == '3600' ) echo 'selected="selected"'; ?>>
                                                <?php esc_html_e( 'Hour', 'event-feed-for-eventbrite' ); ?>
                                            </option>
                                            <option value="86400" <?php if( $cache_duration == '86400' || $cache_duration == '' ) echo 'selected="selected"'; ?>>
                                                <?php esc_html_e( 'Day', 'event-feed-for-eventbrite' ); ?>
                                            </option>
                                            <option value="172800" <?php if( $cache_duration == '172800' ) echo 'selected="selected"'; ?>>
                                                <?php esc_html_e( '2 days', 'event-feed-for-eventbrite' ); ?>
                                            </option>
                                            <option value="604800" <?php if( $cache_duration == '604800' ) echo 'selected="selected"'; ?>>
                                                <?php esc_html_e( 'Week', 'event-feed-for-eventbrite' ); ?>
                                            </option>
                                        </select>
                                    </span>
                                    <p class="description"><?php esc_html_e( 'Set the cache period for the plugin to refresh events displayed on your site. If you select "Day", the plugin will check for updates to your Eventbrite events once a day (new events, updated information, new participants). The advantage of using caching is that event feeds load faster, and this option also saves server resources. The downside is that changes to your Eventbrite events won\'t take effect immediately on your site. To immediately see changes to Eventbrite events, you can clear the cache manually from the admin bar.', 'event-feed-for-eventbrite' ); ?></p>
                                    <?php if( $cache_duration == '3600' ) { ?>
                                        <div class="alert">
                                            <p><?php esc_html_e( 'Please note that the "Hour" period is experimental. For production use, we recommend selecting a period of at least "Day" to avoid exceeding API limits.', 'event-feed-for-eventbrite' ); ?></p>
                                        </div>
                                    <?php } ?>
                                    <?php if( $cache_duration == '60' ) { ?>
                                        <div class="alert">
                                            <p><?php esc_html_e( 'You are currently using the deprecated "Minute" period, which is no longer supported. Please select a different value and re-save the settings.', 'event-feed-for-eventbrite' ); ?></p>
                                        </div>
                                    <?php } ?>
                                </td>
                            </tr>
                        </tbody>

                    </table>
                </div>

            </div>
        
        <?php

        // Appearance tab
        } elseif( $tab === 'appearance' ) {

            // Get options
            $options_default = 'a:4:{s:12:"google_fonts";b:0;s:19:"google_fonts_family";s:7:"Poppins";s:17:"placeholder_image";s:0:"";s:14:"placeholder_id";i:0;}';
            $options = get_option( $this->plugin_name . '-appearance', $options_default );

            // Cleanup
            if( $options !== false ) {
                $google_fonts = isset( $options['google_fonts'] ) ? boolval( $options['google_fonts'] ) : false;
                $google_fonts_family = isset( $options['google_fonts_family'] ) ? sanitize_text_field( $options['google_fonts_family'] ) : 'Poppins';
                $placeholder_image = isset( $options['placeholder_image'] ) ? wp_strip_all_tags( esc_url_raw( $options['placeholder_image'] ) ) : '';
                $placeholder_id = isset( $options['placeholder_id'] ) ? intval( $options['placeholder_id'] ) : '';
            } else {
                $google_fonts = false;
                $google_fonts_family = 'Poppins';
                $placeholder_image = '';
                $placeholder_id = '';
            }

            // Settings name
            $settings_name = $this->plugin_name . '-appearance';

            settings_fields( $settings_name );
            do_settings_sections( $settings_name );

            ?>
            
            <div>
                
                <!-- Font Settings  -->
                <div class="heading-row">    
                    <h2><?php esc_html_e( 'Font', 'event-feed-for-eventbrite' ); ?></h2>
                    <p><?php esc_html_e( 'Choose which font will be used on the event card and in the event popup.', 'event-feed-for-eventbrite' ); ?></p>
                </div>

                <div class="field-row">
                    <table class="form-table">
                        <tbody>
                            <tr valign="top">
                                <th scope="row">
                                    <label class="event-feed-for-eventbrite-switch-label" for="<?php echo esc_attr( $settings_name . '-google_fonts' ); ?>">
                                        <?php esc_html_e( 'Use Google Fonts', 'event-feed-for-eventbrite' ); ?>
                                    </label>
                                </th>
                                <td>
                                    <input class="event-feed-for-eventbrite-switch-input" type="checkbox" id="<?php echo esc_attr( $settings_name . '-google_fonts' ); ?>" name="<?php echo esc_attr( $settings_name . '[google_fonts]' ); ?>" <?php if( $google_fonts === true ) echo 'checked'; ?>>
                                    <div class="event-feed-for-eventbrite-switch <?php if( $google_fonts === true ) echo '-on'; ?>">
                                        <span class="event-feed-for-eventbrite-switch-on"><?php esc_html_e( 'Yes', 'event-feed-for-eventbrite' ); ?></span>
                                        <span class="event-feed-for-eventbrite-switch-off"><?php esc_html_e( 'No', 'event-feed-for-eventbrite' ); ?></span>
                                        <div class="event-feed-for-eventbrite-slider"></div>
                                    </div>
                                    <p class="description"><?php esc_html_e( 'Uncheck if you want to use your theme font instead of Google Fonts.', 'event-feed-for-eventbrite'); ?></p>
                                </td>
                            </tr>
                            <tr <?php if( $google_fonts === false ) echo 'class="hidden"'; ?> valign="top" data-condition-element="<?php echo esc_attr( $settings_name . '[google_fonts]' ); ?>" data-condition-type="checkbox">
                                <th scope="row">
                                    <label for="<?php echo esc_attr( $settings_name . '-google_fonts_family' ); ?>">
                                        <?php esc_html_e( 'Google Font', 'event-feed-for-eventbrite' ); ?>
                                    </label>
                                </th>
                                <td>
                                    <?php 
                                    $google_fonts_list = $this->get_google_fonts_list();
                                    if( $google_fonts_list ) {
                                    ?>
                                    <span class="select-wrapper">
                                        <select id="<?php echo esc_attr( $settings_name . '-google_fonts_family' ); ?>" name="<?php echo esc_attr( $settings_name . '[google_fonts_family]' ); ?>" class="small-text choices-google-fonts-family">
                                            <?php foreach( $google_fonts_list as $font ) { ?>
                                            <option value="<?php echo esc_attr( $font ); ?>" <?php if( $google_fonts_family === $font ) echo 'selected'; ?>>
                                                <?php echo esc_html( $font ); ?>
                                            </option>
                                            <?php } ?>
                                        </select>
                                    </span>
                                    <?php } ?>
                                    <p class="description"><?php
                                    printf(
                                        '%s <a href="https://fonts.google.com/" target="_blank">%s</a>. %s',
                                        esc_html__( 'You can', 'event-feed-for-eventbrite' ),
                                        esc_html__( 'preview all the available Google Fonts here', 'event-feed-for-eventbrite' ),
                                        esc_html__( 'Default font: Poppins', 'event-feed-for-eventbrite' )
                                    );
                                    ?></p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Image placeholder -->
                <div class="heading-row">
                    <h2><?php esc_html_e( 'Image Placeholder', 'event-feed-for-eventbrite'); ?></h2>
                    <p><?php esc_html_e( 'Placeholder image will be used when the event doesn\'t have any image set up on Eventbrite.', 'event-feed-for-eventbrite' ); ?></p>
                </div>

                <div class="field-row">
                    <table class="form-table">

                        <tbody>
                            <tr valign="top">
                                <th scope="row">
                                    <label for="<?php echo esc_attr( $settings_name . '-placeholder_image' ); ?>">
                                        <?php esc_html_e( 'Placeholder Image', 'event-feed-for-eventbrite' ); ?>
                                    </label>
                                </th>
                                <td>
                                    <?php 
                                    $meta = wp_get_attachment_metadata( $placeholder_id );
                                    if( isset( $meta['sizes']['large'] ) ) {
                                        $placeholder_image_preview = wp_get_attachment_image_url( $placeholder_id, 'large' );
                                    } elseif( isset( $meta['sizes']['medium'] ) ) {
                                        $placeholder_image_preview = wp_get_attachment_image_url( $placeholder_id, 'medium' );
                                    } else {
                                        $placeholder_image_preview = $placeholder_image;
                                    }
                                    ?>
                                    <p class="plugin-image-uload">
                                        <input type="text" class="placeholder-background-image" id="<?php echo esc_attr( $settings_name . '-placeholder_image' ); ?>" name="<?php echo esc_attr( $settings_name . '[placeholder_image]' ); ?>" value="<?php echo esc_url( $placeholder_image_preview ); ?>">
                                        <input type="hidden" class="placeholder-background-id" id="<?php echo esc_attr( $settings_name . '-placeholder_id' ); ?>" name="<?php echo esc_attr( $settings_name . '[placeholder_id]' ); ?>" value="<?php echo esc_url( $placeholder_id ); ?>">
                                        <input type="button" class="placeholder-image-upload button-secondary" value="<?php esc_attr_e( 'Upload Image', 'event-feed-for-eventbrite' ); ?>">
                                    </p>
                                    <p class="description"><?php esc_html_e( 'Recommended size is 800x400px.', 'event-feed-for-eventbrite' ); ?></p>    
                                </td>
                            </tr>
                        </tbody>

                    </table>
                </div>

            </div>

            <?php

        // Date & Address tab
        } elseif( $tab === 'formats' ) {

            // Get options
            $options_default = 'a:4:{s:11:"time_format";b:0;s:18:"date_format_custom";s:6:"F j, Y";s:14:"address_format";s:27:"[localized_address_display]";s:19:"eventbrite_timezone";b:1;}';
            $options = get_option( $this->plugin_name . '-formats', $options_default );

            // Cleanup
            if( $options !== false ) {
                $time_format = isset( $options['time_format'] ) ? boolval( $options['time_format'] ) : false;
                $date_format = isset( $options['date_format_custom'] ) ? sanitize_text_field( $options['date_format_custom'] ) : 'F j, Y';
                $shortdate_months = isset( $options['shortdate_months'] ) ? boolval( $options['shortdate_months'] ) : false;
                $address_format = isset( $options['address_format'] ) ? sanitize_text_field( $options['address_format'] ) : '[localized_address_display]';
                $eventbrite_timezone = isset( $options['eventbrite_timezone'] ) ? boolval( $options['eventbrite_timezone'] ) : false;
            } else {
                $time_format = '';
                $date_format = '';
                $shortdate_months = '';
                $address_format = '';
                $eventbrite_timezone = '';
            }

            // Settings name
            $settings_name = $this->plugin_name . '-formats';

            settings_fields( $settings_name );
            do_settings_sections( $settings_name );

            ?>

            <div>

                <!-- Date and Time Format -->
                <div class="heading-row">
                    <h2><?php esc_html_e( 'Date Format', 'event-feed-for-eventbrite' ); ?></h2>
                    <p><?php esc_html_e( 'Here you can modify the default date and time format.', 'event-feed-for-eventbrite' ); ?></p>
                </div>

                <div class="field-row">
                    <table class="form-table">
                        <tbody>
                            <tr valign="top">
                                <th scope="row">
                                    <label class="event-feed-for-eventbrite-switch-label" for="<?php echo esc_attr( $settings_name . '-time_format' ); ?>">
                                        <?php esc_html_e( '24-hour time format', 'event-feed-for-eventbrite' ); ?>
                                    </label>
                                </th>
                                <td>
                                <input class="event-feed-for-eventbrite-switch-input" type="checkbox" id="<?php echo esc_attr( $settings_name . '-time_format' ); ?>" name="<?php echo esc_attr( $settings_name . '[time_format]' ); ?>" <?php if( $time_format === true ) echo 'checked'; ?>>
                                    <div class="event-feed-for-eventbrite-switch <?php if( $time_format === true ) echo '-on'; ?>">
                                        <span class="event-feed-for-eventbrite-switch-on"><?php esc_html_e( 'Yes', 'event-feed-for-eventbrite' ); ?></span>
                                        <span class="event-feed-for-eventbrite-switch-off"><?php esc_html_e( 'No', 'event-feed-for-eventbrite' ); ?></span>
                                        <div class="event-feed-for-eventbrite-slider"></div>
                                    </div>
                                    <p class="description"><?php esc_html_e( 'Check this box to display the time in 24-hour format (e.g., 18:00 instead of 6 PM).', 'event-feed-for-eventbrite' ); ?></p>
                                </td>
                            </tr>
                            <tr valign="top">
                                <th scope="row">
                                    <label>
                                        <?php esc_html_e( 'Date Format', 'event-feed-for-eventbrite' ); ?>
                                    </label>
                                </th>
                                <td>
                                    <fieldset class="event-feed-for-eventbrite-date-settings event-feed-for-eventbrite-fulldate-settings">

                                        <?php 
                                        $date_formats = array(
                                            'F j, Y',
                                            'Y-m-d',
                                            'm/d/Y',
                                            'd/m/Y'
                                        );

                                        foreach( $date_formats as $format ) { ?>

                                            <label>
                                                <input type="radio" id="<?php echo esc_attr( $settings_name . '-date_format' ); ?>" name="<?php echo esc_attr( $settings_name . '[date_format]' ); ?>" value="<?php echo esc_attr( $format ); ?>" <?php if( $date_format === $format ) { echo 'checked="checked"'; } ?>> 
                                                <span class="date-time-text format-i18n"><?php echo esc_html( date_i18n( $format ) ); ?></span>
                                                <code><?php echo esc_html( $format ); ?></code>
                                            </label>
                                            <br>

                                        <?php } ?>

                                        <label>
                                            <input type="radio" class="date-format-custom" id="<?php echo esc_attr( $settings_name . '-date_format' ); ?>" name="<?php echo esc_attr( $settings_name . '[date_format]' ); ?>" value="<?php echo esc_attr( $date_format ); ?>" <?php if( ! in_array( $date_format, $date_formats ) ) { echo 'checked="checked"'; } ?>>
                                            <span class="date-time-text date-time-custom-text">
                                                <?php esc_html_e( 'Custom:', 'event-feed-for-eventbrite' ); ?>
                                            </span>
                                        </label>
                                        <input type="text" id="<?php echo esc_attr( $settings_name . '-date_format_custom' ); ?>" name="<?php echo esc_attr( $settings_name . '[date_format_custom]'); ?>" value="<?php echo esc_attr( $date_format ); ?>" class="small-text">
                                        <br>

                                        <p>
                                            <strong><?php esc_html_e( 'Preview:', 'event-feed-for-eventbrite' ); ?></strong> 
                                            <span class="example"><?php echo esc_html( date_i18n( $date_format ) ); ?></span>
                                            <span class="spinner"></span>
                                        </p>

                                        <p class="date-time-doc">
                                            <a href="https://wordpress.org/support/article/formatting-date-and-time/" target="_blank"><?php esc_html_e( 'Documentation on date and time formatting', 'event-feed-for-eventbrite' ); ?></a>.
                                        </p>

                                    </fieldset>
                                </td>
                            </tr>
                            <tr valign="top">
                                <th scope="row">
                                    <label class="event-feed-for-eventbrite-switch-label" for="<?php echo esc_attr( $settings_name . '-shortdate_months' ); ?>">
                                        <?php esc_html_e( 'Short date - show months', 'event-feed-for-eventbrite' ); ?>
                                    </label>
                                </th>
                                <td>
                                <input class="event-feed-for-eventbrite-switch-input" type="checkbox" id="<?php echo esc_attr( $settings_name . '-shortdate_months' ); ?>" name="<?php echo esc_attr( $settings_name . '[shortdate_months]' ); ?>" <?php if( $shortdate_months === true ) echo 'checked'; ?>>
                                    <div class="event-feed-for-eventbrite-switch <?php if( $shortdate_months === true ) echo '-on'; ?>">
                                        <span class="event-feed-for-eventbrite-switch-on"><?php esc_html_e( 'Yes', 'event-feed-for-eventbrite' ); ?></span>
                                        <span class="event-feed-for-eventbrite-switch-off"><?php esc_html_e( 'No', 'event-feed-for-eventbrite' ); ?></span>
                                        <div class="event-feed-for-eventbrite-slider"></div>
                                    </div>
                                    <p class="description"><?php esc_html_e( 'Show months instead of days in short date (on event card).', 'event-feed-for-eventbrite' ); ?></p>
                                </td>
                            </tr>
                            <tr valign="top">
                                <th scope="row">
                                    <label class="event-feed-for-eventbrite-switch-label" for="<?php echo esc_attr( $settings_name . '-eventbrite_timezone' ); ?>">
                                        <?php esc_html_e( 'Eventbrite Timezone', 'event-feed-for-eventbrite' ); ?>
                                    </label>
                                </th>
                                <td>
                                <input class="event-feed-for-eventbrite-switch-input" type="checkbox" id="<?php echo esc_attr( $settings_name . '-eventbrite_timezone' ); ?>" name="<?php echo esc_attr( $settings_name . '[eventbrite_timezone]' ); ?>" <?php if( $eventbrite_timezone === true ) echo 'checked'; ?>>
                                    <div class="event-feed-for-eventbrite-switch <?php if( $eventbrite_timezone === true ) echo '-on'; ?>">
                                        <span class="event-feed-for-eventbrite-switch-on"><?php esc_html_e( 'Yes', 'event-feed-for-eventbrite' ); ?></span>
                                        <span class="event-feed-for-eventbrite-switch-off"><?php esc_html_e( 'No', 'event-feed-for-eventbrite' ); ?></span>
                                        <div class="event-feed-for-eventbrite-slider"></div>
                                    </div>
                                    <p class="description"><?php esc_html_e( 'Check this box to always respect Eventbrite timezone. WordPress timezone settings will be ignored.', 'event-feed-for-eventbrite' ); ?></p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Address Format -->
                <div class="heading-row">
                    <h2><?php esc_html_e( 'Address Format', 'event-feed-for-eventbrite'); ?></h2>
                    <p><?php esc_html_e( 'Control how you want to display the address for your events.', 'event-feed-for-eventbrite' ); ?></p>
                </div>

                <div class="field-row">
                    <table class="form-table">

                        <tbody>
                            <tr valign="top">
                                <th scope="row">
                                    <label for="<?php echo esc_attr( $settings_name . '-address_format' ); ?>">
                                        <?php esc_html_e( 'Address', 'event-feed-for-eventbrite' ); ?>
                                    </label>
                                </th>
                                <td>
                                    <input type="text" id="<?php echo esc_attr( $settings_name . '-address_format' ); ?>" name="<?php echo esc_attr( $settings_name . '[address_format]' ); ?>" value="<?php echo esc_attr( $address_format ); ?>">
                                    <p class="description"><?php esc_html_e( 'If you want to use a different address format, you can define it with the variables below. See examples to understand what information you get from each variable. You can use any separator between variables or add any information before or after the address as long as you keep variables enclosed in brackets. HTML is allowed.', 'event-feed-for-eventbrite' ); ?></p>
                                    <p class="address-help">
                                        <strong><?php esc_html_e( 'Variables:', 'event-feed-for-eventbrite' ); ?></strong>
                                        <span>
                                            <span><code><?php echo esc_html( '[venue_name]' ); ?></code></span>
                                            <span><?php echo esc_html( 'Art Attelier' ); ?></span>
                                        </span>
                                        <span>
                                            <span><code><?php echo esc_html( '[localized_address_display]' ); ?></code></span>
                                            <span><?php echo esc_html( "333 O'Farrell St Suite 400, San Francisco, CA 94102" ); ?></span>
                                        </span>
                                        <span>
                                            <span><code><?php echo esc_html(  '[localized_area_display]' ); ?></code></span>
                                            <span><?php echo esc_html( 'San Francisco, CA' ); ?></span>
                                        </span>
                                        <span>
                                            <span><code><?php echo esc_html(  '[address_1]' ); ?></code></span>
                                            <span><?php echo esc_html( "333 O'Farrell St" ); ?></span>
                                        </span>
                                        <span>
                                            <span><code><?php echo esc_html(  '[address_2]' ); ?></code></span>
                                            <span><?php echo esc_html( 'Suite 400' ); ?></span>
                                        </span>
                                        <span>
                                            <span><code><?php echo esc_html(  '[city]' ); ?></code></span>
                                            <span><?php echo esc_html( 'San Francisco' ); ?></span>
                                        </span>
                                        <span> 
                                            <span><code><?php echo esc_html(  '[region]' ); ?></code></span>
                                            <span><?php echo esc_html( 'CA' ); ?></span>
                                        </span>
                                        <span>
                                            <span><code><?php echo esc_html(  '[postal_code]' ); ?></code></span>
                                            <span><?php echo esc_html( '94102' ); ?></span>
                                        </span>
                                        <span> 
                                            <span><code><?php echo esc_html(  '[country]' ); ?></code></span>
                                            <span><?php echo esc_html( 'US' ); ?></span>
                                        </span>
                                        <span>
                                            <span><code><?php echo esc_html(  '[latitude]' ); ?></code></span>
                                            <span><?php echo esc_html( '37.7576792' ); ?></span>
                                        </span>
                                        <span>
                                            <span><code><?php echo esc_html(  '[longitude]' ); ?></code></span>
                                            <span><?php echo esc_html( '-122.5078119' ); ?></span>
                                        </span>
                                    </p>    
                                </td>
                            </tr>
                        </tbody>

                    </table>
                </div>

            </div>

            <?php

        // General tab
        } elseif( $tab === 'advanced' ) {

            // Get options
            $options_default = 'a:3:{s:27:"hide_delete_cache_admin_bar";b:0;s:11:"delete_data";b:0;s:14:"show_copyright";b:0;}';
            $options = get_option( $this->plugin_name . '-advanced', $options_default );

            // Cleanup
            if( $options !== false ) {
                $hide_delete_cache_admin_bar = isset( $options['hide_delete_cache_admin_bar'] ) ? boolval( $options['hide_delete_cache_admin_bar'] ) : false;
                $delete_data = isset( $options['delete_data'] ) ? boolval( $options['delete_data'] ) : false;
                $show_copyright = isset( $options['show_copyright'] ) ? boolval( $options['show_copyright'] ) : false;
            } else {
                $hide_delete_cache_admin_bar = '';
                $delete_data = '';
                $show_copyright = '';
            }

            // Settings name
            $settings_name = $this->plugin_name . '-advanced';

            settings_fields( $settings_name );
            do_settings_sections( $settings_name );

            ?>

            <div>

                <!-- Misc options -->
                <div class="heading-row">
                    <h2><?php esc_html_e( 'Misc options', 'event-feed-for-eventbrite' ); ?></h2>
                </div>

                <!-- Show us some love -->
                <div class="field-row">
                    <table class="form-table">
                        <tbody>
                            <tr valign="top">
                                <th scope="row">
                                    <label class="event-feed-for-eventbrite-switch-label" for="<?php echo esc_attr( $settings_name . '-show_copyright' ); ?>">
                                        <?php esc_html_e( 'Show us some love', 'event-feed-for-eventbrite'); ?>
                                    </label>
                                </th>
                                <td>
                                    <input class="event-feed-for-eventbrite-switch-input" type="checkbox" id="<?php echo esc_attr( $settings_name . '-show_copyright' ); ?>" name="<?php echo esc_attr( $settings_name . '[show_copyright]' ); ?>" <?php if( $show_copyright === true ) echo 'checked'; ?>>
                                    <div class="event-feed-for-eventbrite-switch <?php if( $show_copyright === true ) echo '-on'; ?>">
                                        <span class="event-feed-for-eventbrite-switch-on"><?php esc_html_e( 'Yes', 'event-feed-for-eventbrite' ); ?></span>
                                        <span class="event-feed-for-eventbrite-switch-off"><?php esc_html_e( 'No', 'event-feed-for-eventbrite' ); ?></span>
                                        <div class="event-feed-for-eventbrite-slider"></div>
                                    </div>
                                    <p class="description"><?php esc_html_e( 'Support our work and show "Powered by" text under your event feeds.', 'event-feed-for-eventbrite'); ?></p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Hide delete cache button -->
                <div class="field-row">
                    <table class="form-table">
                        <tbody>
                            <tr valign="top">
                                <th scope="row">
                                    <label class="event-feed-for-eventbrite-switch-label" for="<?php echo esc_attr( $settings_name . '-hide_delete_cache_admin_bar' ); ?>">
                                        <?php esc_html_e( 'Hide Delete Cache button', 'event-feed-for-eventbrite'); ?>
                                    </label>
                                </th>
                                <td>
                                    <input class="event-feed-for-eventbrite-switch-input" type="checkbox" id="<?php echo esc_attr( $settings_name . '-hide_delete_cache_admin_bar' ); ?>" name="<?php echo esc_attr( $settings_name . '[hide_delete_cache_admin_bar]' ); ?>" <?php if( $hide_delete_cache_admin_bar === true ) echo 'checked'; ?>>
                                    <div class="event-feed-for-eventbrite-switch <?php if( $hide_delete_cache_admin_bar === true ) echo '-on'; ?>">
                                        <span class="event-feed-for-eventbrite-switch-on"><?php esc_html_e( 'Yes', 'event-feed-for-eventbrite' ); ?></span>
                                        <span class="event-feed-for-eventbrite-switch-off"><?php esc_html_e( 'No', 'event-feed-for-eventbrite' ); ?></span>
                                        <div class="event-feed-for-eventbrite-slider"></div>
                                    </div>
                                    <p class="description"><?php esc_html_e( 'Hide button in admin bar allowing refreshing of displayed events.', 'event-feed-for-eventbrite'); ?></p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Delete all data -->
                <div class="field-row">
                    <table class="form-table">
                        <tbody>
                            <tr valign="top">
                                <th scope="row">
                                    <label class="event-feed-for-eventbrite-switch-label" for="<?php echo esc_attr( $settings_name . '-delete_data' ); ?>"><?php esc_html_e( 'Delete plugin\'s data', 'event-feed-for-eventbrite' ); ?></label>
                                </th>
                                <td>
                                <input class="event-feed-for-eventbrite-switch-input" type="checkbox" id="<?php echo esc_attr( $settings_name . '-delete_data' ); ?>" name="<?php echo esc_attr( $settings_name . '[delete_data]' ); ?>" <?php if( $delete_data === true ) echo 'checked'; ?>>
                                    <div class="event-feed-for-eventbrite-switch <?php if( $delete_data === true ) echo '-on'; ?>">
                                        <span class="event-feed-for-eventbrite-switch-on"><?php esc_html_e( 'Yes', 'event-feed-for-eventbrite' ); ?></span>
                                        <span class="event-feed-for-eventbrite-switch-off"><?php esc_html_e( 'No', 'event-feed-for-eventbrite' ); ?></span>
                                        <div class="event-feed-for-eventbrite-slider"></div>
                                    </div>
                                    <p class="description"><?php esc_html_e( 'Delete all plugin-related data upon uninstallation of the plugin. All feeds and settings will be unrecoverable.', 'event-feed-for-eventbrite'); ?></p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

            </div>

        <?php
        }

        // Submit button
        $submit_btn_text = esc_html__( 'Save changes', 'event-feed-for-eventbrite' );
        submit_button( $submit_btn_text, 'primary', 'submit', TRUE );
        
        ?>

    </form>

</div>