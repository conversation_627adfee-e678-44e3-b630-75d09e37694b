const { VueLoaderPlugin } = require("vue-loader");
const CopyPlugin = require("copy-webpack-plugin");
const path = require("path");
const BrowserSyncPlugin = require('browser-sync-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');

module.exports = [

  // Admin scripts
  {
    entry: {
      'media-uploader': './admin/src/js/media-uploader.js',
      admin: './admin/src/js/admin.js',
      block: './admin/src/js/block.js'
    },
    output: {
      filename: '[name].js',
      chunkFilename: '[name].js',
      path: path.resolve( __dirname, 'admin/assets/js' )
    },
    module: {
      rules: [
        {
          test: /\.js$/,
          exclude: /node_modules/,
          use: {
            loader: "babel-loader",
          },
        },
      ]
    },
    plugins: [
      new BrowserSyncPlugin({
        proxy: "https://events.test/",
        host: 'events.test',
        port: 8080,
        open: 'external',
        https: {
          key: "/Users/<USER>/Library/Application Support/Herd/config/valet/Certificates/events.test.key",
          cert: "/Users/<USER>/Library/Application Support/Herd/config/valet/Certificates/events.test.crt"
        },
        files: [
          "./**/*.php",
          "./admin/assets/css/**/*.css",
          "./admin/assets/js/**/*.js",
          "./public/assets/css/**/*.css",
          "./public/assets/js/**/*.js",
        ]
      }),
      new CopyPlugin({
        patterns: [

          // Minicolors (admin)
          { from: "./node_modules/@claviska/jquery-minicolors/jquery.minicolors.min.js", to: "../../libs/minicolors" },
          { from: "./node_modules/@claviska/jquery-minicolors/jquery.minicolors.css", to: "../../libs/minicolors" },
          { from: "./node_modules/@claviska/jquery-minicolors/jquery.minicolors.png", to: "../../libs/minicolors" },

          // Choices.js (admin)
          { from: "./node_modules/choices.js/public/assets/scripts/choices.min.js", to: "../../libs/choices" },
          { from: "./node_modules/choices.js/public/assets/styles/choices.min.css", to: "../../libs/choices" },

          // Tooltipster (admin)
          { from: "./node_modules/tooltipster/dist/js/tooltipster.bundle.min.js", to: "../../libs/tooltipster" },
          { from: "./node_modules/tooltipster/dist/css/tooltipster.bundle.min.css", to: "../../libs/tooltipster" },
          { from: "./node_modules/tooltipster/dist/css/plugins/tooltipster/sideTip/themes/tooltipster-sideTip-borderless.min.css", to: "../../libs/tooltipster" },
          
          // Clipboard.js (admin)
          { from: "./node_modules/clipboard/dist/clipboard.min.js", to: "../../libs/clipboard" },

          // Font Awesome (admin)
          { from: "./node_modules/font-awesome/css/font-awesome.min.css", to: "../../libs/font-awesome/css" },
          { from: "./node_modules/font-awesome/fonts", to: "../../libs/font-awesome/fonts" },

          // jQuery confirm (admin)
          { from: "./node_modules/jquery-confirm/dist/jquery-confirm.min.js", to: "../../libs/jquery-confirm" },
          { from: "./node_modules/jquery-confirm/dist/jquery-confirm.min.css", to: "../../libs/jquery-confirm" },

          // JQuery are you sure? (admin)
          { from: "./node_modules/jquery.are-you-sure/jquery.are-you-sure.js", to: "../../libs/jquery-are-you-sure" },

        ],
      }),
    ],
    optimization: {
      minimize: true,
      minimizer: [
        new TerserPlugin({
          terserOptions: {
            format: {
              comments: /^removeIf|^endRemoveIf/i,
            },
          },
          extractComments: true,
        }),
      ],
    },
  },

  // Admin styles
  {
    entry: {
      admin: './admin/src/scss/admin.scss',
      block: './admin/src/scss/block.scss'
    },
    output: {
      path: path.resolve( __dirname, 'admin' )
    },
    module: {
      rules: [
        {
          test: /\.scss$/,
          exclude: /node_modules/,
          use: [
            {
              loader: 'file-loader',
              options: { 
                outputPath: 'assets/css/', name: '[name].css'
              }
            },
            {
              loader: 'postcss-loader',
              options: {
                postcssOptions: {
                  plugins: [
                    [
                      'postcss-preset-env',
                      {
                        autoprefixer: {
                          flexbox: 'no-2009',
                        },
                        stage: 3,
                      },
                    ],
                  ],
                },
              }
            },
            'sass-loader'
          ]
        },
      ]
    },
  },

  // Public scripts
  {
    entry: {
      app: './public/src/js/app.js',
      preview: './public/src/js/preview.js'
    },
    devtool: 'source-map',
    output: {
      filename: '[name].js',
      chunkFilename: '[name].js',
      path: path.resolve( __dirname, 'public/assets/js' ),
      clean: true,
    },
    module: {
      rules: [
        {
          test: /\.js$/,
          exclude: /node_modules\/(?!calendar-link)/,
          use: {
            loader: "babel-loader",
          },
        },
        {
          test: /\.vue$/,
          loader: "vue-loader",
        },
      ]
    },
    plugins: [
      new VueLoaderPlugin(),
      new CopyPlugin({
        patterns: [

          // Vue (public)
          { from: "./node_modules/vue/dist/vue.min.js", to: "../../libs/vue" },

          // Tooltipster (public)
          { from: "./node_modules/tooltipster/dist/js/tooltipster.bundle.min.js", to: "../../libs/tooltipster" },
          { from: "./node_modules/tooltipster/dist/css/tooltipster.bundle.min.css", to: "../../libs/tooltipster" },
          { from: "./node_modules/tooltipster/dist/css/plugins/tooltipster/sideTip/themes/tooltipster-sideTip-borderless.min.css", to: "../../libs/tooltipster" },
          
          // Clipboard.js (public)
          { from: "./node_modules/clipboard/dist/clipboard.min.js", to: "../../libs/clipboard" },
  
        ],
      }),
    ],
    externals: {
      vue: 'Vue'
    },
    optimization: {
      minimize: true,
      minimizer: [
        new TerserPlugin({
          terserOptions: {
            format: {
              comments: /^removeIf|^endRemoveIf/i,
            },
          },
          extractComments: true,
        }),
      ],
    },
  },

  // Public styles
  {
    entry: {
      app: './public/src/scss/app.scss'
    },
    output: {
      publicPath: '',
      path: path.resolve( __dirname, 'public' )
    },
    module: {
      rules: [
        {
          test: /\.scss$/,
          exclude: /node_modules/,
          use: [
            {
              loader: 'file-loader',
              options: { 
                outputPath: 'assets/css/',
                name: '[name].css'
              }
            },
            {
              loader: 'postcss-loader',
              options: {
                postcssOptions: {
                  plugins: [
                    [
                      "postcss-preset-env",
                      {
                        autoprefixer: {
                          flexbox: 'no-2009',
                        },
                        stage: 3,
                      },
                    ],
                  ],
                },
              }
            },
            'sass-loader'
          ]
        },
      ]
    },
    plugins: [
      new CopyPlugin({
        patterns: [

          // Vue modal (public)
          { from: "./node_modules/@kouts/vue-modal/dist/vue-modal.css", to: "../../libs/vue-modal" },
  
        ],
      }),
    ],
  },

]