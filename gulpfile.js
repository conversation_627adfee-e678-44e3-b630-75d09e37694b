// --------------------------------------------------
// DEVELOPMENT MODULES
// --------------------------------------------------

var gulp        = require("gulp"),
    removeCode  = require('gulp-remove-code'),
    replace     = require('gulp-replace'),
    del         = require('del'),
    wpPot       = require('gulp-wp-pot');

// --------------------------------------------------
// CLEAN DIST FOLDER
// --------------------------------------------------

gulp.task("clean", function() {
    return del(["./dist"]);
})

// --------------------------------------------------
// PRO VERSION BUNDLE
// --------------------------------------------------

// Admin folder
gulp.task("pro-admin", function() {
    return (
        gulp
            .src([
                "./admin/**/*"
            ])
            .pipe(gulp.dest("./dist/pro/admin"))
    );
})

// Freemius folder
gulp.task("pro-freemius", function() {
    return (
        gulp
            .src([
                "./freemius/**/*"
            ])
            .pipe(gulp.dest("./dist/pro/freemius"))
    );
})

// Includes folder
gulp.task("pro-includes", function() {
    return (
        gulp
            .src([
                "./includes/**/*"
            ])
            .pipe(removeCode({ premium: true }))
            .pipe(replace('//removeIf(!premium)', ''))
            .pipe(replace('//endRemoveIf(!premium)', ''))
            .pipe(gulp.dest("./dist/pro/includes"))
    );
})

// Public folder
gulp.task("pro-public", function() {
    return (
        gulp
            .src([
                "./public/**/*"
            ])
            .pipe(removeCode({ premium: true }))
            .pipe(replace('//removeIf(!premium)', ''))
            .pipe(replace('//endRemoveIf(!premium)', ''))
            .pipe(gulp.dest("./dist/pro/public"))
    );
})

// Delete licence comments from js files
gulp.task("pro-deleteCommentsJs", function() {
    return (
        gulp
            .src([
                "./public/assets/js/*.js"
            ])
            .pipe(replace('//removeIf(!premium)', ''))
            .pipe(replace('//endRemoveIf(!premium)', ''))
            .pipe(gulp.dest("./dist/pro/public/assets/js"))
    );
})

// Root files
gulp.task("pro-rootFiles", function() {
    return (
        gulp
            .src([
                "./event-feed-for-eventbrite.php",
                "./index.php",
                "./wpml-config.xml",
                "./LICENSE.txt",
                "./README.txt"
            ])
            .pipe(gulp.dest("./dist/pro"))
    );
})

// Delete source and obsolete files
gulp.task("pro-deleteSourceFiles", function() {
    return del([
        "./dist/pro/admin/*.js",
        "./dist/pro/admin/src",
        "./dist/pro/public/*.js",
        "./dist/pro/public/src",
        "./dist/pro/admin/assets/js/libs"
    ]);
})

// Remove Freemius secret key
gulp.task("pro-stripCode", function() {
    return (
        gulp
            .src('./event-feed-for-eventbrite.php')
            .pipe(removeCode({ premium: true }))
            .pipe(removeCode({ production: true }))
            .pipe(replace('//removeIf(!premium)', ''))
            .pipe(replace('//endRemoveIf(!premium)', ''))
            .pipe(replace('//removeIf(!production)', ''))
            .pipe(replace('//endRemoveIf(!production)', ''))
            .pipe(gulp.dest('./dist/pro'))
    );
})

// Copy placeholder
gulp.task("pro-placeholder", function() {
    return (
        gulp
            .src("./public/assets/img/placeholder.png")
            .pipe(gulp.dest('./dist/pro/public/assets/img'))
    );
})

// --------------------------------------------------
// FREE VERSION BUNDLE
// --------------------------------------------------

// Admin folder
gulp.task("free-admin", function() {
    return (
        gulp
            .src([
                "./admin/**/*"
            ])
            .pipe(gulp.dest("./dist/free/admin"))
    );
})

// Freemius folder
gulp.task("free-freemius", function() {
    return (
        gulp
            .src([
                "./freemius/**/*"
            ])
            .pipe(gulp.dest("./dist/free/freemius"))
    );
})

// Includes folder
gulp.task("free-includes", function() {
    return (
        gulp
            .src([
                "./includes/**/*"
            ])
            .pipe(removeCode({ premium: false }))
            .pipe(replace('//removeIf(premium)', ''))
            .pipe(replace('//endRemoveIf(premium)', ''))
            .pipe(gulp.dest("./dist/free/includes"))
    );
})

// Public folder
gulp.task("free-public", function() {
    return (
        gulp
            .src([
                "./public/**/*"
            ])
            .pipe(removeCode({ premium: false }))
            .pipe(gulp.dest("./dist/free/public"))
    );
})

// Delete licence comments from js files
gulp.task("free-deleteCommentsJs", function() {
    return (
        gulp
            .src([
                "./public/assets/js/*.js"
            ])
            .pipe(replace('//removeIf(!premium)', ''))
            .pipe(replace('//endRemoveIf(!premium)', ''))
            .pipe(gulp.dest("./dist/free/public/assets/js"))
    );
})

// Root files
gulp.task("free-rootFiles", function() {
    return (
        gulp
            .src([
                "./event-feed-for-eventbrite.php",
                "./index.php",
                "./wpml-config.xml",
                "./LICENSE.txt",
                "./README.txt"
            ])
            .pipe(gulp.dest("./dist/free"))
    );
})

// Delete source and obsolete files
gulp.task("free-deleteSourceFiles", function() {
    return del([
        "./dist/free/admin/*.js",
        "./dist/free/admin/src",
        "./dist/free/public/*.js",
        "./dist/free/public/src",
        "./dist/free/admin/assets/js/libs",
        "./dist/free/public/assets/js/cards.js",
        "./dist/free/public/assets/js/cards.js.map",
        "./dist/free/public/assets/js/grid.js",
        "./dist/free/public/assets/js/grid.js.map",
    ]);
})

// Remove Freemius secret key
gulp.task("free-stripCode", function() {
    return (
        gulp
            .src('./event-feed-for-eventbrite.php')
            .pipe(removeCode({ premium: false }))
            .pipe(removeCode({ production: true }))
            .pipe(replace('//removeIf(premium)', ''))
            .pipe(replace('//endRemoveIf(premium)', ''))
            .pipe(replace('//removeIf(!production)', ''))
            .pipe(replace('//endRemoveIf(!production)', ''))
            .pipe(gulp.dest('./dist/free'))
    );
})

// Copy placeholder
gulp.task("free-placeholder", function() {
    return (
        gulp
            .src("./public/assets/img/placeholder.png")
            .pipe(gulp.dest('./dist/free/public/assets/img'))
    );
})

// --------------------------------------------------
// GENERATE POT FILES
// --------------------------------------------------
gulp.task('translation', function () {
    return gulp.src(['./**/*.{php,txt}', '!./freemius/**/*.{php,txt}', '!./dist/**/*.{php,txt}'])
        .pipe(wpPot({
            domain: 'event-feed-for-eventbrite',
            package: 'Event Feed for Eventbrite',
            team: 'Bohemia Plugins <<EMAIL>>',
            lastTranslator: 'Bohemia Plugins <<EMAIL>>',
            metadataFile: 'event-feed-for-eventbrite.php'
        }))
        .pipe(gulp.dest('./dist/pro/languages/event-feed-for-eventbrite.pot'))
        .pipe(gulp.dest('./dist/free/languages/event-feed-for-eventbrite.pot'));
});

// --------------------------------------------------
// BUNDLING
// --------------------------------------------------

// Create bundle
gulp.task(
    "bundle", 
    gulp.series(
        "clean",
        gulp.parallel(
            "pro-admin",
            "pro-freemius",
            "pro-includes",
            "pro-public",
            "pro-rootFiles",
        ),
        gulp.parallel(
            "pro-stripCode",
            "pro-deleteCommentsJs",
            "pro-deleteSourceFiles",
            "pro-placeholder"
        ),
        gulp.parallel(
            "free-admin",
            "free-freemius",
            "free-includes",
            "free-public",
            "free-rootFiles",
        ),
        gulp.parallel(
            "free-stripCode",
            "free-deleteCommentsJs",
            "free-deleteSourceFiles",
            "free-placeholder"
        ),
        "translation"
    )
)
